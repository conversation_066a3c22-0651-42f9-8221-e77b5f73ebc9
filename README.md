# AI Chat Application - 多模态AI聊天应用

一个支持文本、图像和语音的多模态AI聊天应用，使用Rust后端和Flutter前端开发。

## 🎉 最新更新

**✅ 多模态聊天功能已完全实现并可用！**
- 支持文本+图片混合消息
- OpenAI兼容的API格式
- 智能模型选择（文本/视觉）
- 流式和非流式响应
- 完整的错误处理和恢复机制

## 项目结构

```
ai_chat/
├── backend/                 # Rust后端服务器
│   ├── src/
│   ├── Cargo.toml
│   └── ...
├── frontend/               # Flutter前端应用
│   ├── lib/
│   ├── pubspec.yaml
│   └── ...
├── docs/                   # 项目文档
├── config/                 # 配置文件
└── README.md
```

## 功能特性

### 核心功能
- 📝 **文本对话**：支持与AI进行文本聊天 ✅ **已实现**
- 🖼️ **图像分析**：上传图片并获得AI分析和描述 ✅ **已实现**
- 🎯 **多模态聊天**：文本+图片混合消息 ✅ **已实现**
- 🌊 **流式响应**：实时流式消息传输 ✅ **已实现**
- 🎤 语音交互：语音输入（STT）和语音输出（TTS） 🔄 **开发中**
- 💬 实时聊天：实时聊天界面和对话历史记录 ✅ **已实现**

### 技术特性
- 🦀 **后端**：Rust + Axum Web框架 ✅ **已实现**
- 📱 **前端**：Flutter跨平台应用 ✅ **已实现**
- 🤖 **AI服务**：通义千问API集成（qwen-turbo + qwen-vl-plus）✅ **已实现**
- 🔗 **API格式**：OpenAI兼容格式 ✅ **已实现**
- 🌊 **流式响应**：SSE + 智能降级 ✅ **已实现**
- 🗄️ **数据库**：内存存储（开发）/ SQLite（生产）🔄 **开发中**
- 🔐 **认证**：JWT令牌认证 🔄 **开发中**
- 📁 **存储**：文件上传和存储系统 🔄 **开发中**

## 快速开始

### 环境要求
- Rust 1.70+
- Flutter 3.0+
- 通义千问API密钥

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd ai_chat
```

2. **后端设置**
```bash
cd backend
# 构建项目
cargo build
# 启动服务器（默认运行配置已设置）
cargo run
# 服务器将在 http://127.0.0.1:8080 启动
```

3. **前端设置**
```bash
cd frontend
flutter pub get
flutter run
```

### 配置

复制配置模板并填入你的API密钥：
```bash
cp config/config.example.toml config/config.toml
# 编辑 config/config.toml 文件，填入你的通义千问API密钥
```

## 🚀 快速测试

启动后端服务后，可以立即测试多模态聊天功能：

### 1. 测试文本聊天
```bash
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "你好，请介绍一下你自己", "stream": false}' \
  -w "\nHTTP Status: %{http_code}\n"
```

### 2. 测试多模态聊天（文本+图片）
```bash
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": [
      {"type": "text", "text": "请分析这张图片"},
      {"type": "image_url", "image_url": {"url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"}}
    ],
    "stream": false
  }' \
  -w "\nHTTP Status: %{http_code}\n"
```

### 3. 测试流式响应
```bash
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "请写一首关于春天的诗", "stream": true}'
```

### 4. 健康检查
```bash
# 基础健康检查
curl http://127.0.0.1:8080/api/health

# AI服务健康检查
curl http://127.0.0.1:8080/api/health/ai

# 完整健康检查
curl http://127.0.0.1:8080/api/health/full
```

## API文档

详细的API文档请参考 [docs/api.md](docs/api.md)

## 开发指南

- [后端开发指南](docs/backend.md)
- [前端开发指南](docs/frontend.md)
- [部署指南](docs/deployment.md)

## 许可证

MIT License
