// Flutter旅行规划表集成组件
// 与AI聊天后端和高德地图MCP服务集成

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:convert';
import 'dart:io';

/// 旅行规划数据模型 - 兼容MCP服务格式
class TravelPlanData {
  final String destination;
  final TravelDates dates;
  final WeatherInfo weather;
  final List<ItineraryItem> itinerary;
  final List<TransportationItem> transportation;
  final AccommodationInfo accommodation;
  final BudgetInfo budget;

  TravelPlanData({
    required this.destination,
    required this.dates,
    required this.weather,
    required this.itinerary,
    required this.transportation,
    required this.accommodation,
    required this.budget,
  });

  /// 从MCP服务响应转换
  factory TravelPlanData.fromMcpResponse(Map<String, dynamic> mcpData) {
    return TravelPlanData(
      destination: mcpData['destination'] ?? '未知目的地',
      dates: TravelDates.fromJson(mcpData['dates'] ?? {}),
      weather: WeatherInfo.fromJson(mcpData['weather'] ?? {}),
      itinerary: (mcpData['itinerary'] as List? ?? [])
          .map((item) => ItineraryItem.fromJson(item))
          .toList(),
      transportation: (mcpData['transportation'] as List? ?? [])
          .map((item) => TransportationItem.fromJson(item))
          .toList(),
      accommodation: AccommodationInfo.fromJson(mcpData['accommodation'] ?? {}),
      budget: BudgetInfo.fromJson(mcpData['budget'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'destination': destination,
      'dates': dates.toJson(),
      'weather': weather.toJson(),
      'itinerary': itinerary.map((item) => item.toJson()).toList(),
      'transportation': transportation.map((item) => item.toJson()).toList(),
      'accommodation': accommodation.toJson(),
      'budget': budget.toJson(),
    };
  }
}

class TravelDates {
  final String start;
  final String end;

  TravelDates({required this.start, required this.end});

  factory TravelDates.fromJson(Map<String, dynamic> json) {
    return TravelDates(
      start: json['start'] ?? '',
      end: json['end'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {'start': start, 'end': end};
}

class WeatherInfo {
  final String temperature;
  final String condition;
  final String wind;
  final String icon;

  WeatherInfo({
    required this.temperature,
    required this.condition,
    required this.wind,
    required this.icon,
  });

  factory WeatherInfo.fromJson(Map<String, dynamic> json) {
    return WeatherInfo(
      temperature: json['temperature'] ?? '',
      condition: json['condition'] ?? '',
      wind: json['wind'] ?? '',
      icon: json['icon'] ?? 'fa-cloud',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'temperature': temperature,
      'condition': condition,
      'wind': wind,
      'icon': icon,
    };
  }
}

class ItineraryItem {
  final String time;
  final String activity;
  final String location;
  final String type;
  final String price;
  final String? notes;

  ItineraryItem({
    required this.time,
    required this.activity,
    required this.location,
    required this.type,
    required this.price,
    this.notes,
  });

  factory ItineraryItem.fromJson(Map<String, dynamic> json) {
    return ItineraryItem(
      time: json['time'] ?? '',
      activity: json['activity'] ?? '',
      location: json['location'] ?? '',
      type: json['type'] ?? 'attraction',
      price: json['price'] ?? '',
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'time': time,
      'activity': activity,
      'location': location,
      'type': type,
      'price': price,
      'notes': notes,
    };
  }
}

class TransportationItem {
  final String from;
  final String to;
  final String method;
  final String duration;
  final String cost;

  TransportationItem({
    required this.from,
    required this.to,
    required this.method,
    required this.duration,
    required this.cost,
  });

  factory TransportationItem.fromJson(Map<String, dynamic> json) {
    return TransportationItem(
      from: json['from'] ?? '',
      to: json['to'] ?? '',
      method: json['method'] ?? '',
      duration: json['duration'] ?? '',
      cost: json['cost'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'from': from,
      'to': to,
      'method': method,
      'duration': duration,
      'cost': cost,
    };
  }
}

class AccommodationInfo {
  final String name;
  final String address;
  final String checkin;
  final String checkout;

  AccommodationInfo({
    required this.name,
    required this.address,
    required this.checkin,
    required this.checkout,
  });

  factory AccommodationInfo.fromJson(Map<String, dynamic> json) {
    return AccommodationInfo(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      checkin: json['checkin'] ?? '',
      checkout: json['checkout'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'checkin': checkin,
      'checkout': checkout,
    };
  }
}

class BudgetInfo {
  final int total;
  final Map<String, int> breakdown;

  BudgetInfo({required this.total, required this.breakdown});

  factory BudgetInfo.fromJson(Map<String, dynamic> json) {
    final breakdown = <String, int>{};
    if (json['breakdown'] is Map) {
      json['breakdown'].forEach((key, value) {
        breakdown[key] = value is int ? value : int.tryParse(value.toString()) ?? 0;
      });
    }

    return BudgetInfo(
      total: json['total'] is int ? json['total'] : int.tryParse(json['total'].toString()) ?? 0,
      breakdown: breakdown,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'breakdown': breakdown,
    };
  }
}

/// 旅行规划表Widget
class TravelPlannerWidget extends StatefulWidget {
  final String? conversationId;
  final TravelPlanData? initialData;
  final String backendUrl;

  const TravelPlannerWidget({
    Key? key,
    this.conversationId,
    this.initialData,
    this.backendUrl = 'http://127.0.0.1:8080',
  }) : super(key: key);

  @override
  State<TravelPlannerWidget> createState() => _TravelPlannerWidgetState();
}

class _TravelPlannerWidgetState extends State<TravelPlannerWidget> {
  late WebViewController _webViewController;
  TravelPlanData? _travelData;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _loadTravelData();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _injectTravelData();
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: (JavaScriptMessage message) {
          _handleWebViewMessage(message.message);
        },
      );
  }

  Future<void> _loadTravelData() async {
    try {
      if (widget.initialData != null) {
        _travelData = widget.initialData;
      } else if (widget.conversationId != null) {
        _travelData = await _fetchTravelDataFromBackend(widget.conversationId!);
      } else {
        _travelData = _getDefaultTravelData();
      }

      await _loadHtmlTemplate();
    } catch (e) {
      setState(() {
        _error = '加载旅行数据失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<TravelPlanData> _fetchTravelDataFromBackend(String conversationId) async {
    // 这里集成您的AI聊天后端API
    // 实际实现中应该调用您的Rust+Axum后端
    
    // 模拟API调用
    await Future.delayed(const Duration(seconds: 1));
    
    // 返回示例数据，实际应该从后端获取
    return _getDefaultTravelData();
  }

  TravelPlanData _getDefaultTravelData() {
    return TravelPlanData(
      destination: '上海一日游规划表',
      dates: TravelDates(start: '2025-03-30', end: '2025-03-30'),
      weather: WeatherInfo(
        temperature: '13°C/7°C',
        condition: '阴',
        wind: '东风1-3级',
        icon: 'fa-cloud',
      ),
      itinerary: [
        ItineraryItem(
          time: '09:00-11:00',
          activity: '游览豫园',
          location: '福佑路168号',
          type: 'attraction',
          price: '40元',
          notes: '提前预订门票',
        ),
        ItineraryItem(
          time: '11:30-12:30',
          activity: '南翔小笼包',
          location: '豫园商城内',
          type: 'dining',
          price: '60元',
          notes: '排队较长',
        ),
        ItineraryItem(
          time: '13:00-15:00',
          activity: '外滩观光',
          location: '中山东一路',
          type: 'attraction',
          price: '免费',
          notes: '最佳拍照时间',
        ),
      ],
      transportation: [
        TransportationItem(
          from: '豫园',
          to: '外滩',
          method: '步行',
          duration: '15分钟',
          cost: '0元',
        ),
      ],
      accommodation: AccommodationInfo(
        name: '上海外滩酒店',
        address: '黄浦区中山东一路500号',
        checkin: '15:00',
        checkout: '12:00',
      ),
      budget: BudgetInfo(
        total: 500,
        breakdown: {
          'transport': 50,
          'food': 200,
          'attractions': 150,
          'shopping': 100,
        },
      ),
    );
  }

  Future<void> _loadHtmlTemplate() async {
    try {
      // 读取HTML模板文件
      final String htmlContent = await rootBundle.loadString('assets/travel_planner_template.html');
      
      // 将HTML内容加载到WebView
      await _webViewController.loadHtmlString(htmlContent);
    } catch (e) {
      setState(() {
        _error = '加载HTML模板失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _injectTravelData() async {
    if (_travelData != null) {
      final String jsonData = jsonEncode(_travelData!.toJson());
      final String script = '''
        if (typeof renderTravelPlan === 'function') {
          renderTravelPlan($jsonData);
        }
      ''';
      
      await _webViewController.runJavaScript(script);
    }
  }

  void _handleWebViewMessage(String message) {
    try {
      final Map<String, dynamic> data = jsonDecode(message);
      final String action = data['action'] ?? '';
      
      switch (action) {
        case 'print':
          _handlePrint();
          break;
        case 'share':
          _handleShare();
          break;
        case 'edit':
          _handleEdit();
          break;
      }
    } catch (e) {
      debugPrint('处理WebView消息失败: $e');
    }
  }

  void _handlePrint() {
    // 触发打印功能
    _webViewController.runJavaScript('window.print();');
  }

  Future<void> _handleShare() async {
    try {
      if (_travelData != null) {
        final String shareText = '''
${_travelData!.destination}
日期: ${_travelData!.dates.start}
预算: ¥${_travelData!.budget.total}
景点数量: ${_travelData!.itinerary.length}个

通过AI智能规划生成，查看详细规划表请访问应用。
        ''';
        
        await Share.share(shareText, subject: '我的旅行规划');
      }
    } catch (e) {
      _showErrorSnackBar('分享失败: $e');
    }
  }

  void _handleEdit() {
    // 显示编辑对话框或导航到编辑页面
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑规划'),
        content: const Text('编辑功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('旅行规划表'),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _handleShare,
            tooltip: '分享规划',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _handlePrint,
            tooltip: '打印规划表',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1976D2)),
            ),
            SizedBox(height: 16),
            Text('正在加载旅行规划表...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _loadTravelData();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return WebViewWidget(controller: _webViewController);
  }
}

/// 使用示例
class TravelPlannerScreen extends StatelessWidget {
  final String? conversationId;

  const TravelPlannerScreen({Key? key, this.conversationId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TravelPlannerWidget(
      conversationId: conversationId,
      backendUrl: 'http://127.0.0.1:8080', // 您的后端URL
    );
  }
}
