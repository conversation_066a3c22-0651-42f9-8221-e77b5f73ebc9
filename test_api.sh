#!/bin/bash

echo "Testing /api/chat/multimodal endpoint..."

# Test 1: Simple text message (non-streaming)
echo "Test 1: Simple text message (non-streaming)"
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "你是谁？", "stream": false}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# Test 2: Simple text message (streaming)
echo "Test 2: Simple text message (streaming)"
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "你是谁？", "stream": true}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# Test 3: Multimodal message (text + image)
echo "Test 3: Multimodal message (text + image)"
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": [
      {"type": "text", "text": "请分析这张图片"},
      {"type": "image_url", "image_url": {"url": "https://example.com/test.jpg"}}
    ],
    "stream": false
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo "Tests completed!"
