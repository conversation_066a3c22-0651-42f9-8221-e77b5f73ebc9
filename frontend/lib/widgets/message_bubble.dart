// 消息气泡组件 - 显示单条聊天消息
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/message.dart';

/// 消息气泡组件
class MessageBubble extends StatelessWidget {
  /// 消息数据
  final Message message;
  
  /// 是否显示头像
  final bool showAvatar;
  
  /// 是否显示时间
  final bool showTime;
  
  /// 点击消息回调
  final VoidCallback? onTap;
  
  /// 长按消息回调
  final VoidCallback? onLongPress;

  const MessageBubble({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showTime = true,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.isUser;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 助手头像（左侧）
          if (!isUser && showAvatar) _buildAvatar(context, false),
          
          // 消息内容
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              child: Column(
                crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  // 消息气泡
                  GestureDetector(
                    onTap: onTap,
                    onLongPress: onLongPress,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: isUser 
                            ? theme.colorScheme.primary
                            : theme.colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.circular(18).copyWith(
                          bottomRight: isUser ? const Radius.circular(4) : null,
                          bottomLeft: !isUser ? const Radius.circular(4) : null,
                        ),
                      ),
                      child: _buildMessageContent(context),
                    ),
                  ),
                  
                  // 时间戳
                  if (showTime)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        _formatTime(message.createdAt),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          
          // 用户头像（右侧）
          if (isUser && showAvatar) _buildAvatar(context, true),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(BuildContext context, bool isUser) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.only(
        left: isUser ? 8 : 0,
        right: isUser ? 0 : 8,
      ),
      child: CircleAvatar(
        radius: 16,
        backgroundColor: isUser 
            ? theme.colorScheme.primary
            : theme.colorScheme.secondary,
        child: Icon(
          isUser ? Icons.person : Icons.smart_toy,
          size: 18,
          color: isUser 
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSecondary,
        ),
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.isUser;
    
    switch (message.type) {
      case MessageType.text:
        return _buildTextContent(context, isUser);
      case MessageType.image:
        return _buildImageContent(context, isUser);
      case MessageType.audio:
        return _buildAudioContent(context, isUser);
      case MessageType.file:
        return _buildFileContent(context, isUser);
    }
  }

  /// 构建文本内容
  Widget _buildTextContent(BuildContext context, bool isUser) {
    final theme = Theme.of(context);
    
    return SelectableText(
      message.content,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: isUser 
            ? theme.colorScheme.onPrimary
            : theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// 构建图片内容
  Widget _buildImageContent(BuildContext context, bool isUser) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片
        if (message.fileUrl != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedNetworkImage(
              imageUrl: message.fileUrl!,
              width: 200,
              height: 200,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 200,
                height: 200,
                color: theme.colorScheme.surfaceVariant,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                width: 200,
                height: 200,
                color: theme.colorScheme.errorContainer,
                child: Icon(
                  Icons.error,
                  color: theme.colorScheme.onErrorContainer,
                ),
              ),
            ),
          ),
        
        // 图片描述
        if (message.content.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              message.content,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isUser 
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建音频内容
  Widget _buildAudioContent(BuildContext context, bool isUser) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.audiotrack,
          color: isUser 
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Text(
          '音频消息',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isUser 
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// 构建文件内容
  Widget _buildFileContent(BuildContext context, bool isUser) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.attach_file,
          color: isUser 
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Flexible(
          child: Text(
            message.content.isNotEmpty ? message.content : '文件',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isUser 
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurfaceVariant,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 格式化时间
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
