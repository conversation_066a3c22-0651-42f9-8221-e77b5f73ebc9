// 聊天输入组件 - 处理用户输入和发送消息
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';

/// 聊天输入组件
class ChatInput extends StatefulWidget {
  /// 发送文本消息回调
  final Function(String) onSendText;

  /// 发送图片消息回调
  final Function(File, String?) onSendImage;

  /// 是否正在发送消息
  final bool isSending;

  /// 是否启用输入
  final bool enabled;

  const ChatInput({
    super.key,
    required this.onSendText,
    required this.onSendImage,
    this.isSending = false,
    this.enabled = true,
  });

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isMultiline = false;
  bool _hasText = false;

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // 附件按钮
            _buildAttachmentButton(context),

            const SizedBox(width: 8),

            // 输入框
            Expanded(child: _buildInputField(context)),

            const SizedBox(width: 8),

            // 发送按钮
            _buildSendButton(context),
          ],
        ),
      ),
    );
  }

  /// 构建附件按钮
  Widget _buildAttachmentButton(BuildContext context) {
    final theme = Theme.of(context);

    return IconButton(
      onPressed:
          widget.enabled && !widget.isSending ? _showAttachmentOptions : null,
      icon: Icon(
        Icons.add,
        color:
            widget.enabled && !widget.isSending
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
      ),
      style: IconButton.styleFrom(
        backgroundColor: theme.colorScheme.surfaceVariant,
        shape: const CircleBorder(),
      ),
    );
  }

  /// 构建输入框
  Widget _buildInputField(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      constraints: const BoxConstraints(minHeight: 40, maxHeight: 120),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(20),
      ),
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: _handleKeyEvent,
        child: TextField(
          controller: _textController,
          focusNode: _focusNode,
          enabled: widget.enabled && !widget.isSending,
          maxLines: null,
          textInputAction:
              _isMultiline ? TextInputAction.newline : TextInputAction.send,
          decoration: InputDecoration(
            hintText:
                _isMultiline
                    ? '输入消息... (Shift+Enter换行，Enter发送)'
                    : '输入消息... (Enter发送)',
            hintStyle: TextStyle(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 10,
            ),
          ),
          style: TextStyle(color: theme.colorScheme.onSurfaceVariant),
          onChanged: _onTextChanged,
          onSubmitted: _onSubmitted,
        ),
      ),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton(BuildContext context) {
    final theme = Theme.of(context);

    return IconButton(
      onPressed:
          _hasText && widget.enabled && !widget.isSending
              ? _sendTextMessage
              : null,
      icon:
          widget.isSending
              ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: theme.colorScheme.primary,
                ),
              )
              : Icon(
                Icons.send,
                color:
                    _hasText && widget.enabled
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
              ),
      style: IconButton.styleFrom(
        backgroundColor:
            _hasText && widget.enabled && !widget.isSending
                ? theme.colorScheme.primary
                : theme.colorScheme.surfaceVariant,
        foregroundColor:
            _hasText && widget.enabled && !widget.isSending
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onSurfaceVariant,
        shape: const CircleBorder(),
      ),
    );
  }

  /// 键盘事件处理
  void _handleKeyEvent(KeyEvent event) {
    // 只处理按键按下事件
    if (event is! KeyDownEvent) return;

    // 检查是否为Enter键
    if (event.logicalKey == LogicalKeyboardKey.enter) {
      // 检查是否按住Shift键
      final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

      if (isShiftPressed || _isMultiline) {
        // Shift+Enter 或多行模式：允许换行，不发送消息
        return;
      } else {
        // 单独Enter键且非多行模式：发送消息
        if (_hasText) {
          _sendTextMessage();
        }
      }
    }
  }

  /// 文本变化处理
  void _onTextChanged(String text) {
    // 检查是否有文本内容
    final hasText = text.trim().isNotEmpty;

    // 检查是否为多行：包含换行符或文本过长
    final isMultiline = text.contains('\n') || text.length > 100;

    // 只有状态发生变化时才更新UI
    if (hasText != _hasText || isMultiline != _isMultiline) {
      setState(() {
        _hasText = hasText;
        _isMultiline = isMultiline;
      });
    }
  }

  /// 提交处理（TextField的onSubmitted回调）
  void _onSubmitted(String text) {
    // 只在非多行模式且有文本时发送
    if (!_isMultiline && _hasText) {
      _sendTextMessage();
    }
  }

  /// 发送文本消息
  void _sendTextMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      // 调用回调发送消息
      widget.onSendText(text);

      // 清空输入框并重置状态
      _textController.clear();
      setState(() {
        _hasText = false;
        _isMultiline = false;
      });

      // 重新聚焦到输入框
      _focusNode.requestFocus();
    }
  }

  /// 显示附件选项
  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) =>
              _AttachmentOptionsSheet(onImageSelected: _handleImageSelected),
    );
  }

  /// 处理图片选择
  void _handleImageSelected(File imageFile) {
    // 可以在这里添加图片描述输入对话框
    _showImageDescriptionDialog(imageFile);
  }

  /// 显示图片描述输入对话框
  void _showImageDescriptionDialog(File imageFile) {
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('添加图片描述'),
            content: TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                hintText: '为图片添加描述（可选）',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onSendImage(
                    imageFile,
                    descriptionController.text.trim(),
                  );
                },
                child: const Text('发送'),
              ),
            ],
          ),
    );
  }
}

/// 附件选项底部表单
class _AttachmentOptionsSheet extends StatelessWidget {
  final Function(File) onImageSelected;

  const _AttachmentOptionsSheet({required this.onImageSelected});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 16),

          Text('选择附件', style: theme.textTheme.titleMedium),

          const SizedBox(height: 16),

          // 选项列表
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildOption(
                context,
                icon: Icons.photo_camera,
                label: '拍照',
                onTap: () => _pickImage(context, ImageSource.camera),
              ),
              _buildOption(
                context,
                icon: Icons.photo_library,
                label: '相册',
                onTap: () => _pickImage(context, ImageSource.gallery),
              ),
              _buildOption(
                context,
                icon: Icons.mic,
                label: '语音',
                onTap: () => _showComingSoon(context),
              ),
              _buildOption(
                context,
                icon: Icons.attach_file,
                label: '文件',
                onTap: () => _showComingSoon(context),
              ),
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// 构建选项
  Widget _buildOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.onPrimaryContainer,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(label, style: theme.textTheme.bodySmall),
        ],
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImage(BuildContext context, ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: source);

      if (image != null && context.mounted) {
        Navigator.of(context).pop();
        onImageSelected(File(image.path));
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('选择图片失败: $e')));
      }
    }
  }

  /// 显示即将推出提示
  void _showComingSoon(BuildContext context) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('功能即将推出')));
  }
}
