// 聊天状态管理 - 使用Provider管理聊天相关状态
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/conversation.dart';
import '../models/message.dart';
import '../services/chat_service.dart';

/// 聊天状态管理器
class ChatProvider extends ChangeNotifier {
  final ChatService _chatService = ChatService();

  // 对话相关状态
  List<Conversation> _conversations = [];
  Conversation? _currentConversation;
  bool _isLoadingConversations = false;

  // 消息相关状态
  List<Message> _messages = [];
  bool _isLoadingMessages = false;
  bool _isSendingMessage = false;

  // 错误状态
  String? _errorMessage;

  // 分页状态
  int _currentPage = 1;
  bool _hasMoreMessages = true;

  /// 对话列表
  List<Conversation> get conversations => _conversations;

  /// 当前对话
  Conversation? get currentConversation => _currentConversation;

  /// 是否正在加载对话列表
  bool get isLoadingConversations => _isLoadingConversations;

  /// 当前对话的消息列表
  List<Message> get messages => _messages;

  /// 是否正在加载消息
  bool get isLoadingMessages => _isLoadingMessages;

  /// 是否正在发送消息
  bool get isSendingMessage => _isSendingMessage;

  /// 错误消息
  String? get errorMessage => _errorMessage;

  /// 是否还有更多消息
  bool get hasMoreMessages => _hasMoreMessages;

  /// 加载对话列表
  Future<void> loadConversations({bool refresh = false}) async {
    if (_isLoadingConversations && !refresh) return;

    try {
      _isLoadingConversations = true;
      _clearError();
      notifyListeners();

      final response = await _chatService.getConversations();

      if (response.isSuccess && response.data != null) {
        _conversations = response.data!.conversations;
      } else {
        _setError(response.error?.message ?? '加载对话列表失败');
      }
    } catch (e) {
      print('加载对话列表失败: $e');
      _setError('加载对话列表失败: $e');
    } finally {
      _isLoadingConversations = false;
      notifyListeners();
    }
  }

  /// 创建新对话
  Future<Conversation?> createConversation(String title) async {
    try {
      _clearError();

      final request = CreateConversationRequest(title: title);
      final response = await _chatService.createConversation(request);

      if (response.isSuccess && response.data != null) {
        final newConversation = response.data!;
        _conversations.insert(0, newConversation);
        notifyListeners();
        return newConversation;
      } else {
        _setError(response.error?.message ?? '创建对话失败');
        return null;
      }
    } catch (e) {
      print('创建对话失败: $e');
      _setError('创建对话失败: $e');
      return null;
    }
  }

  /// 选择当前对话
  Future<void> selectConversation(Conversation conversation) async {
    if (_currentConversation?.id == conversation.id) return;

    _currentConversation = conversation;
    _messages.clear();
    _currentPage = 1;
    _hasMoreMessages = true;
    notifyListeners();

    // 加载对话消息
    await loadMessages();
  }

  /// 加载对话消息
  Future<void> loadMessages({bool loadMore = false}) async {
    if (_currentConversation == null) return;
    if (_isLoadingMessages) return;
    if (loadMore && !_hasMoreMessages) return;

    try {
      _isLoadingMessages = true;
      _clearError();
      notifyListeners();

      final page = loadMore ? _currentPage + 1 : 1;
      final response = await _chatService.getConversationMessages(
        conversationId: _currentConversation!.id,
        page: page,
      );

      if (response.isSuccess && response.data != null) {
        final newMessages = response.data!.messages;

        if (loadMore) {
          _messages.addAll(newMessages);
          _currentPage = page;
        } else {
          _messages = newMessages;
          _currentPage = 1;
        }

        _hasMoreMessages = newMessages.length >= 50; // 假设每页50条
      } else {
        _setError(response.error?.message ?? '加载消息失败');
      }
    } catch (e) {
      print('加载消息失败: $e');
      _setError('加载消息失败: $e');
    } finally {
      _isLoadingMessages = false;
      notifyListeners();
    }
  }

  /// 发送文本消息
  Future<bool> sendTextMessage(String content) async {
    if (content.trim().isEmpty) return false;

    try {
      _isSendingMessage = true;
      _clearError();
      notifyListeners();

      // 首先创建并添加用户消息到界面
      final userMessage = Message(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        conversationId: _currentConversation?.id ?? 'temp_conversation',
        userId: 'current_user', // 这里应该从AuthProvider获取真实用户ID
        role: MessageRole.user,
        type: MessageType.text,
        content: content.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 立即添加用户消息到界面
      _messages.insert(0, userMessage);
      notifyListeners();

      final request = SendTextMessageRequest(
        content: content.trim(),
        conversationId: _currentConversation?.id,
      );

      final response = await _chatService.sendTextMessage(request);

      if (response.isSuccess && response.data != null) {
        final aiMessage = response.data!;

        // 如果是新对话，更新当前对话信息
        if (_currentConversation == null) {
          // 这里需要从消息中获取对话信息，或者重新加载对话列表
          await loadConversations(refresh: true);
          // 找到新创建的对话
          final newConversation = _conversations.firstWhere(
            (conv) => conv.id == aiMessage.conversationId,
          );
          _currentConversation = newConversation;

          // 更新用户消息的conversationId
          final updatedUserMessage = userMessage.copyWith(
            conversationId: aiMessage.conversationId,
          );
          _messages[0] = updatedUserMessage;
        }

        // 添加AI回复消息到列表开头
        _messages.insert(0, aiMessage);
        notifyListeners();
        return true;
      } else {
        _setError(response.error?.message ?? '发送消息失败');
        return false;
      }
    } catch (e) {
      print('发送消息失败: $e');
      _setError('发送消息失败: $e');
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  /// 发送图片消息
  Future<bool> sendImageMessage({
    required File imageFile,
    String? description,
  }) async {
    try {
      _isSendingMessage = true;
      _clearError();
      notifyListeners();

      final response = await _chatService.sendImageMessage(
        imageFile: imageFile,
        message: description,
        conversationId: _currentConversation?.id,
      );

      if (response.isSuccess && response.data != null) {
        final newMessage = response.data!;

        // 如果是新对话，更新当前对话信息
        if (_currentConversation == null) {
          await loadConversations(refresh: true);
          final newConversation = _conversations.firstWhere(
            (conv) => conv.id == newMessage.conversationId,
          );
          _currentConversation = newConversation;
        }

        _messages.insert(0, newMessage);
        notifyListeners();
        return true;
      } else {
        _setError(response.error?.message ?? '发送图片失败');
        return false;
      }
    } catch (e) {
      print('发送图片失败: $e');
      _setError('发送图片失败: $e');
      return false;
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  /// 删除对话
  Future<bool> deleteConversation(String conversationId) async {
    try {
      _clearError();

      final response = await _chatService.deleteConversation(conversationId);

      if (response.isSuccess) {
        _conversations.removeWhere((conv) => conv.id == conversationId);

        // 如果删除的是当前对话，清空当前对话
        if (_currentConversation?.id == conversationId) {
          _currentConversation = null;
          _messages.clear();
        }

        notifyListeners();
        return true;
      } else {
        _setError(response.error?.message ?? '删除对话失败');
        return false;
      }
    } catch (e) {
      print('删除对话失败: $e');
      _setError('删除对话失败: $e');
      return false;
    }
  }

  /// 更新对话标题
  Future<bool> updateConversationTitle({
    required String conversationId,
    required String title,
  }) async {
    try {
      _clearError();

      final response = await _chatService.updateConversationTitle(
        conversationId: conversationId,
        title: title,
      );

      if (response.isSuccess && response.data != null) {
        final updatedConversation = response.data!;

        // 更新对话列表中的对话
        final index = _conversations.indexWhere(
          (conv) => conv.id == conversationId,
        );
        if (index != -1) {
          _conversations[index] = updatedConversation;
        }

        // 如果是当前对话，也要更新
        if (_currentConversation?.id == conversationId) {
          _currentConversation = updatedConversation;
        }

        notifyListeners();
        return true;
      } else {
        _setError(response.error?.message ?? '更新对话标题失败');
        return false;
      }
    } catch (e) {
      print('更新对话标题失败: $e');
      _setError('更新对话标题失败: $e');
      return false;
    }
  }

  /// 清空当前对话
  void clearCurrentConversation() {
    _currentConversation = null;
    _messages.clear();
    _currentPage = 1;
    _hasMoreMessages = true;
    notifyListeners();
  }

  /// 设置错误消息
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// 清除错误消息
  void _clearError() {
    _errorMessage = null;
  }

  /// 清除所有状态
  void clear() {
    _conversations.clear();
    _currentConversation = null;
    _messages.clear();
    _currentPage = 1;
    _hasMoreMessages = true;
    _isLoadingConversations = false;
    _isLoadingMessages = false;
    _isSendingMessage = false;
    _errorMessage = null;
    notifyListeners();
  }
}
