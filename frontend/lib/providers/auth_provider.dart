// 认证状态管理 - 使用Provider管理用户认证状态
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

/// 认证状态枚举
enum AuthStatus {
  /// 未初始化
  uninitialized,

  /// 已认证
  authenticated,

  /// 未认证
  unauthenticated,

  /// 认证中
  authenticating,
}

/// 认证状态管理器
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  AuthStatus _status = AuthStatus.uninitialized;
  User? _user;
  String? _token;
  String? _errorMessage;
  bool _isLoading = false;

  /// 当前认证状态
  AuthStatus get status => _status;

  /// 当前用户
  User? get user => _user;

  /// 认证令牌
  String? get token => _token;

  /// 错误消息
  String? get errorMessage => _errorMessage;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 是否已认证
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;

  /// 是否未认证
  bool get isUnauthenticated => _status == AuthStatus.unauthenticated;

  /// 初始化认证状态
  Future<void> initialize() async {
    try {
      _setLoading(true);

      // 从本地存储获取令牌
      final prefs = await SharedPreferences.getInstance();
      final savedToken = prefs.getString('auth_token');

      if (savedToken != null) {
        _token = savedToken;

        // 验证令牌并获取用户信息
        final response = await _authService.getCurrentUser();
        if (response.isSuccess && response.data != null) {
          _user = response.data;
          _status = AuthStatus.authenticated;
          _clearError();
        } else {
          // 令牌无效，清除本地存储
          await _clearLocalAuth();
          _status = AuthStatus.unauthenticated;
        }
      } else {
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      print('初始化认证状态失败: $e');
      _status = AuthStatus.unauthenticated;
      _setError('初始化失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 临时方法：使用模拟用户初始化（用于测试聊天功能）
  Future<void> initializeWithMockUser() async {
    try {
      _setLoading(true);

      // 创建模拟用户
      _user = User(
        id: 'mock_user_001',
        username: '测试用户',
        email: '<EMAIL>',
        avatarUrl: null,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        isActive: true,
      );

      // 设置模拟令牌
      _token = 'mock_token_for_testing';

      // 设置为已认证状态
      _status = AuthStatus.authenticated;
      _clearError();

      // 保存模拟令牌到本地存储
      await _saveLocalAuth();

      debugPrint('模拟用户初始化完成: ${_user!.username}');
    } catch (e) {
      debugPrint('模拟用户初始化失败: $e');
      _status = AuthStatus.unauthenticated;
      _setError('模拟用户初始化失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登录
  Future<bool> login({required String email, required String password}) async {
    try {
      _setLoading(true);
      _status = AuthStatus.authenticating;
      _clearError();

      final request = LoginRequest(email: email, password: password);
      final response = await _authService.login(request);

      if (response.isSuccess && response.data != null) {
        _user = response.data!.user;
        _token = response.data!.token;
        _status = AuthStatus.authenticated;

        // 保存到本地存储
        await _saveLocalAuth();

        notifyListeners();
        return true;
      } else {
        _status = AuthStatus.unauthenticated;
        _setError(response.error?.message ?? '登录失败');
        return false;
      }
    } catch (e) {
      print('登录失败: $e');
      _status = AuthStatus.unauthenticated;
      _setError('登录失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 用户注册
  Future<bool> register({
    required String username,
    required String email,
    required String password,
    String? avatarUrl,
  }) async {
    try {
      _setLoading(true);
      _status = AuthStatus.authenticating;
      _clearError();

      final request = CreateUserRequest(
        username: username,
        email: email,
        password: password,
        avatarUrl: avatarUrl,
      );

      final response = await _authService.register(request);

      if (response.isSuccess && response.data != null) {
        _user = response.data!.user;
        _token = response.data!.token;
        _status = AuthStatus.authenticated;

        // 保存到本地存储
        await _saveLocalAuth();

        notifyListeners();
        return true;
      } else {
        _status = AuthStatus.unauthenticated;
        _setError(response.error?.message ?? '注册失败');
        return false;
      }
    } catch (e) {
      print('注册失败: $e');
      _status = AuthStatus.unauthenticated;
      _setError('注册失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      _setLoading(true);

      // 调用后端登出接口
      await _authService.logout();

      // 清除本地状态
      await _clearLocalAuth();

      _user = null;
      _token = null;
      _status = AuthStatus.unauthenticated;
      _clearError();
    } catch (e) {
      print('登出失败: $e');
      // 即使后端调用失败，也要清除本地状态
      await _clearLocalAuth();
      _user = null;
      _token = null;
      _status = AuthStatus.unauthenticated;
    } finally {
      _setLoading(false);
    }
  }

  /// 更新用户信息
  Future<bool> updateUser(Map<String, dynamic> updates) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await _authService.updateUser(updates);

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        await _saveLocalAuth();
        notifyListeners();
        return true;
      } else {
        _setError(response.error?.message ?? '更新用户信息失败');
        return false;
      }
    } catch (e) {
      print('更新用户信息失败: $e');
      _setError('更新用户信息失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新用户信息
  Future<void> refreshUser() async {
    if (!isAuthenticated) return;

    try {
      final response = await _authService.getCurrentUser();
      if (response.isSuccess && response.data != null) {
        _user = response.data;
        await _saveLocalAuth();
        notifyListeners();
      }
    } catch (e) {
      print('刷新用户信息失败: $e');
    }
  }

  /// 保存认证信息到本地存储
  Future<void> _saveLocalAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_token != null) {
        await prefs.setString('auth_token', _token!);
      }
      if (_user != null) {
        await prefs.setString('user_data', _user!.toJson().toString());
      }
    } catch (e) {
      print('保存认证信息失败: $e');
    }
  }

  /// 清除本地认证信息
  Future<void> _clearLocalAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('user_data');
    } catch (e) {
      print('清除认证信息失败: $e');
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误消息
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// 清除错误消息
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
