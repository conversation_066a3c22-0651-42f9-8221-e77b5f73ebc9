// 文件服务 - 处理文件上传和管理相关的API调用
import 'dart:io';
import 'package:dio/dio.dart';
import '../models/file.dart';
import '../models/api_response.dart';
import 'api_client.dart';

/// 文件服务
class FileService {
  final ApiClient _apiClient = ApiClient.instance;

  /// 上传单个文件
  Future<ApiResponse<FileUploadResponse>> uploadFile(
    File file, {
    ProgressCallback? onProgress,
  }) async {
    return await _apiClient.uploadFile<FileUploadResponse>(
      '/files/upload',
      file,
      fieldName: 'file',
      fromJson: (json) => FileUploadResponse.fromJson(json),
      onSendProgress: onProgress,
    );
  }

  /// 上传多个文件
  Future<ApiResponse<List<FileUploadResponse>>> uploadMultipleFiles(
    List<File> files, {
    ProgressCallback? onProgress,
  }) async {
    // 注意：这里需要根据后端API的实际实现来调整
    // 目前假设后端支持多文件上传
    final responses = <FileUploadResponse>[];

    for (final file in files) {
      final response = await uploadFile(file, onProgress: onProgress);
      if (response.isSuccess && response.data != null) {
        responses.add(response.data!);
      } else {
        // 如果某个文件上传失败，返回错误
        return ApiResponse<List<FileUploadResponse>>.error(
          error: response.error!,
          message: '文件 ${file.path.split('/').last} 上传失败',
        );
      }
    }

    return ApiResponse<List<FileUploadResponse>>.success(data: responses);
  }

  /// 获取文件信息
  Future<ApiResponse<FileModel>> getFileInfo(String fileId) async {
    return await _apiClient.get<FileModel>(
      '/files/$fileId',
      fromJson: (json) => FileModel.fromJson(json),
    );
  }

  /// 获取用户文件列表
  Future<ApiResponse<FileListResponse>> getUserFiles({
    int page = 1,
    int perPage = 20,
    String? fileType,
  }) async {
    final queryParameters = <String, dynamic>{
      'page': page,
      'per_page': perPage,
    };

    if (fileType != null) {
      queryParameters['file_type'] = fileType;
    }

    return await _apiClient.get<FileListResponse>(
      '/files',
      queryParameters: queryParameters,
      fromJson: (json) => FileListResponse.fromJson(json),
    );
  }

  /// 删除文件
  Future<ApiResponse<void>> deleteFile(String fileId) async {
    return await _apiClient.delete<void>('/files/$fileId');
  }

  /// 获取文件下载URL
  Future<ApiResponse<String>> getDownloadUrl(String fileId) async {
    return await _apiClient.get<String>(
      '/files/$fileId/download',
      fromJson: (json) => json.toString(),
    );
  }

  /// 获取图片预览URL
  Future<ApiResponse<String>> getPreviewUrl(String fileId) async {
    return await _apiClient.get<String>(
      '/files/$fileId/preview',
      fromJson: (json) => json.toString(),
    );
  }

  /// 清理过期文件
  Future<ApiResponse<Map<String, dynamic>>> cleanupExpiredFiles() async {
    return await _apiClient.post<Map<String, dynamic>>(
      '/files/cleanup',
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }

  /// 搜索文件
  Future<ApiResponse<FileListResponse>> searchFiles({
    required String query,
    String? fileType,
    int page = 1,
    int perPage = 20,
  }) async {
    final queryParameters = <String, dynamic>{
      'q': query,
      'page': page,
      'per_page': perPage,
    };

    if (fileType != null) {
      queryParameters['file_type'] = fileType;
    }

    return await _apiClient.get<FileListResponse>(
      '/search/files',
      queryParameters: queryParameters,
      fromJson: (json) => FileListResponse.fromJson(json),
    );
  }

  /// 获取文件统计信息
  Future<ApiResponse<Map<String, dynamic>>> getFileStats() async {
    return await _apiClient.get<Map<String, dynamic>>(
      '/files/stats',
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }

  /// 批量删除文件
  Future<ApiResponse<Map<String, dynamic>>> deleteMultipleFiles(
    List<String> fileIds,
  ) async {
    return await _apiClient.post<Map<String, dynamic>>(
      '/files/batch-delete',
      data: {'file_ids': fileIds},
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }

  /// 重命名文件
  Future<ApiResponse<FileModel>> renameFile({
    required String fileId,
    required String newName,
  }) async {
    return await _apiClient.put<FileModel>(
      '/files/$fileId',
      data: {'original_name': newName},
      fromJson: (json) => FileModel.fromJson(json),
    );
  }

  /// 获取文件分享链接
  Future<ApiResponse<String>> getShareLink({
    required String fileId,
    Duration? expiresIn,
  }) async {
    final data = <String, dynamic>{};
    if (expiresIn != null) {
      data['expires_in'] = expiresIn.inSeconds;
    }

    return await _apiClient.post<String>(
      '/files/$fileId/share',
      data: data,
      fromJson: (json) => json.toString(),
    );
  }

  /// 验证文件类型
  bool isValidImageType(String mimeType) {
    const validImageTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
    ];
    return validImageTypes.contains(mimeType.toLowerCase());
  }

  /// 验证文件大小
  bool isValidFileSize(int fileSize, {int maxSizeInMB = 10}) {
    const bytesInMB = 1024 * 1024;
    return fileSize <= maxSizeInMB * bytesInMB;
  }

  /// 获取文件扩展名
  String getFileExtension(String fileName) {
    final lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex == -1) return '';
    return fileName.substring(lastDotIndex + 1).toLowerCase();
  }

  /// 根据文件扩展名获取MIME类型
  String getMimeTypeFromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'ogg':
        return 'audio/ogg';
      case 'm4a':
        return 'audio/m4a';
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }
}
