// 聊天服务 - 处理聊天相关的API调用
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/message.dart';
import '../models/conversation.dart';
import '../models/api_response.dart';
import 'api_client.dart';

/// 聊天服务
class ChatService {
  final ApiClient _apiClient = ApiClient.instance;

  /// 检查是否为模拟模式（用于测试）
  /// TODO: 设置为false以启用真实API调用，或者删除此属性
  bool get _isMockMode => false; // 改为false以测试真实API调用

  /// 发送文本消息
  Future<ApiResponse<Message>> sendTextMessage(
    SendTextMessageRequest request,
  ) async {
    // 模拟模式：返回模拟AI回复
    if (_isMockMode) {
      await Future.delayed(const Duration(milliseconds: 800)); // 模拟AI思考时间

      // 模拟AI回复
      final aiMessage = Message(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        conversationId: request.conversationId ?? 'conv_001',
        userId: 'ai_assistant',
        role: MessageRole.assistant,
        type: MessageType.text,
        content: _generateMockAIResponse(request.content),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 返回AI回复消息
      return ApiResponse<Message>.success(data: aiMessage);
    }

    return await _apiClient.post<Message>(
      '/chat/text',
      data: request.toJson(),
      fromJson: (json) => Message.fromJson(json),
    );
  }

  /// 生成模拟AI回复
  String _generateMockAIResponse(String userInput) {
    final input = userInput.toLowerCase();

    if (input.contains('你好') ||
        input.contains('hello') ||
        input.contains('hi')) {
      return '你好！很高兴与您对话。我是AI聊天助手，有什么可以帮助您的吗？';
    } else if (input.contains('功能') || input.contains('能做什么')) {
      return '我可以帮您：\n• 回答各种问题\n• 分析图片内容\n• 进行语音交互\n• 提供专业建议\n• 协助创作和翻译\n\n请告诉我您需要什么帮助！';
    } else if (input.contains('天气')) {
      return '抱歉，我目前无法获取实时天气信息。建议您查看天气应用或网站获取准确的天气预报。';
    } else if (input.contains('时间')) {
      return '当前时间是：${DateTime.now().toString().substring(0, 19)}';
    } else if (input.contains('谢谢') || input.contains('感谢')) {
      return '不客气！很高兴能帮助到您。如果还有其他问题，请随时告诉我。';
    } else if (input.contains('再见') || input.contains('拜拜')) {
      return '再见！期待下次与您的对话。祝您生活愉快！';
    } else {
      return '这是一个很有趣的问题！作为AI助手，我正在思考如何最好地回答您关于"$userInput"的问题。\n\n在实际应用中，我会调用后端API来获取更准确和详细的回答。目前这是一个模拟回复，用于测试聊天界面的功能。';
    }
  }

  /// 发送图片消息
  Future<ApiResponse<Message>> sendImageMessage({
    required File imageFile,
    String? message,
    String? conversationId,
  }) async {
    final data = <String, dynamic>{};
    if (message != null) {
      data['message'] = message;
    }
    if (conversationId != null) {
      data['conversation_id'] = conversationId;
    }

    return await _apiClient.uploadFile<Message>(
      '/chat/image',
      imageFile,
      fieldName: 'image',
      data: data,
      fromJson: (json) => Message.fromJson(json),
    );
  }

  /// 获取对话列表
  Future<ApiResponse<ConversationListResponse>> getConversations({
    int page = 1,
    int perPage = 20,
  }) async {
    // 模拟模式：返回模拟数据
    if (_isMockMode) {
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟网络延迟

      final mockConversations = [
        Conversation(
          id: 'conv_001',
          userId: 'mock_user_001',
          title: '欢迎对话',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
          isActive: true,
        ),
        Conversation(
          id: 'conv_002',
          userId: 'mock_user_001',
          title: '关于AI的讨论',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
          isActive: true,
        ),
      ];

      final response = ConversationListResponse(
        conversations: mockConversations,
        total: mockConversations.length,
        page: page,
        perPage: perPage,
      );

      return ApiResponse<ConversationListResponse>.success(data: response);
    }

    return await _apiClient.get<ConversationListResponse>(
      '/conversations',
      queryParameters: {'page': page, 'per_page': perPage},
      fromJson: (json) => ConversationListResponse.fromJson(json),
    );
  }

  /// 获取对话详情
  Future<ApiResponse<Conversation>> getConversation(
    String conversationId,
  ) async {
    return await _apiClient.get<Conversation>(
      '/conversations/$conversationId',
      fromJson: (json) => Conversation.fromJson(json),
    );
  }

  /// 创建新对话
  Future<ApiResponse<Conversation>> createConversation(
    CreateConversationRequest request,
  ) async {
    return await _apiClient.post<Conversation>(
      '/conversations',
      data: request.toJson(),
      fromJson: (json) => Conversation.fromJson(json),
    );
  }

  /// 更新对话标题
  Future<ApiResponse<Conversation>> updateConversationTitle({
    required String conversationId,
    required String title,
  }) async {
    return await _apiClient.put<Conversation>(
      '/conversations/$conversationId',
      data: {'title': title},
      fromJson: (json) => Conversation.fromJson(json),
    );
  }

  /// 删除对话
  Future<ApiResponse<void>> deleteConversation(String conversationId) async {
    return await _apiClient.delete<void>('/conversations/$conversationId');
  }

  /// 获取对话消息列表
  Future<ApiResponse<MessageListResponse>> getConversationMessages({
    required String conversationId,
    int page = 1,
    int perPage = 50,
  }) async {
    // 模拟模式：返回模拟消息
    if (_isMockMode) {
      await Future.delayed(const Duration(milliseconds: 300)); // 模拟网络延迟

      final mockMessages = [
        Message(
          id: 'msg_001',
          conversationId: conversationId,
          userId: 'mock_user_001',
          role: MessageRole.user,
          type: MessageType.text,
          content: '你好，AI助手！',
          createdAt: DateTime.now().subtract(const Duration(minutes: 10)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 10)),
        ),
        Message(
          id: 'msg_002',
          conversationId: conversationId,
          userId: 'ai_assistant',
          role: MessageRole.assistant,
          type: MessageType.text,
          content: '你好！我是AI聊天助手，很高兴为您服务。有什么我可以帮助您的吗？',
          createdAt: DateTime.now().subtract(const Duration(minutes: 9)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 9)),
        ),
        Message(
          id: 'msg_003',
          conversationId: conversationId,
          userId: 'mock_user_001',
          role: MessageRole.user,
          type: MessageType.text,
          content: '请介绍一下你的功能',
          createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        ),
        Message(
          id: 'msg_004',
          conversationId: conversationId,
          userId: 'ai_assistant',
          role: MessageRole.assistant,
          type: MessageType.text,
          content:
              '我是一个多模态AI助手，可以帮您：\n\n1. 📝 文本对话 - 回答问题、提供建议\n2. 🖼️ 图片分析 - 识别和描述图片内容\n3. 🎤 语音交互 - 语音转文字和文字转语音\n4. 📚 知识问答 - 各领域的专业知识\n\n请随时告诉我您需要什么帮助！',
          createdAt: DateTime.now().subtract(const Duration(minutes: 4)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 4)),
        ),
      ];

      final response = MessageListResponse(
        messages: mockMessages.reversed.toList(), // 反转顺序，最新消息在前
        total: mockMessages.length,
        page: page,
        perPage: perPage,
      );

      return ApiResponse<MessageListResponse>.success(data: response);
    }

    return await _apiClient.get<MessageListResponse>(
      '/conversations/$conversationId/messages',
      queryParameters: {'page': page, 'per_page': perPage},
      fromJson: (json) => MessageListResponse.fromJson(json),
    );
  }

  /// 获取消息详情
  Future<ApiResponse<Message>> getMessage(String messageId) async {
    return await _apiClient.get<Message>(
      '/messages/$messageId',
      fromJson: (json) => Message.fromJson(json),
    );
  }

  /// 删除消息
  Future<ApiResponse<void>> deleteMessage(String messageId) async {
    return await _apiClient.delete<void>('/messages/$messageId');
  }

  /// 重新生成AI回复
  Future<ApiResponse<Message>> regenerateResponse(String messageId) async {
    return await _apiClient.post<Message>(
      '/messages/$messageId/regenerate',
      fromJson: (json) => Message.fromJson(json),
    );
  }

  /// 点赞/点踩消息
  Future<ApiResponse<void>> rateMessage({
    required String messageId,
    required bool isPositive,
  }) async {
    return await _apiClient.post<void>(
      '/messages/$messageId/rate',
      data: {'is_positive': isPositive},
    );
  }

  /// 搜索消息
  Future<ApiResponse<MessageListResponse>> searchMessages({
    required String query,
    String? conversationId,
    int page = 1,
    int perPage = 20,
  }) async {
    final queryParameters = <String, dynamic>{
      'q': query,
      'page': page,
      'per_page': perPage,
    };

    if (conversationId != null) {
      queryParameters['conversation_id'] = conversationId;
    }

    return await _apiClient.get<MessageListResponse>(
      '/search/messages',
      queryParameters: queryParameters,
      fromJson: (json) => MessageListResponse.fromJson(json),
    );
  }

  /// 导出对话
  Future<ApiResponse<String>> exportConversation({
    required String conversationId,
    String format = 'json', // json, markdown, txt
  }) async {
    return await _apiClient.get<String>(
      '/conversations/$conversationId/export',
      queryParameters: {'format': format},
      fromJson: (json) => json.toString(),
    );
  }

  /// 获取对话统计信息
  Future<ApiResponse<Map<String, dynamic>>> getConversationStats(
    String conversationId,
  ) async {
    return await _apiClient.get<Map<String, dynamic>>(
      '/conversations/$conversationId/stats',
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }
}
