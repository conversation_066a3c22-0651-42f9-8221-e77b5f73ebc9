// API客户端 - 统一的HTTP请求处理
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';

/// API客户端配置
class ApiConfig {
  /// 基础URL
  static const String baseUrl = 'http://127.0.0.1:8080/api';

  /// 连接超时时间（毫秒）
  static const int connectTimeout = 30000;

  /// 接收超时时间（毫秒）
  static const int receiveTimeout = 30000;

  /// 发送超时时间（毫秒）
  static const int sendTimeout = 30000;
}

/// API客户端
class ApiClient {
  late final Dio _dio;
  static ApiClient? _instance;

  /// 单例模式
  static ApiClient get instance {
    _instance ??= ApiClient._internal();
    return _instance!;
  }

  ApiClient._internal() {
    _dio = Dio(
      BaseOptions(
        baseUrl: ApiConfig.baseUrl,
        connectTimeout: Duration(milliseconds: ApiConfig.connectTimeout),
        receiveTimeout: Duration(milliseconds: ApiConfig.receiveTimeout),
        sendTimeout: Duration(milliseconds: ApiConfig.sendTimeout),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _setupInterceptors();
  }

  /// 设置拦截器
  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // 添加认证令牌
          final token = await _getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          // 打印请求信息
          print('🚀 API Request: ${options.method} ${options.uri}');
          if (options.data != null) {
            print('📤 Request Data: ${options.data}');
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          // 打印响应信息
          print(
            '✅ API Response: ${response.statusCode} ${response.requestOptions.uri}',
          );
          print('📥 Response Data: ${response.data}');

          handler.next(response);
        },
        onError: (error, handler) {
          // 打印错误信息
          print('❌ API Error: ${error.message}');
          if (error.response != null) {
            print('📥 Error Response: ${error.response?.data}');
          }

          // 处理401错误（未授权）
          if (error.response?.statusCode == 401) {
            _handleUnauthorized();
          }

          handler.next(error);
        },
      ),
    );
  }

  /// 获取认证令牌
  Future<String?> _getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      // 如果是模拟令牌，直接返回（用于测试）
      if (token == 'mock_token_for_testing') {
        return token;
      }

      return token;
    } catch (e) {
      print('获取认证令牌失败: $e');
      return null;
    }
  }

  /// 设置认证令牌
  Future<void> setAuthToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
    } catch (e) {
      print('设置认证令牌失败: $e');
    }
  }

  /// 清除认证令牌
  Future<void> clearAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
    } catch (e) {
      print('清除认证令牌失败: $e');
    }
  }

  /// 处理未授权错误
  void _handleUnauthorized() {
    // 清除认证令牌
    clearAuthToken();
    // 这里可以添加跳转到登录页面的逻辑
    print('用户未授权，请重新登录');
  }

  /// 检查是否为网络连接错误
  bool _isConnectionError(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        (error.type == DioExceptionType.unknown &&
            error.message?.contains('Connection failed') == true);
  }

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        error: ApiError(code: 'UNKNOWN_ERROR', message: '未知错误: $e'),
      );
    }
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        error: ApiError(code: 'UNKNOWN_ERROR', message: '未知错误: $e'),
      );
    }
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        error: ApiError(code: 'UNKNOWN_ERROR', message: '未知错误: $e'),
      );
    }
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        queryParameters: queryParameters,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        error: ApiError(code: 'UNKNOWN_ERROR', message: '未知错误: $e'),
      );
    }
  }

  /// 上传文件
  Future<ApiResponse<T>> uploadFile<T>(
    String path,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? data,
    T Function(dynamic)? fromJson,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      final formData = FormData();

      // 添加文件
      formData.files.add(
        MapEntry(
          fieldName,
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );

      // 添加其他数据
      if (data != null) {
        data.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }

      final response = await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
      );

      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error(
        error: ApiError(code: 'UPLOAD_ERROR', message: '文件上传失败: $e'),
      );
    }
  }

  /// 处理响应
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      final responseData = response.data;

      if (responseData is Map<String, dynamic>) {
        // 如果响应已经是ApiResponse格式
        if (responseData.containsKey('success')) {
          final success = responseData['success'] as bool;
          if (success) {
            final data = responseData['data'];
            return ApiResponse<T>.success(
              data: fromJson != null && data != null ? fromJson(data) : data,
              message: responseData['message'],
            );
          } else {
            return ApiResponse<T>.error(
              error: ApiError.fromJson(responseData['error']),
              message: responseData['message'],
            );
          }
        }
      }

      // 直接返回数据
      return ApiResponse<T>.success(
        data: fromJson != null ? fromJson(responseData) : responseData,
      );
    } else {
      return ApiResponse<T>.error(
        error: ApiError(
          code: 'HTTP_ERROR',
          message: 'HTTP错误: ${response.statusCode}',
        ),
      );
    }
  }

  /// 处理错误
  ApiResponse<T> _handleError<T>(DioException error) {
    String code = 'NETWORK_ERROR';
    String message = '网络错误';

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        code = 'CONNECTION_TIMEOUT';
        message = '连接超时';
        break;
      case DioExceptionType.sendTimeout:
        code = 'SEND_TIMEOUT';
        message = '发送超时';
        break;
      case DioExceptionType.receiveTimeout:
        code = 'RECEIVE_TIMEOUT';
        message = '接收超时';
        break;
      case DioExceptionType.badResponse:
        code = 'BAD_RESPONSE';
        message = '服务器响应错误: ${error.response?.statusCode}';

        // 尝试解析服务器返回的错误信息
        if (error.response?.data is Map<String, dynamic>) {
          final errorData = error.response!.data as Map<String, dynamic>;
          if (errorData.containsKey('error')) {
            return ApiResponse<T>.error(
              error: ApiError.fromJson(errorData['error']),
              message: errorData['message'],
            );
          }
        }
        break;
      case DioExceptionType.cancel:
        code = 'REQUEST_CANCELLED';
        message = '请求已取消';
        break;
      case DioExceptionType.unknown:
        code = 'UNKNOWN_ERROR';
        // 检查是否为连接失败
        if (_isConnectionError(error)) {
          code = 'CONNECTION_FAILED';
          message = '无法连接到服务器，请检查网络连接或确保后端服务正在运行';
        } else {
          message = '未知错误: ${error.message}';
        }
        break;
      default:
        break;
    }

    return ApiResponse<T>.error(
      error: ApiError(
        code: code,
        message: message,
        details: {'type': error.type.toString(), 'message': error.message},
      ),
    );
  }
}
