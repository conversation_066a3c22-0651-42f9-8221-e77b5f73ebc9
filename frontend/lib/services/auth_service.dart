// 认证服务 - 处理用户认证相关的API调用
import '../models/user.dart';
import '../models/api_response.dart';
import 'api_client.dart';

/// 认证服务
class AuthService {
  final ApiClient _apiClient = ApiClient.instance;
  
  /// 用户注册
  Future<ApiResponse<AuthResponse>> register(CreateUserRequest request) async {
    return await _apiClient.post<AuthResponse>(
      '/auth/register',
      data: request.toJson(),
      fromJson: (json) => AuthResponse.fromJson(json),
    );
  }
  
  /// 用户登录
  Future<ApiResponse<AuthResponse>> login(LoginRequest request) async {
    final response = await _apiClient.post<AuthResponse>(
      '/auth/login',
      data: request.toJson(),
      fromJson: (json) => AuthResponse.fromJson(json),
    );
    
    // 如果登录成功，保存认证令牌
    if (response.isSuccess && response.data != null) {
      await _apiClient.setAuthToken(response.data!.token);
    }
    
    return response;
  }
  
  /// 用户登出
  Future<ApiResponse<void>> logout() async {
    // 清除本地存储的认证令牌
    await _apiClient.clearAuthToken();
    
    // 调用后端登出接口（如果有的话）
    return await _apiClient.post<void>('/auth/logout');
  }
  
  /// 刷新令牌
  Future<ApiResponse<AuthResponse>> refreshToken() async {
    return await _apiClient.post<AuthResponse>(
      '/auth/refresh',
      fromJson: (json) => AuthResponse.fromJson(json),
    );
  }
  
  /// 获取当前用户信息
  Future<ApiResponse<User>> getCurrentUser() async {
    return await _apiClient.get<User>(
      '/auth/me',
      fromJson: (json) => User.fromJson(json),
    );
  }
  
  /// 更新用户信息
  Future<ApiResponse<User>> updateUser(Map<String, dynamic> updates) async {
    return await _apiClient.put<User>(
      '/auth/me',
      data: updates,
      fromJson: (json) => User.fromJson(json),
    );
  }
  
  /// 修改密码
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    return await _apiClient.put<void>(
      '/auth/password',
      data: {
        'current_password': currentPassword,
        'new_password': newPassword,
      },
    );
  }
  
  /// 重置密码（发送重置邮件）
  Future<ApiResponse<void>> resetPassword(String email) async {
    return await _apiClient.post<void>(
      '/auth/reset-password',
      data: {'email': email},
    );
  }
  
  /// 确认重置密码
  Future<ApiResponse<void>> confirmResetPassword({
    required String token,
    required String newPassword,
  }) async {
    return await _apiClient.post<void>(
      '/auth/reset-password/confirm',
      data: {
        'token': token,
        'new_password': newPassword,
      },
    );
  }
  
  /// 验证邮箱
  Future<ApiResponse<void>> verifyEmail(String token) async {
    return await _apiClient.post<void>(
      '/auth/verify-email',
      data: {'token': token},
    );
  }
  
  /// 重新发送验证邮件
  Future<ApiResponse<void>> resendVerificationEmail() async {
    return await _apiClient.post<void>('/auth/resend-verification');
  }
}
