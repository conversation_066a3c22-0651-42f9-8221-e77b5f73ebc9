// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
  id: json['id'] as String,
  conversationId: json['conversationId'] as String,
  userId: json['userId'] as String,
  role: $enumDecode(_$MessageRoleEnumMap, json['role']),
  type: $enumDecode(_$MessageTypeEnumMap, json['type']),
  content: json['content'] as String,
  fileId: json['fileId'] as String?,
  fileUrl: json['fileUrl'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
  'id': instance.id,
  'conversationId': instance.conversationId,
  'userId': instance.userId,
  'role': _$MessageRoleEnumMap[instance.role]!,
  'type': _$MessageTypeEnumMap[instance.type]!,
  'content': instance.content,
  'fileId': instance.fileId,
  'fileUrl': instance.fileUrl,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$MessageRoleEnumMap = {
  MessageRole.user: 'user',
  MessageRole.assistant: 'assistant',
  MessageRole.system: 'system',
};

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.image: 'image',
  MessageType.audio: 'audio',
  MessageType.file: 'file',
};

SendTextMessageRequest _$SendTextMessageRequestFromJson(
  Map<String, dynamic> json,
) => SendTextMessageRequest(
  content: json['content'] as String,
  conversationId: json['conversation_id'] as String?,
);

Map<String, dynamic> _$SendTextMessageRequestToJson(
  SendTextMessageRequest instance,
) => <String, dynamic>{
  'content': instance.content,
  'conversation_id': instance.conversationId,
};

SendImageMessageRequest _$SendImageMessageRequestFromJson(
  Map<String, dynamic> json,
) => SendImageMessageRequest(
  message: json['message'] as String?,
  conversationId: json['conversation_id'] as String?,
);

Map<String, dynamic> _$SendImageMessageRequestToJson(
  SendImageMessageRequest instance,
) => <String, dynamic>{
  'message': instance.message,
  'conversation_id': instance.conversationId,
};

MessageListResponse _$MessageListResponseFromJson(Map<String, dynamic> json) =>
    MessageListResponse(
      messages:
          (json['messages'] as List<dynamic>)
              .map((e) => Message.fromJson(e as Map<String, dynamic>))
              .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      perPage: (json['perPage'] as num).toInt(),
    );

Map<String, dynamic> _$MessageListResponseToJson(
  MessageListResponse instance,
) => <String, dynamic>{
  'messages': instance.messages,
  'total': instance.total,
  'page': instance.page,
  'perPage': instance.perPage,
};
