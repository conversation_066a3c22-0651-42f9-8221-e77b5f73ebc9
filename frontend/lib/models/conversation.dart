// 对话数据模型 - 与后端Conversation模型对应
import 'package:json_annotation/json_annotation.dart';

part 'conversation.g.dart';

/// 对话数据模型
@JsonSerializable()
class Conversation {
  /// 对话ID
  final String id;
  
  /// 用户ID
  final String userId;
  
  /// 对话标题
  final String title;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 是否激活
  final bool isActive;

  const Conversation({
    required this.id,
    required this.userId,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
  });

  /// 从JSON创建Conversation对象
  factory Conversation.fromJson(Map<String, dynamic> json) => 
      _$ConversationFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$ConversationToJson(this);

  /// 复制并修改部分属性
  Conversation copyWith({
    String? id,
    String? userId,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return Conversation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Conversation &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Conversation{id: $id, title: $title, userId: $userId}';
  }
}

/// 创建对话请求
@JsonSerializable()
class CreateConversationRequest {
  /// 对话标题
  final String title;

  const CreateConversationRequest({
    required this.title,
  });

  /// 从JSON创建对象
  factory CreateConversationRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateConversationRequestFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$CreateConversationRequestToJson(this);
}

/// 对话列表响应
@JsonSerializable()
class ConversationListResponse {
  /// 对话列表
  final List<Conversation> conversations;
  
  /// 总数
  final int total;
  
  /// 当前页
  final int page;
  
  /// 每页数量
  final int perPage;

  const ConversationListResponse({
    required this.conversations,
    required this.total,
    required this.page,
    required this.perPage,
  });

  /// 从JSON创建对象
  factory ConversationListResponse.fromJson(Map<String, dynamic> json) =>
      _$ConversationListResponseFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$ConversationListResponseToJson(this);
}
