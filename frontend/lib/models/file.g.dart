// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FileModel _$FileModelFromJson(Map<String, dynamic> json) => FileModel(
  id: json['id'] as String,
  userId: json['userId'] as String,
  originalName: json['originalName'] as String,
  fileName: json['fileName'] as String,
  filePath: json['filePath'] as String,
  fileSize: (json['fileSize'] as num).toInt(),
  mimeType: json['mimeType'] as String,
  fileType: $enumDecode(_$FileTypeEnumMap, json['fileType']),
  fileHash: json['fileHash'] as String,
  downloadUrl: json['downloadUrl'] as String?,
  previewUrl: json['previewUrl'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  expiresAt:
      json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
);

Map<String, dynamic> _$FileModelToJson(FileModel instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'originalName': instance.originalName,
  'fileName': instance.fileName,
  'filePath': instance.filePath,
  'fileSize': instance.fileSize,
  'mimeType': instance.mimeType,
  'fileType': _$FileTypeEnumMap[instance.fileType]!,
  'fileHash': instance.fileHash,
  'downloadUrl': instance.downloadUrl,
  'previewUrl': instance.previewUrl,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'expiresAt': instance.expiresAt?.toIso8601String(),
};

const _$FileTypeEnumMap = {
  FileType.image: 'image',
  FileType.audio: 'audio',
  FileType.document: 'document',
  FileType.other: 'other',
};

FileUploadResponse _$FileUploadResponseFromJson(Map<String, dynamic> json) =>
    FileUploadResponse(
      file: FileModel.fromJson(json['file'] as Map<String, dynamic>),
      message: json['message'] as String,
    );

Map<String, dynamic> _$FileUploadResponseToJson(FileUploadResponse instance) =>
    <String, dynamic>{'file': instance.file, 'message': instance.message};

FileListResponse _$FileListResponseFromJson(Map<String, dynamic> json) =>
    FileListResponse(
      files:
          (json['files'] as List<dynamic>)
              .map((e) => FileModel.fromJson(e as Map<String, dynamic>))
              .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      perPage: (json['perPage'] as num).toInt(),
    );

Map<String, dynamic> _$FileListResponseToJson(FileListResponse instance) =>
    <String, dynamic>{
      'files': instance.files,
      'total': instance.total,
      'page': instance.page,
      'perPage': instance.perPage,
    };
