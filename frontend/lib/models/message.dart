// 消息数据模型 - 与后端Message模型对应
import 'package:json_annotation/json_annotation.dart';

part 'message.g.dart';

/// 消息角色枚举
enum MessageRole {
  @JsonValue('user')
  user,
  @JsonValue('assistant')
  assistant,
  @JsonValue('system')
  system,
}

/// 消息类型枚举
enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('audio')
  audio,
  @JsonValue('file')
  file,
}

/// 消息数据模型
@JsonSerializable()
class Message {
  /// 消息ID
  final String id;

  /// 对话ID
  final String conversationId;

  /// 用户ID
  final String userId;

  /// 消息角色
  final MessageRole role;

  /// 消息类型
  final MessageType type;

  /// 消息内容
  final String content;

  /// 文件ID（如果是文件消息）
  final String? fileId;

  /// 文件URL（如果是文件消息）
  final String? fileUrl;

  /// 元数据（JSON格式的额外信息）
  final Map<String, dynamic>? metadata;

  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  final DateTime updatedAt;

  const Message({
    required this.id,
    required this.conversationId,
    required this.userId,
    required this.role,
    required this.type,
    required this.content,
    this.fileId,
    this.fileUrl,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 从JSON创建Message对象
  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  /// 复制并修改部分属性
  Message copyWith({
    String? id,
    String? conversationId,
    String? userId,
    MessageRole? role,
    MessageType? type,
    String? content,
    String? fileId,
    String? fileUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Message(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      type: type ?? this.type,
      content: content ?? this.content,
      fileId: fileId ?? this.fileId,
      fileUrl: fileUrl ?? this.fileUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 是否为用户消息
  bool get isUser => role == MessageRole.user;

  /// 是否为助手消息
  bool get isAssistant => role == MessageRole.assistant;

  /// 是否为系统消息
  bool get isSystem => role == MessageRole.system;

  /// 是否为文本消息
  bool get isText => type == MessageType.text;

  /// 是否为图片消息
  bool get isImage => type == MessageType.image;

  /// 是否为音频消息
  bool get isAudio => type == MessageType.audio;

  /// 是否为文件消息
  bool get isFile => type == MessageType.file;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Message && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Message{id: $id, role: $role, type: $type, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content}}';
  }
}

/// 发送文本消息请求
@JsonSerializable()
class SendTextMessageRequest {
  /// 消息内容（对应后端API的content字段）
  final String content;

  /// 对话ID（可选，如果不提供则创建新对话）
  @JsonKey(name: 'conversation_id')
  final String? conversationId;

  const SendTextMessageRequest({required this.content, this.conversationId});

  /// 从JSON创建对象
  factory SendTextMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendTextMessageRequestFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$SendTextMessageRequestToJson(this);
}

/// 发送图片消息请求
@JsonSerializable()
class SendImageMessageRequest {
  /// 图片描述（可选）
  final String? message;

  /// 对话ID（可选，如果不提供则创建新对话）
  @JsonKey(name: 'conversation_id')
  final String? conversationId;

  const SendImageMessageRequest({this.message, this.conversationId});

  /// 从JSON创建对象
  factory SendImageMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendImageMessageRequestFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$SendImageMessageRequestToJson(this);
}

/// 消息列表响应
@JsonSerializable()
class MessageListResponse {
  /// 消息列表
  final List<Message> messages;

  /// 总数
  final int total;

  /// 当前页
  final int page;

  /// 每页数量
  final int perPage;

  const MessageListResponse({
    required this.messages,
    required this.total,
    required this.page,
    required this.perPage,
  });

  /// 从JSON创建对象
  factory MessageListResponse.fromJson(Map<String, dynamic> json) =>
      _$MessageListResponseFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$MessageListResponseToJson(this);
}
