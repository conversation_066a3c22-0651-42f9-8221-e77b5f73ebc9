// 用户数据模型 - 与后端User模型对应
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

/// 用户数据模型
@JsonSerializable()
class User {
  /// 用户ID
  final String id;
  
  /// 用户名
  final String username;
  
  /// 邮箱地址
  final String email;
  
  /// 头像URL
  final String? avatarUrl;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 是否激活
  final bool isActive;

  const User({
    required this.id,
    required this.username,
    required this.email,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
  });

  /// 从JSON创建User对象
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// 复制并修改部分属性
  User copyWith({
    String? id,
    String? username,
    String? email,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User{id: $id, username: $username, email: $email, isActive: $isActive}';
  }
}

/// 用户创建请求
@JsonSerializable()
class CreateUserRequest {
  /// 用户名
  final String username;
  
  /// 邮箱地址
  final String email;
  
  /// 密码
  final String password;
  
  /// 头像URL（可选）
  final String? avatarUrl;

  const CreateUserRequest({
    required this.username,
    required this.email,
    required this.password,
    this.avatarUrl,
  });

  /// 从JSON创建对象
  factory CreateUserRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateUserRequestFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$CreateUserRequestToJson(this);
}

/// 用户登录请求
@JsonSerializable()
class LoginRequest {
  /// 邮箱地址
  final String email;
  
  /// 密码
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  /// 从JSON创建对象
  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

/// 认证响应
@JsonSerializable()
class AuthResponse {
  /// 用户信息
  final User user;
  
  /// JWT令牌
  final String token;

  const AuthResponse({
    required this.user,
    required this.token,
  });

  /// 从JSON创建对象
  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}
