// 文件数据模型 - 与后端File模型对应
import 'package:json_annotation/json_annotation.dart';

part 'file.g.dart';

/// 文件类型枚举
enum FileType {
  @JsonValue('image')
  image,
  @JsonValue('audio')
  audio,
  @JsonValue('document')
  document,
  @JsonValue('other')
  other,
}

/// 文件数据模型
@JsonSerializable()
class FileModel {
  /// 文件ID
  final String id;
  
  /// 用户ID
  final String userId;
  
  /// 原始文件名
  final String originalName;
  
  /// 存储文件名
  final String fileName;
  
  /// 文件路径
  final String filePath;
  
  /// 文件大小（字节）
  final int fileSize;
  
  /// MIME类型
  final String mimeType;
  
  /// 文件类型
  final FileType fileType;
  
  /// 文件哈希值
  final String fileHash;
  
  /// 下载URL
  final String? downloadUrl;
  
  /// 预览URL（仅图片）
  final String? previewUrl;
  
  /// 元数据（JSON格式的额外信息）
  final Map<String, dynamic>? metadata;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 过期时间（可选）
  final DateTime? expiresAt;

  const FileModel({
    required this.id,
    required this.userId,
    required this.originalName,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.mimeType,
    required this.fileType,
    required this.fileHash,
    this.downloadUrl,
    this.previewUrl,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.expiresAt,
  });

  /// 从JSON创建FileModel对象
  factory FileModel.fromJson(Map<String, dynamic> json) => 
      _$FileModelFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$FileModelToJson(this);

  /// 复制并修改部分属性
  FileModel copyWith({
    String? id,
    String? userId,
    String? originalName,
    String? fileName,
    String? filePath,
    int? fileSize,
    String? mimeType,
    FileType? fileType,
    String? fileHash,
    String? downloadUrl,
    String? previewUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiresAt,
  }) {
    return FileModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      originalName: originalName ?? this.originalName,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      fileType: fileType ?? this.fileType,
      fileHash: fileHash ?? this.fileHash,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      previewUrl: previewUrl ?? this.previewUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  /// 是否为图片文件
  bool get isImage => fileType == FileType.image;
  
  /// 是否为音频文件
  bool get isAudio => fileType == FileType.audio;
  
  /// 是否为文档文件
  bool get isDocument => fileType == FileType.document;
  
  /// 格式化文件大小
  String get formattedSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FileModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FileModel{id: $id, originalName: $originalName, fileType: $fileType, size: $formattedSize}';
  }
}

/// 文件上传响应
@JsonSerializable()
class FileUploadResponse {
  /// 文件信息
  final FileModel file;
  
  /// 上传消息
  final String message;

  const FileUploadResponse({
    required this.file,
    required this.message,
  });

  /// 从JSON创建对象
  factory FileUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$FileUploadResponseFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$FileUploadResponseToJson(this);
}

/// 文件列表响应
@JsonSerializable()
class FileListResponse {
  /// 文件列表
  final List<FileModel> files;
  
  /// 总数
  final int total;
  
  /// 当前页
  final int page;
  
  /// 每页数量
  final int perPage;

  const FileListResponse({
    required this.files,
    required this.total,
    required this.page,
    required this.perPage,
  });

  /// 从JSON创建对象
  factory FileListResponse.fromJson(Map<String, dynamic> json) =>
      _$FileListResponseFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$FileListResponseToJson(this);
}
