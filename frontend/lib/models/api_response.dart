// API响应数据模型 - 统一的API响应格式
import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

/// 统一的API响应格式
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  /// 是否成功
  final bool success;
  
  /// 响应数据
  final T? data;
  
  /// 错误信息
  final ApiError? error;
  
  /// 响应消息
  final String? message;
  
  /// 时间戳
  final DateTime? timestamp;

  const ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.message,
    this.timestamp,
  });

  /// 从JSON创建ApiResponse对象
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);

  /// 转换为JSON
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// 创建成功响应
  factory ApiResponse.success({
    T? data,
    String? message,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  /// 创建错误响应
  factory ApiResponse.error({
    required ApiError error,
    String? message,
  }) {
    return ApiResponse<T>(
      success: false,
      error: error,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  /// 是否为成功响应
  bool get isSuccess => success && error == null;
  
  /// 是否为错误响应
  bool get isError => !success || error != null;

  @override
  String toString() {
    return 'ApiResponse{success: $success, data: $data, error: $error, message: $message}';
  }
}

/// API错误信息
@JsonSerializable()
class ApiError {
  /// 错误代码
  final String code;
  
  /// 错误消息
  final String message;
  
  /// 错误详情
  final Map<String, dynamic>? details;

  const ApiError({
    required this.code,
    required this.message,
    this.details,
  });

  /// 从JSON创建ApiError对象
  factory ApiError.fromJson(Map<String, dynamic> json) => 
      _$ApiErrorFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);

  @override
  String toString() {
    return 'ApiError{code: $code, message: $message, details: $details}';
  }
}

/// 分页信息
@JsonSerializable()
class PaginationInfo {
  /// 当前页
  final int page;
  
  /// 每页数量
  final int perPage;
  
  /// 总数
  final int total;
  
  /// 总页数
  final int totalPages;
  
  /// 是否有下一页
  final bool hasNext;
  
  /// 是否有上一页
  final bool hasPrev;

  const PaginationInfo({
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  /// 从JSON创建PaginationInfo对象
  factory PaginationInfo.fromJson(Map<String, dynamic> json) => 
      _$PaginationInfoFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$PaginationInfoToJson(this);

  /// 创建分页信息
  factory PaginationInfo.create({
    required int page,
    required int perPage,
    required int total,
  }) {
    final totalPages = (total / perPage).ceil();
    return PaginationInfo(
      page: page,
      perPage: perPage,
      total: total,
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    );
  }

  @override
  String toString() {
    return 'PaginationInfo{page: $page, perPage: $perPage, total: $total, totalPages: $totalPages}';
  }
}

/// 分页响应
@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> {
  /// 数据列表
  final List<T> data;
  
  /// 分页信息
  final PaginationInfo pagination;

  const PaginatedResponse({
    required this.data,
    required this.pagination,
  });

  /// 从JSON创建PaginatedResponse对象
  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$PaginatedResponseFromJson(json, fromJsonT);

  /// 转换为JSON
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);

  @override
  String toString() {
    return 'PaginatedResponse{data: ${data.length} items, pagination: $pagination}';
  }
}
