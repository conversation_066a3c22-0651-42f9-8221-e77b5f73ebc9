// 聊天页面 - 主要的聊天界面
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/chat_provider.dart';
import '../widgets/message_bubble.dart';
import '../widgets/chat_input.dart';
import '../models/conversation.dart';

/// 聊天页面
class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始化聊天数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ChatProvider>().loadConversations();
    });

    // 监听滚动事件，实现上拉加载更多
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动监听
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // 接近底部时加载更多消息
      final chatProvider = context.read<ChatProvider>();
      if (chatProvider.hasMoreMessages && !chatProvider.isLoadingMessages) {
        chatProvider.loadMessages(loadMore: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: Column(
        children: [
          // 消息列表
          Expanded(child: _buildMessageList()),
          // 输入区域
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Consumer<ChatProvider>(
        builder: (context, chatProvider, child) {
          return Text(
            chatProvider.currentConversation?.title ?? 'AI聊天助手',
            style: const TextStyle(fontWeight: FontWeight.w600),
          );
        },
      ),
      actions: [
        // 新建对话按钮
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: () => _startNewConversation(),
          tooltip: '新建对话',
        ),
        // 用户菜单
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return PopupMenuButton<String>(
              icon: CircleAvatar(
                radius: 16,
                backgroundColor: Theme.of(context).colorScheme.primary,
                child: Text(
                  authProvider.user?.username.substring(0, 1).toUpperCase() ??
                      'U',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              onSelected: (value) => _handleMenuAction(value),
              itemBuilder:
                  (context) => [
                    PopupMenuItem(
                      value: 'profile',
                      child: Row(
                        children: [
                          const Icon(Icons.person),
                          const SizedBox(width: 8),
                          Text('个人资料'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'settings',
                      child: Row(
                        children: [
                          const Icon(Icons.settings),
                          const SizedBox(width: 8),
                          Text('设置'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    PopupMenuItem(
                      value: 'logout',
                      child: Row(
                        children: [
                          const Icon(Icons.logout),
                          const SizedBox(width: 8),
                          Text('退出登录'),
                        ],
                      ),
                    ),
                  ],
            );
          },
        ),
      ],
    );
  }

  /// 构建侧边栏
  Widget _buildDrawer() {
    return Drawer(
      child: Consumer<ChatProvider>(
        builder: (context, chatProvider, child) {
          return Column(
            children: [
              // 抽屉头部
              DrawerHeader(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 48,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '对话历史',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // 对话列表
              Expanded(
                child:
                    chatProvider.isLoadingConversations
                        ? const Center(child: CircularProgressIndicator())
                        : chatProvider.conversations.isEmpty
                        ? _buildEmptyConversations()
                        : _buildConversationList(chatProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建空对话列表
  Widget _buildEmptyConversations() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有对话',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始新的对话吧',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建对话列表
  Widget _buildConversationList(ChatProvider chatProvider) {
    return ListView.builder(
      itemCount: chatProvider.conversations.length,
      itemBuilder: (context, index) {
        final conversation = chatProvider.conversations[index];
        final isSelected =
            chatProvider.currentConversation?.id == conversation.id;

        return ListTile(
          selected: isSelected,
          leading: CircleAvatar(
            backgroundColor:
                isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceVariant,
            child: Icon(
              Icons.chat,
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          title: Text(
            conversation.title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(
            _formatDate(conversation.updatedAt),
            style: Theme.of(context).textTheme.bodySmall,
          ),
          onTap: () {
            chatProvider.selectConversation(conversation);
            Navigator.of(context).pop(); // 关闭抽屉
          },
          trailing: PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected:
                (value) => _handleConversationAction(value, conversation),
            itemBuilder:
                (context) => [
                  const PopupMenuItem(value: 'rename', child: Text('重命名')),
                  const PopupMenuItem(value: 'delete', child: Text('删除')),
                ],
          ),
        );
      },
    );
  }

  /// 构建消息列表
  Widget _buildMessageList() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        if (chatProvider.currentConversation == null) {
          return _buildWelcomeScreen();
        }

        if (chatProvider.isLoadingMessages && chatProvider.messages.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (chatProvider.messages.isEmpty) {
          return _buildEmptyMessages();
        }

        return RefreshIndicator(
          onRefresh: () => chatProvider.loadMessages(),
          child: ListView.builder(
            controller: _scrollController,
            reverse: true, // 从底部开始显示
            itemCount:
                chatProvider.messages.length +
                (chatProvider.isLoadingMessages ? 1 : 0),
            itemBuilder: (context, index) {
              // 加载指示器
              if (index == chatProvider.messages.length) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              // 消息气泡
              final message = chatProvider.messages[index];
              return MessageBubble(
                message: message,
                showAvatar: true,
                showTime: true,
              );
            },
          ),
        );
      },
    );
  }

  /// 构建欢迎屏幕
  Widget _buildWelcomeScreen() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.smart_toy, size: 80, color: theme.colorScheme.primary),
          const SizedBox(height: 24),
          Text(
            '欢迎使用AI聊天助手',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '开始新的对话，体验智能AI助手',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _startNewConversation,
            icon: const Icon(Icons.add),
            label: const Text('开始新对话'),
          ),
        ],
      ),
    );
  }

  /// 构建空消息提示
  Widget _buildEmptyMessages() {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '开始对话',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '发送消息开始与AI助手对话',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        return ChatInput(
          onSendText: _handleSendText,
          onSendImage: _handleSendImage,
          isSending: chatProvider.isSendingMessage,
          enabled: true,
        );
      },
    );
  }

  /// 处理发送文本消息
  Future<void> _handleSendText(String text) async {
    final chatProvider = context.read<ChatProvider>();
    final success = await chatProvider.sendTextMessage(text);

    if (success) {
      // 滚动到底部
      _scrollToBottom();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(chatProvider.errorMessage ?? '发送消息失败'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  /// 处理发送图片消息
  Future<void> _handleSendImage(File imageFile, String? description) async {
    final chatProvider = context.read<ChatProvider>();
    final success = await chatProvider.sendImageMessage(
      imageFile: imageFile,
      description: description,
    );

    if (success) {
      _scrollToBottom();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(chatProvider.errorMessage ?? '发送图片失败'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  /// 开始新对话
  void _startNewConversation() {
    final chatProvider = context.read<ChatProvider>();
    chatProvider.clearCurrentConversation();
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'profile':
        _showComingSoon('个人资料');
        break;
      case 'settings':
        _showComingSoon('设置');
        break;
      case 'logout':
        _handleLogout();
        break;
    }
  }

  /// 处理对话操作
  void _handleConversationAction(String action, Conversation conversation) {
    switch (action) {
      case 'rename':
        _showRenameDialog(conversation);
        break;
      case 'delete':
        _showDeleteDialog(conversation);
        break;
    }
  }

  /// 显示重命名对话框
  void _showRenameDialog(Conversation conversation) {
    final controller = TextEditingController(text: conversation.title);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('重命名对话'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: '对话标题',
                border: OutlineInputBorder(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<ChatProvider>().updateConversationTitle(
                    conversationId: conversation.id,
                    title: controller.text.trim(),
                  );
                },
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteDialog(Conversation conversation) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('删除对话'),
            content: Text('确定要删除对话"${conversation.title}"吗？此操作无法撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<ChatProvider>().deleteConversation(
                    conversation.id,
                  );
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  /// 处理退出登录
  void _handleLogout() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('退出登录'),
            content: const Text('确定要退出登录吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<AuthProvider>().logout();
                  context.read<ChatProvider>().clear();
                },
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('$feature功能即将推出')));
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  /// 格式化日期
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
