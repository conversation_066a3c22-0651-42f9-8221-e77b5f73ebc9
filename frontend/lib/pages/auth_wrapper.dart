// 认证包装器 - 根据认证状态显示不同页面
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
// import 'login_page.dart'; // 暂时注释，测试完成后恢复
import 'chat_page.dart';

/// 认证包装器组件
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    // 临时跳过认证，直接设置模拟用户状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().initializeWithMockUser();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // 临时修改：直接显示聊天页面，跳过认证检查
        // TODO: 恢复完整认证流程时，取消注释下面的代码并删除直接返回ChatPage的代码

        // 直接返回聊天页面进行测试
        if (authProvider.status == AuthStatus.uninitialized) {
          return const _LoadingPage();
        }
        return const ChatPage();

        /* 完整认证流程代码（暂时注释）：
        switch (authProvider.status) {
          case AuthStatus.uninitialized:
            return const _LoadingPage();
          case AuthStatus.authenticated:
            return const ChatPage();
          case AuthStatus.unauthenticated:
          case AuthStatus.authenticating:
            return const LoginPage();
        }
        */
      },
    );
  }
}

/// 加载页面
class _LoadingPage extends StatelessWidget {
  const _LoadingPage();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.smart_toy,
                size: 60,
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),

            const SizedBox(height: 32),

            // 应用标题
            Text(
              'AI聊天助手',
              style: theme.textTheme.headlineMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // 加载指示器
            CircularProgressIndicator(color: theme.colorScheme.primary),

            const SizedBox(height: 16),

            // 加载文本
            Text(
              '正在初始化...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
