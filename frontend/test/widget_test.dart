// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:ai_chat_frontend/main.dart';

void main() {
  testWidgets('AI Chat App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AIChatApp());

    // Verify that the app loads with the loading screen or login page
    expect(find.text('AI聊天助手'), findsOneWidget);
  });
}
