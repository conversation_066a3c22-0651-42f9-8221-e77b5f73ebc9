# 测试模式说明

## 当前状态
应用目前运行在**测试模式**下，跳过了用户认证流程，直接进入聊天界面。这样可以先测试聊天功能和UI交互，而不需要配置完整的后端认证系统。

## 测试模式特性

### ✅ 已启用的功能
- **模拟用户认证**：自动创建测试用户（用户名：测试用户）
- **模拟对话数据**：预设了示例对话和消息
- **模拟AI回复**：发送消息后会收到智能的模拟回复
- **完整UI功能**：所有界面组件都可以正常使用
- **状态管理**：Provider状态管理正常工作

### 🔄 模拟的API调用
- `getConversations()` - 返回预设的对话列表
- `getConversationMessages()` - 返回示例消息历史
- `sendTextMessage()` - 返回智能的模拟AI回复

### 📱 可以测试的功能
1. **聊天界面**：发送消息、查看回复、滚动消息列表
2. **对话管理**：切换对话、查看对话列表
3. **UI交互**：侧边栏、菜单、按钮等
4. **状态管理**：加载状态、错误处理等
5. **响应式设计**：窗口大小调整、布局适配

## 恢复完整认证流程

当聊天功能测试完成后，按以下步骤恢复完整的认证流程：

### 1. 修改 AuthWrapper.dart
```dart
// 在 frontend/lib/pages/auth_wrapper.dart 中：

// 将这行：
context.read<AuthProvider>().initializeWithMockUser();

// 改回：
context.read<AuthProvider>().initialize();

// 取消注释完整认证流程代码，删除直接返回ChatPage的代码
```

### 2. 修改 ChatService.dart
```dart
// 在 frontend/lib/services/chat_service.dart 中：

// 将这行：
bool get _isMockMode => true;

// 改为：
bool get _isMockMode => false;

// 或者完全删除模拟模式相关代码
```

### 3. 恢复导入
```dart
// 在 frontend/lib/pages/auth_wrapper.dart 中：

// 取消注释：
import 'login_page.dart';
```

### 4. 清理模拟代码（可选）
- 删除 `AuthProvider.initializeWithMockUser()` 方法
- 删除 `ChatService._generateMockAIResponse()` 方法
- 删除 `ChatService._isMockMode` 属性
- 删除所有模拟数据生成代码

## 当前测试建议

1. **测试发送消息**：尝试发送不同类型的消息，观察AI回复
2. **测试UI交互**：点击各种按钮、菜单项，测试响应
3. **测试对话切换**：在侧边栏中切换不同对话
4. **测试错误处理**：观察加载状态和错误提示
5. **测试响应式设计**：调整窗口大小，观察布局变化

## 智能回复测试

模拟AI会根据输入内容返回不同的回复：
- 发送"你好" → 欢迎消息
- 发送"功能" → 功能介绍
- 发送"时间" → 当前时间
- 发送"谢谢" → 礼貌回复
- 发送其他内容 → 通用智能回复

## 注意事项

- 当前所有数据都是模拟的，不会保存到真实数据库
- 图片上传功能需要后端API支持，目前可能无法正常工作
- 语音功能插件已禁用，需要后续启用和测试
- 模拟模式下的令牌是 `mock_token_for_testing`

## 下一步计划

1. ✅ 测试聊天界面基本功能
2. 🔄 启动Rust后端服务
3. 🔄 测试真实API连接
4. 🔄 实现图片上传功能
5. 🔄 添加语音功能
6. 🔄 实现流式对话
7. 🔄 恢复完整认证流程
