# 多模态聊天功能测试指南

## 概述

本文档介绍如何测试AI聊天应用的多模态功能（文本+图片）。我们提供了完整的测试脚本和curl命令示例。

## 快速开始

### 1. 启动后端服务

```bash
cd backend
cargo run
```

### 2. 运行自动化测试

```bash
# 运行所有多模态测试
./test_multimodal_api.sh

# 运行特定测试
./test_multimodal_api.sh multimodal  # 只测试多模态消息
./test_multimodal_api.sh stream      # 只测试流式响应
./test_multimodal_api.sh health      # 只测试健康检查
```

### 3. 手动测试示例

#### 发送纯文本消息
```bash
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": "你好，请介绍一下你的多模态能力"
  }'
```

#### 发送多模态消息（文本+图片）
```bash
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请分析这张图片的内容"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ]
  }'
```

#### 发送流式多模态消息
```bash
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请为这张图片写一个故事"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ],
    "stream": true
  }'
```

## 测试脚本功能

### test_multimodal_api.sh

这是主要的测试脚本，包含以下测试用例：

1. **健康检查** - 验证服务器和AI服务状态
2. **纯文本消息** - 测试使用多模态端点发送文本
3. **多模态消息** - 测试文本+图片混合消息
4. **流式响应** - 测试实时流式多模态响应
5. **复杂消息** - 测试包含多个文本段落的复杂消息
6. **错误处理** - 测试各种错误情况的处理
7. **向后兼容** - 验证旧API端点仍然可用

### 使用方法

```bash
# 运行所有测试
./test_multimodal_api.sh

# 运行特定测试
./test_multimodal_api.sh [选项]

# 可用选项：
# health      - 健康检查测试
# text        - 纯文本消息测试
# multimodal  - 多模态消息测试
# stream      - 流式响应测试
# complex     - 复杂多模态测试
# error       - 错误处理测试
# compat      - 向后兼容性测试
# help        - 显示帮助信息
```

## 预期结果

### 成功响应示例

**纯文本消息响应：**
```json
{
  "success": true,
  "data": {
    "user_message": {
      "id": "uuid-string",
      "role": "User",
      "content": "你好，请介绍一下你的多模态能力"
    },
    "assistant_message": {
      "id": "uuid-string", 
      "role": "Assistant",
      "content": "我是一个多模态AI助手，可以理解和分析文本、图片等多种类型的内容..."
    }
  }
}
```

**多模态消息响应：**
```json
{
  "success": true,
  "data": {
    "user_message": {
      "id": "uuid-string",
      "role": "User",
      "content": "请分析这张图片的内容",
      "metadata": {
        "multimodal": true,
        "has_image": true
      }
    },
    "assistant_message": {
      "id": "uuid-string",
      "role": "Assistant", 
      "content": "这张图片显示了一个小女孩和一只狗在一起..."
    }
  }
}
```

### 流式响应示例

```
event: user_message
data: {"id": "uuid", "role": "User", "content": "请分析图片"}

event: ai_chunk
data: {"event_type": "chunk", "data": {"content": "这张", "delta": "这张"}}

event: ai_chunk
data: {"event_type": "chunk", "data": {"content": "这张图片", "delta": "图片"}}

event: ai_done
data: {"event_type": "done", "data": {"total_tokens": 25}}
```

## 故障排除

### 常见问题

1. **服务器未运行**
   ```
   ❌ 服务器未运行，请先启动后端服务
   ```
   解决方案：运行 `cargo run` 启动后端服务

2. **图片URL无法访问**
   ```json
   {
     "success": false,
     "error": {
       "code": "IMAGE_URL_INVALID",
       "message": "图片URL格式无效或无法访问"
     }
   }
   ```
   解决方案：检查图片URL是否可访问，使用HTTPS协议

3. **AI服务错误**
   ```json
   {
     "success": false,
     "error": {
       "code": "AI_SERVICE_ERROR",
       "message": "AI服务调用失败"
     }
   }
   ```
   解决方案：检查千问API配置和网络连接

### 调试技巧

1. **查看详细日志**
   ```bash
   RUST_LOG=debug cargo run
   ```

2. **测试API连通性**
   ```bash
   curl -s http://127.0.0.1:8080/health
   curl -s http://127.0.0.1:8080/api/health/ai
   ```

3. **验证JSON格式**
   ```bash
   echo '{"content": "test"}' | jq .
   ```

## 性能测试

### 并发测试
```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 -p multimodal_request.json -T application/json \
   http://127.0.0.1:8080/api/chat/multimodal
```

### 大图片测试
```bash
# 测试大尺寸图片处理
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {"type": "text", "text": "请分析这张高分辨率图片"},
      {"type": "image_url", "image_url": {"url": "https://example.com/large-image.jpg"}}
    ]
  }'
```

## 更多信息

- 完整API文档：[docs/api.md](../docs/api.md)
- 多模态功能实现：[MULTIMODAL_FEATURES.md](MULTIMODAL_FEATURES.md)
- 项目主页：[README.md](../README.md)
