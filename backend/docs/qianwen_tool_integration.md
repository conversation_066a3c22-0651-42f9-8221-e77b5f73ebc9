# 千问API工具调用集成 - 高德地图功能

## 概述

本文档描述了如何将现有的高德地图MCP (Model Context Protocol) 集成修改为千问API的tool调用方式。

## 实现要点

### 1. 千问API Tool调用格式

我们实现了符合千问API compatible-mode/v1/chat/completions端点的工具调用格式：

```rust
/// 千问API工具定义 - 符合OpenAI兼容格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QianwenTool {
    #[serde(rename = "type")]
    pub tool_type: String, // 固定为 "function"
    pub function: QianwenFunction,
}

/// 千问API函数定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QianwenFunction {
    pub name: String,
    pub description: String,
    pub parameters: serde_json::Value, // JSON Schema格式的参数定义
}
```

### 2. 高德地图工具集合

实现了`AmapTools`结构体，提供5个核心地图功能：

1. **POI搜索** (`search_nearby_pois`) - 搜索附近的兴趣点
2. **路线规划** (`plan_route`) - 规划两点间的路线
3. **天气查询** (`get_weather_info`) - 查询天气信息
4. **地理编码** (`geocode_address`) - 地址转坐标
5. **逆地理编码** (`reverse_geocode`) - 坐标转地址

### 3. 智能工具调用检测

- 自动检测用户消息中的旅行相关关键词
- 根据内容智能推断需要调用的工具
- 支持中文自然语言理解

### 4. 工具执行流程

1. **检测阶段**: 分析用户输入，判断是否需要启用工具调用
2. **调用阶段**: 向千问API发送带有工具定义的请求
3. **执行阶段**: 解析API响应中的工具调用，执行相应的高德地图API
4. **格式化阶段**: 将高德API结果格式化为用户友好的文本

### 5. 参数定义示例

POI搜索工具的参数定义：

```json
{
  "type": "object",
  "properties": {
    "location": {
      "type": "string",
      "description": "搜索位置，可以是地名、地址或经纬度坐标"
    },
    "keywords": {
      "type": "string", 
      "description": "搜索关键词，如'餐厅'、'景点'、'酒店'等"
    },
    "radius": {
      "type": "integer",
      "description": "搜索半径，单位为米，默认3000米",
      "default": 3000,
      "minimum": 100,
      "maximum": 50000
    }
  },
  "required": ["location", "keywords"]
}
```

## 核心代码修改

### 1. 聊天参数扩展

在`ChatParameters`中添加了`tools`字段：

```rust
pub struct ChatParameters {
    // ... 其他字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tools: Option<Vec<QianwenTool>>, // 千问API工具调用
}
```

### 2. 统一聊天接口

修改了`chat_multimodal`方法，支持工具调用：

```rust
pub async fn chat_multimodal(&self, messages: Vec<ChatMessage>) -> AppResult<String> {
    // 检查是否需要启用工具调用
    let should_use_tools = self.should_enable_tools(&messages);
    
    // 根据消息类型选择合适的API端点
    if has_images {
        self.chat_multimodal_with_tools(messages, should_use_tools).await
    } else {
        self.chat_text_with_tools(messages, should_use_tools).await
    }
}
```

### 3. 工具执行引擎

实现了完整的工具执行流程：

- `execute_tool_call()` - 工具调用分发器
- `execute_poi_search()` - POI搜索执行
- `execute_route_planning()` - 路线规划执行
- `execute_weather_query()` - 天气查询执行
- 等等...

## 配置要求

确保配置文件中启用了MCP集成（现在用于工具调用）：

```toml
[qianwen]
enable_mcp_integration = true
```

## 使用示例

用户输入："北京附近有什么好吃的餐厅？"

系统会：
1. 检测到旅行相关关键词
2. 启用工具调用模式
3. 调用`search_nearby_pois`工具
4. 返回格式化的POI搜索结果

## 兼容性

- 保持与现有AI聊天应用架构的兼容性
- 支持流式和非流式响应模式
- 向后兼容原有的MCP集成（作为降级方案）

## 测试

实现了完整的单元测试：
- `test_amap_tools()` - 测试工具定义
- `test_should_enable_tools()` - 测试工具调用检测
- `test_parse_tool_calls_from_text()` - 测试智能解析

## 注意事项

1. 所有代码注释使用中文
2. 错误处理完善，包含详细的错误信息
3. 日志记录完整，便于调试
4. 参数验证严格，确保API调用安全
