# 流式聊天 API 文档

## 概述

本文档描述了 AI 聊天应用中的流式响应功能，支持实时接收 AI 回复，提供更好的用户体验。

## 功能特性

1. **Server-Sent Events (SSE)** - 使用标准的 SSE 协议进行流式通信
2. **实时响应** - AI 回复以流的形式逐步返回，无需等待完整响应
3. **错误处理** - 完善的错误处理机制，包括网络错误和 API 错误
4. **内存存储兼容** - 与现有的 MemoryStorageService 完全兼容
5. **模拟流式** - 当 AI API 不支持真实流式时，自动降级到模拟流式效果

## API 端点

### 流式文本聊天

**端点**: `POST /api/chat/text/stream`

**请求头**:
```
Content-Type: application/json
Accept: text/event-stream
```

**请求体**:
```json
{
    "content": "你好，请介绍一下人工智能的发展历史",
    "conversation_id": "optional-uuid-here"
}
```

**响应格式**: Server-Sent Events (SSE)

**事件类型**:

1. **user_message** - 用户消息确认
```
event: user_message
data: {"id":"uuid","content":"用户消息内容","role":"user",...}
```

2. **ai_chunk** - AI 回复片段
```
event: ai_chunk
data: {"event_type":"chunk","data":{"content":"AI回复片段","is_final":false,"chunk_id":"chunk_0"}}
```

3. **ai_done** - AI 回复完成
```
event: ai_done
data: {"event_type":"done","data":{"total_tokens":150}}
```

4. **error** - 错误事件
```
event: error
data: "错误描述信息"
```

## 客户端使用示例

### JavaScript/TypeScript

```javascript
async function sendStreamingMessage(content, conversationId = null) {
    const response = await fetch('/api/chat/text/stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
            content: content,
            conversation_id: conversationId
        })
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('event: ')) {
                    const eventType = line.substring(7);
                    continue;
                }
                
                if (line.startsWith('data: ')) {
                    const data = line.substring(6);
                    
                    try {
                        const eventData = JSON.parse(data);
                        handleStreamEvent(eventType, eventData);
                    } catch (e) {
                        // 处理非 JSON 数据
                        handleStreamEvent(eventType, data);
                    }
                }
            }
        }
    } finally {
        reader.releaseLock();
    }
}

function handleStreamEvent(eventType, data) {
    switch (eventType) {
        case 'user_message':
            console.log('用户消息已发送:', data);
            break;
            
        case 'ai_chunk':
            // 逐步显示 AI 回复
            const chunk = data.data;
            appendToChat(chunk.content);
            break;
            
        case 'ai_done':
            console.log('AI 回复完成');
            break;
            
        case 'error':
            console.error('流式聊天错误:', data);
            break;
    }
}

function appendToChat(content) {
    // 将内容追加到聊天界面
    const chatContainer = document.getElementById('chat-messages');
    const lastMessage = chatContainer.lastElementChild;
    
    if (lastMessage && lastMessage.classList.contains('ai-message')) {
        lastMessage.textContent += content;
    } else {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'ai-message';
        messageDiv.textContent = content;
        chatContainer.appendChild(messageDiv);
    }
}
```

### EventSource API (更简单的方式)

```javascript
function sendStreamingMessageWithEventSource(content, conversationId = null) {
    // 注意：EventSource 不支持 POST 请求，需要使用 GET 或其他方式
    // 这里展示概念，实际使用时可能需要调整
    
    const url = new URL('/api/chat/text/stream', window.location.origin);
    url.searchParams.set('content', content);
    if (conversationId) {
        url.searchParams.set('conversation_id', conversationId);
    }

    const eventSource = new EventSource(url);

    eventSource.addEventListener('user_message', (event) => {
        const data = JSON.parse(event.data);
        console.log('用户消息已发送:', data);
    });

    eventSource.addEventListener('ai_chunk', (event) => {
        const data = JSON.parse(event.data);
        appendToChat(data.data.content);
    });

    eventSource.addEventListener('ai_done', (event) => {
        console.log('AI 回复完成');
        eventSource.close();
    });

    eventSource.addEventListener('error', (event) => {
        console.error('流式聊天错误:', event.data);
        eventSource.close();
    });

    eventSource.onerror = (error) => {
        console.error('EventSource 连接错误:', error);
        eventSource.close();
    };
}
```

## 实现细节

### 流式响应处理流程

1. **接收请求** - 服务器接收流式聊天请求
2. **保存用户消息** - 立即保存用户消息到内存存储
3. **发送用户消息事件** - 向客户端确认用户消息已接收
4. **调用 AI API** - 调用通义千问 API 获取流式响应
5. **处理 AI 流** - 逐步处理 AI 响应并发送给客户端
6. **保存 AI 消息** - 在流式响应完成后保存完整的 AI 消息

### 错误处理机制

1. **网络错误** - 自动重试和错误恢复
2. **API 错误** - 详细的错误信息返回
3. **解析错误** - 优雅的错误处理和用户提示
4. **连接中断** - 客户端重连机制

### 性能优化

1. **分块传输** - 合理的分块大小，平衡实时性和性能
2. **内存管理** - 及时释放不需要的资源
3. **连接池** - 复用 HTTP 连接减少开销

## 配置选项

在 `config/config.toml` 中可以配置相关参数：

```toml
[qianwen]
api_key = "your-api-key"
base_url = "https://dashscope.aliyuncs.com"
model_text = "qwen-turbo"
model_vision = "qwen-vl-plus"

[server]
host = "127.0.0.1"
port = 8080
```

## 注意事项

1. **浏览器兼容性** - 确保目标浏览器支持 EventSource 或 Fetch API
2. **网络稳定性** - 在不稳定的网络环境下可能需要重连机制
3. **资源清理** - 及时关闭不需要的连接避免资源泄漏
4. **安全考虑** - 实施适当的认证和授权机制

## 故障排除

### 常见问题

1. **连接超时** - 检查网络连接和服务器状态
2. **数据格式错误** - 验证 JSON 格式和事件结构
3. **内存泄漏** - 确保正确关闭流连接

### 调试技巧

1. 使用浏览器开发者工具监控网络请求
2. 检查服务器日志获取详细错误信息
3. 使用 curl 测试 API 端点的基本功能
