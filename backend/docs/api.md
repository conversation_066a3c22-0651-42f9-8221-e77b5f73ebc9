# AI聊天应用 API 文档

## 概述

本文档描述了AI聊天应用的完整API接口，包括文件管理、聊天对话、图像分析等功能。

## 基础信息

- **基础URL**: `http://localhost:3000`
- **API版本**: v1
- **认证方式**: 当前版本使用临时用户ID（开发阶段）
- **请求格式**: JSON / multipart/form-data
- **响应格式**: JSON

## 通用响应格式

所有API响应都遵循统一格式：

```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

错误响应：
```json
{
  "success": false,
  "data": null,
  "message": "错误描述",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 文件管理 API

### 1. 文件上传

#### 单文件上传
- **端点**: `POST /api/files/upload`
- **格式**: `multipart/form-data`
- **参数**: `file` (必需)
- **限制**: 最大10MB，支持图片、文档、音频、视频

#### 批量文件上传
- **端点**: `POST /api/files/upload/multiple`
- **格式**: `multipart/form-data`
- **参数**: 多个 `file` 字段

### 2. 文件访问

#### 文件下载
- **端点**: `GET /api/files/:filename/download`
- **功能**: 强制下载文件到本地
- **支持**: 所有文件类型
- **响应头**: `Content-Disposition: attachment`

#### 文件内联查看
- **端点**: `GET /api/files/:filename/view`
- **功能**: 在浏览器中直接显示文件
- **支持**: 仅图片文件 (image/*)
- **响应头**: `Content-Disposition: inline`
- **缓存**: `Cache-Control: public, max-age=3600`

**使用示例**:
```html
<!-- 在网页中显示上传的图片 -->
<img src="/api/files/example_image.jpg/view" alt="图片">

<!-- 下载链接 -->
<a href="/api/files/document.pdf/download">下载文档</a>
```

### 支持的文件类型

| 类型 | MIME类型 | 扩展名 | 查看支持 | 下载支持 |
|------|----------|--------|----------|----------|
| 图片 | image/jpeg, image/png, image/gif, image/webp | .jpg, .png, .gif, .webp | ✅ | ✅ |
| 文档 | application/pdf, text/plain, text/markdown | .pdf, .txt, .md | ❌ | ✅ |
| 音频 | audio/mpeg, audio/wav | .mp3, .wav | ❌ | ✅ |
| 视频 | video/mp4 | .mp4 | ❌ | ✅ |

## 聊天对话 API

### 1. 文本聊天

#### 普通聊天
- **端点**: `POST /api/chat/message`
- **功能**: 发送文本消息，获取AI回复

#### 流式聊天
- **端点**: `POST /api/chat/stream`
- **功能**: 实时流式接收AI回复
- **响应**: Server-Sent Events (SSE)

### 2. 多模态聊天

#### 图像分析
- **端点**: `POST /api/chat/image`
- **功能**: 分析图片内容，支持文字描述
- **参数**:
  - `image_url`: 图片URL（必需）
  - `prompt`: 分析提示词（可选）

**请求示例**:
```json
{
  "image_url": "https://example.com/image.jpg",
  "prompt": "请详细描述这张图片的内容"
}
```

**集成使用**（上传图片后分析）:
```json
{
  "image_url": "/api/files/uploaded_image.jpg/view",
  "prompt": "分析这张上传的图片"
}
```

## 错误代码

| HTTP状态码 | 错误代码 | 描述 |
|-----------|----------|------|
| 400 | BAD_REQUEST | 请求参数错误 |
| 401 | UNAUTHORIZED | 认证失败 |
| 404 | NOT_FOUND | 资源未找到 |
| 413 | PAYLOAD_TOO_LARGE | 文件大小超限 |
| 415 | UNSUPPORTED_MEDIA_TYPE | 不支持的文件类型 |
| 500 | INTERNAL_SERVER_ERROR | 服务器内部错误 |

## 使用流程示例

### 完整的图片分析流程

1. **上传图片**:
```bash
curl -X POST http://localhost:3000/api/files/upload \
  -F "file=@photo.jpg"
```

2. **获取文件名**（从上传响应中）:
```json
{
  "data": {
    "file": {
      "filename": "uuid_photo.jpg",
      "download_url": "/api/files/uuid_photo.jpg/download"
    }
  }
}
```

3. **分析图片**:
```bash
curl -X POST http://localhost:3000/api/chat/image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "/api/files/uuid_photo.jpg/view",
    "prompt": "请分析这张图片"
  }'
```

4. **在网页中显示**:
```html
<img src="/api/files/uuid_photo.jpg/view" alt="上传的图片">
```

## 开发注意事项

1. **认证**: 当前使用临时用户ID，生产环境需要实现完整认证
2. **存储**: 文件保存在 `./uploads/` 目录，元数据存储在内存中
3. **缓存**: 图片查看端点设置了1小时缓存
4. **安全**: 实现了文件类型验证和大小限制
5. **日志**: 所有操作都有详细的调试日志

## 测试工具

项目提供了测试脚本：
```bash
# 测试所有功能
python3 backend/test_fixes.py

# 测试特定功能
curl -X GET http://localhost:3000/health
```
