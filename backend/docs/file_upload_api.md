# 文件上传API文档

## 概述

本文档描述了AI聊天应用中的文件上传功能API接口。支持单文件和批量文件上传，集成了文件类型验证、大小限制等安全检查。

## API端点

### 1. 单文件上传

**端点**: `POST /api/files/upload`

**请求格式**: `multipart/form-data`

**请求参数**:
- `file`: 要上传的文件（必需）

**支持的文件类型**:
- 图片: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- 文档: `application/pdf`, `text/plain`, `text/markdown`
- 音频: `audio/mpeg`, `audio/wav`
- 视频: `video/mp4`

**文件大小限制**: 最大 10MB

**响应示例**:
```json
{
  "success": true,
  "data": {
    "file": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "filename": "example.jpg",
      "original_filename": "my_photo.jpg",
      "file_type": "Image",
      "mime_type": "image/jpeg",
      "file_size": 1024000,
      "file_path": "./uploads/550e8400-e29b-41d4-a716-446655440000_example.jpg",
      "download_url": "/api/files/550e8400-e29b-41d4-a716-446655440000_example.jpg/download",
      "status": "Active",
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    },
    "message": "文件上传成功"
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. 批量文件上传

**端点**: `POST /api/files/upload/multiple`

**请求格式**: `multipart/form-data`

**请求参数**:
- `files`: 要上传的多个文件（必需）
- 或者多个 `file` 字段

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "file": {
          "id": "550e8400-e29b-41d4-a716-446655440001",
          "filename": "document1.pdf",
          "original_filename": "report.pdf",
          "file_type": "Document",
          "mime_type": "application/pdf",
          "file_size": 2048000,
          "file_path": "./uploads/550e8400-e29b-41d4-a716-446655440001_document1.pdf",
          "download_url": "/api/files/550e8400-e29b-41d4-a716-446655440001_document1.pdf/download",
          "status": "Active",
          "created_at": "2024-01-01T12:00:00Z",
          "updated_at": "2024-01-01T12:00:00Z"
        },
        "message": "文件 report.pdf 上传成功"
      }
    ],
    "total_count": 3,
    "success_count": 2,
    "failed_count": 1
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 错误响应

### 文件类型不支持
```json
{
  "success": false,
  "data": null,
  "message": "不支持的文件类型: application/exe",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 文件大小超限
```json
{
  "success": false,
  "data": null,
  "message": "文件大小超过限制，最大允许 10 MB",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 缺少文件字段
```json
{
  "success": false,
  "data": null,
  "message": "未找到文件字段",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 使用示例

### cURL 示例

#### 单文件上传
```bash
curl -X POST http://localhost:3000/api/files/upload \
  -F "file=@/path/to/your/file.jpg" \
  -H "Content-Type: multipart/form-data"
```

#### 批量文件上传
```bash
curl -X POST http://localhost:3000/api/files/upload/multiple \
  -F "file=@/path/to/file1.jpg" \
  -F "file=@/path/to/file2.pdf" \
  -F "file=@/path/to/file3.txt" \
  -H "Content-Type: multipart/form-data"
```

### JavaScript 示例

#### 单文件上传
```javascript
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await fetch('/api/files/upload', {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('文件上传成功:', result.data.file);
    } else {
      console.error('上传失败:', result.message);
    }
  } catch (error) {
    console.error('上传错误:', error);
  }
};
```

#### 批量文件上传
```javascript
const uploadMultipleFiles = async (files) => {
  const formData = new FormData();
  
  for (let i = 0; i < files.length; i++) {
    formData.append('file', files[i]);
  }

  try {
    const response = await fetch('/api/files/upload/multiple', {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    if (result.success) {
      console.log(`成功上传 ${result.data.success_count} 个文件`);
      console.log('上传结果:', result.data.files);
    } else {
      console.error('上传失败:', result.message);
    }
  } catch (error) {
    console.error('上传错误:', error);
  }
};
```

## 技术实现

### 存储方式
- 当前使用内存存储 (`MemoryStorageService`)
- 文件元数据保存在内存中
- 支持按用户ID索引文件列表

### 安全特性
- 文件类型白名单验证
- 文件大小限制检查
- 唯一文件名生成（防止冲突）
- MIME类型验证

### 文件类型映射
系统会根据MIME类型自动确定文件类型：
- `image/*` → `FileType::Image`
- `audio/*` → `FileType::Audio`
- `video/*` → `FileType::Video`
- `application/pdf`, `text/*` → `FileType::Document`
- 其他 → `FileType::Other`

## 注意事项

1. **认证**: 当前版本使用临时用户ID，生产环境中应集成认证中间件
2. **存储**: 当前为内存存储，重启后数据丢失，生产环境建议使用持久化存储
3. **文件清理**: 暂未实现自动清理机制，需要手动管理存储空间
4. **并发**: 内存存储使用读写锁，支持并发访问

### 3. 文件下载

**端点**: `GET /api/files/:filename/download`

**功能**: 下载文件到本地，浏览器会提示保存文件

**URL参数**:
- `filename`: 文件名（从上传响应中获取）

**响应**:
- 返回文件的二进制内容
- 设置 `Content-Disposition: attachment` 头，强制下载
- 设置正确的 `Content-Type` 头

**示例**:
```bash
# 下载文件
curl -X GET "http://localhost:3000/api/files/550e8400-e29b-41d4-a716-446655440000_example.jpg/download" \
  --output "downloaded_file.jpg"
```

### 4. 文件内联查看

**端点**: `GET /api/files/:filename/view`

**功能**: 在浏览器中直接显示文件（主要用于图片）

**URL参数**:
- `filename`: 文件名（从上传响应中获取）

**支持的文件类型**: 仅支持图片文件
- `image/jpeg`
- `image/png`
- `image/gif`
- `image/webp`

**响应**:
- 返回文件的二进制内容
- 设置 `Content-Disposition: inline` 头，浏览器内联显示
- 设置正确的 `Content-Type` 头
- 添加缓存控制头 `Cache-Control: public, max-age=3600`

**示例**:
```bash
# 在浏览器中查看图片
curl -X GET "http://localhost:3000/api/files/550e8400-e29b-41d4-a716-446655440000_example.jpg/view"
```

**HTML中使用**:
```html
<!-- 直接在网页中显示图片 -->
<img src="/api/files/550e8400-e29b-41d4-a716-446655440000_example.jpg/view" alt="上传的图片">
```

**错误响应**（非图片文件）:
```json
{
  "success": false,
  "data": null,
  "message": "不支持的文件类型用于内联查看: application/pdf。仅支持图片文件。",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 端点对比

| 端点 | 用途 | Content-Disposition | 支持文件类型 | 浏览器行为 |
|------|------|-------------------|------------|-----------|
| `/download` | 文件下载 | `attachment` | 所有类型 | 提示保存文件 |
| `/view` | 内联查看 | `inline` | 仅图片 | 直接显示内容 |

## 未来扩展

- [ ] 文件删除接口
- [ ] 文件列表接口
- [ ] 文件分享功能
- [ ] 云存储集成
- [ ] 文件压缩和优化
- [ ] 病毒扫描集成
- [ ] 图片缩略图生成
