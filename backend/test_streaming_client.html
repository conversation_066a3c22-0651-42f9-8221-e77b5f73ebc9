<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式聊天测试客户端</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #fafafa;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .ai-message {
            background-color: #e9ecef;
            color: #333;
            margin-right: auto;
        }
        
        .system-message {
            background-color: #fff3cd;
            color: #856404;
            text-align: center;
            font-style: italic;
            margin: 0 auto;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        #messageInput {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        #sendButton {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        #sendButton:hover {
            background-color: #0056b3;
        }
        
        #sendButton:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status.sending {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .typing-indicator {
            display: none;
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }
        
        .typing-indicator.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 流式聊天测试客户端</h1>
        
        <div id="status" class="status">准备就绪</div>
        
        <div id="chatContainer" class="chat-container">
            <div class="message system-message">
                欢迎使用流式聊天测试客户端！请输入消息开始对话。
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            AI 正在思考中...
        </div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="输入您的消息..." />
            <button id="sendButton">发送</button>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
            <h3>使用说明：</h3>
            <ul>
                <li>输入消息后点击"发送"按钮或按 Enter 键</li>
                <li>AI 回复将以流式方式逐步显示</li>
                <li>支持多轮对话，会话状态会自动保持</li>
                <li>如果遇到错误，请检查后端服务是否正常运行</li>
            </ul>
        </div>
    </div>

    <script>
        let conversationId = null;
        let isStreaming = false;
        
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusDiv = document.getElementById('status');
        const typingIndicator = document.getElementById('typingIndicator');
        
        // 添加消息到聊天容器
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return messageDiv;
        }
        
        // 更新状态
        function updateStatus(message, type = '') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        // 发送流式消息
        async function sendStreamingMessage(content) {
            if (isStreaming) return;
            
            isStreaming = true;
            sendButton.disabled = true;
            updateStatus('正在发送消息...', 'sending');
            
            // 添加用户消息
            addMessage(content, 'user');
            
            try {
                const response = await fetch('/api/chat/text/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                    },
                    body: JSON.stringify({
                        content: content,
                        conversation_id: conversationId
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                updateStatus('正在接收 AI 回复...', 'connected');
                typingIndicator.classList.add('show');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let aiMessageDiv = null;
                let currentEvent = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('event: ')) {
                            currentEvent = line.substring(7).trim();
                            continue;
                        }
                        
                        if (line.startsWith('data: ')) {
                            const data = line.substring(6);
                            
                            try {
                                const eventData = JSON.parse(data);
                                handleStreamEvent(currentEvent, eventData);
                            } catch (e) {
                                // 处理非 JSON 数据
                                handleStreamEvent(currentEvent, data);
                            }
                        }
                    }
                }
                
                function handleStreamEvent(eventType, data) {
                    switch (eventType) {
                        case 'user_message':
                            console.log('用户消息已确认:', data);
                            break;
                            
                        case 'ai_chunk':
                            if (!aiMessageDiv) {
                                aiMessageDiv = addMessage('', 'ai');
                            }
                            
                            if (data.data && data.data.content) {
                                aiMessageDiv.textContent += data.data.content;
                                chatContainer.scrollTop = chatContainer.scrollHeight;
                            }
                            break;
                            
                        case 'ai_done':
                            console.log('AI 回复完成');
                            typingIndicator.classList.remove('show');
                            updateStatus('对话就绪', 'connected');
                            break;
                            
                        case 'error':
                            console.error('流式聊天错误:', data);
                            updateStatus(`错误: ${data}`, 'error');
                            typingIndicator.classList.remove('show');
                            break;
                    }
                }
                
            } catch (error) {
                console.error('发送消息失败:', error);
                updateStatus(`发送失败: ${error.message}`, 'error');
                typingIndicator.classList.remove('show');
            } finally {
                isStreaming = false;
                sendButton.disabled = false;
            }
        }
        
        // 发送按钮点击事件
        sendButton.addEventListener('click', () => {
            const content = messageInput.value.trim();
            if (content && !isStreaming) {
                sendStreamingMessage(content);
                messageInput.value = '';
            }
        });
        
        // 输入框回车事件
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !isStreaming) {
                const content = messageInput.value.trim();
                if (content) {
                    sendStreamingMessage(content);
                    messageInput.value = '';
                }
            }
        });
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('客户端已就绪，可以开始对话', 'connected');
            messageInput.focus();
        });
    </script>
</body>
</html>
