#!/bin/bash

# 多模态聊天API测试脚本
# 用于测试AI聊天应用的多模态功能

set -e

# 配置
BASE_URL="http://127.0.0.1:8080"
CONTENT_TYPE="Content-Type: application/json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_test_header() {
    echo -e "\n${YELLOW}🧪 $1${NC}"
    echo "----------------------------------------"
}

# 检查服务器状态
check_server() {
    print_info "检查服务器状态..."
    if curl -s "$BASE_URL/health" > /dev/null; then
        print_success "服务器运行正常"
    else
        print_error "服务器未运行，请先启动后端服务"
        exit 1
    fi
}

# 测试1: 健康检查
test_health_check() {
    print_test_header "测试1: 健康检查"
    
    print_info "基础健康检查"
    curl -s "$BASE_URL/health"
    echo
    
    print_info "AI服务健康检查"
    curl -s "$BASE_URL/api/health/ai" | jq .
    echo
}

# 测试2: 纯文本消息（使用多模态端点）
test_text_message() {
    print_test_header "测试2: 纯文本消息（多模态端点）"
    
    print_info "发送纯文本消息..."
    curl -X POST "$BASE_URL/api/chat/multimodal" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": "你好，我想了解一下多模态AI的能力，请简单介绍一下。",
            "stream": false
        }' | jq .
    
    print_success "纯文本消息测试完成"
}

# 测试3: 多模态消息（文本+图片）
test_multimodal_message() {
    print_test_header "测试3: 多模态消息（文本+图片）"
    
    print_info "发送多模态消息..."
    curl -X POST "$BASE_URL/api/chat/multimodal" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": [
                {
                    "type": "text",
                    "text": "请详细分析这张图片的内容，包括人物、动物、环境等元素"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                    }
                }
            ],
            "stream": false
        }' | jq .
    
    print_success "多模态消息测试完成"
}

# 测试4: 流式多模态响应
test_streaming_multimodal() {
    print_test_header "测试4: 流式多模态响应"
    
    print_info "发送流式多模态请求..."
    print_warning "注意：这将显示实时流式响应，按Ctrl+C停止"
    
    curl -X POST "$BASE_URL/api/chat/multimodal" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": [
                {
                    "type": "text",
                    "text": "请为这张图片写一个有趣的故事"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                    }
                }
            ],
            "stream": true
        }'
    
    echo
    print_success "流式响应测试完成"
}

# 测试5: 复杂多模态消息
test_complex_multimodal() {
    print_test_header "测试5: 复杂多模态消息"
    
    print_info "发送包含多个文本段落的复杂消息..."
    curl -X POST "$BASE_URL/api/chat/multimodal" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": [
                {
                    "type": "text",
                    "text": "我想让你帮我分析这张图片。"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                    }
                },
                {
                    "type": "text",
                    "text": "请从以下几个角度进行分析：1. 构图和色彩 2. 人物表情和动作 3. 整体氛围和情感表达"
                }
            ],
            "stream": false
        }' | jq .
    
    print_success "复杂多模态消息测试完成"
}

# 测试6: 错误处理
test_error_handling() {
    print_test_header "测试6: 错误处理"
    
    print_info "测试无效的内容格式..."
    curl -X POST "$BASE_URL/api/chat/multimodal" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": [
                {
                    "type": "invalid_type",
                    "text": "这是无效的内容类型"
                }
            ]
        }' | jq .
    
    echo
    print_info "测试无效的图片URL..."
    curl -X POST "$BASE_URL/api/chat/multimodal" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": [
                {
                    "type": "text",
                    "text": "请分析这张图片"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://invalid-url.com/nonexistent.jpg"
                    }
                }
            ]
        }' | jq .
    
    print_success "错误处理测试完成"
}

# 测试7: 向后兼容性
test_backward_compatibility() {
    print_test_header "测试7: 向后兼容性"
    
    print_info "测试旧的文本端点..."
    curl -X POST "$BASE_URL/api/chat/text" \
        -H "$CONTENT_TYPE" \
        -d '{
            "content": "这是使用旧端点的测试消息"
        }' | jq .
    
    echo
    print_info "测试旧的图像端点..."
    curl -X POST "$BASE_URL/api/chat/image" \
        -H "$CONTENT_TYPE" \
        -d '{
            "image_url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg",
            "prompt": "请分析这张图片"
        }' | jq .
    
    print_success "向后兼容性测试完成"
}

# 主函数
main() {
    echo -e "${GREEN}🚀 多模态聊天API测试开始${NC}"
    echo "测试目标: $BASE_URL"
    echo
    
    # 检查依赖
    if ! command -v jq &> /dev/null; then
        print_warning "jq未安装，JSON输出将不会格式化"
        print_info "安装jq: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
    fi
    
    # 执行测试
    check_server
    test_health_check
    test_text_message
    test_multimodal_message
    test_streaming_multimodal
    test_complex_multimodal
    test_error_handling
    test_backward_compatibility
    
    echo
    print_success "🎉 所有测试完成！"
    print_info "如需查看详细的API文档，请参考 docs/api.md"
}

# 处理命令行参数
case "${1:-}" in
    "health")
        check_server
        test_health_check
        ;;
    "text")
        check_server
        test_text_message
        ;;
    "multimodal")
        check_server
        test_multimodal_message
        ;;
    "stream")
        check_server
        test_streaming_multimodal
        ;;
    "complex")
        check_server
        test_complex_multimodal
        ;;
    "error")
        check_server
        test_error_handling
        ;;
    "compat")
        check_server
        test_backward_compatibility
        ;;
    "help"|"-h"|"--help")
        echo "多模态聊天API测试脚本"
        echo
        echo "用法: $0 [选项]"
        echo
        echo "选项:"
        echo "  health      只运行健康检查测试"
        echo "  text        只运行纯文本消息测试"
        echo "  multimodal  只运行多模态消息测试"
        echo "  stream      只运行流式响应测试"
        echo "  complex     只运行复杂多模态测试"
        echo "  error       只运行错误处理测试"
        echo "  compat      只运行向后兼容性测试"
        echo "  help        显示此帮助信息"
        echo
        echo "不带参数运行将执行所有测试"
        ;;
    *)
        main
        ;;
esac
