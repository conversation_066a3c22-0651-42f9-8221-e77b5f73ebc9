#!/bin/bash

# 测试脚本：验证AI聊天后端API端点
# Test script: Verify AI chat backend API endpoints

echo "🚀 开始测试AI聊天后端API..."
echo "🚀 Starting AI chat backend API tests..."

BASE_URL="http://127.0.0.1:8080"

# 测试健康检查端点
echo "📋 测试健康检查端点..."
echo "📋 Testing health check endpoint..."
curl -s "$BASE_URL/health" | jq '.' || echo "健康检查失败 / Health check failed"

echo -e "\n"

# 测试获取对话列表
echo "💬 测试获取对话列表..."
echo "💬 Testing get conversations..."
curl -s "$BASE_URL/api/conversations" | jq '.' || echo "获取对话列表失败 / Get conversations failed"

echo -e "\n"

# 测试创建新对话
echo "🆕 测试创建新对话..."
echo "🆕 Testing create conversation..."
CONVERSATION_RESPONSE=$(curl -s -X POST "$BASE_URL/api/conversations" \
  -H "Content-Type: application/json" \
  -d '{}')

echo "$CONVERSATION_RESPONSE" | jq '.'

# 提取对话ID
CONVERSATION_ID=$(echo "$CONVERSATION_RESPONSE" | jq -r '.data.id // empty')

if [ -n "$CONVERSATION_ID" ]; then
    echo "✅ 对话创建成功，ID: $CONVERSATION_ID"
    echo "✅ Conversation created successfully, ID: $CONVERSATION_ID"
    
    echo -e "\n"
    
    # 测试发送消息
    echo "📤 测试发送消息..."
    echo "📤 Testing send message..."
    MESSAGE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/conversations/$CONVERSATION_ID/messages" \
      -H "Content-Type: application/json" \
      -d '{
        "content": "你好，这是一个测试消息",
        "role": "user",
        "message_type": "text"
      }')
    
    echo "$MESSAGE_RESPONSE" | jq '.'
    
    echo -e "\n"
    
    # 测试获取对话消息
    echo "📥 测试获取对话消息..."
    echo "📥 Testing get conversation messages..."
    curl -s "$BASE_URL/api/conversations/$CONVERSATION_ID/messages" | jq '.' || echo "获取消息失败 / Get messages failed"
    
else
    echo "❌ 对话创建失败"
    echo "❌ Conversation creation failed"
fi

echo -e "\n"
echo "🎉 测试完成！"
echo "🎉 Tests completed!"
