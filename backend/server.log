warning: unused imports: `get` and `post`
 --> src/routes/auth.rs:5:15
  |
5 |     routing::{get, post},
  |               ^^^  ^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `delete`, `get`, and `post`
 --> src/routes/file.rs:5:15
  |
5 |     routing::{delete, get, post},
  |               ^^^^^^  ^^^  ^^^^

warning: unused import: `std::sync::Arc`
  --> src/routes/health.rs:10:5
   |
10 | use std::sync::Arc;
   |     ^^^^^^^^^^^^^^

warning: unused imports: `memory::MemoryStorageService`, `qianwen::QianwenService`, and `utils::config::Settings`
  --> src/routes/health.rs:14:16
   |
14 |     services::{memory::MemoryStorageService, qianwen::QianwenService},
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
15 |     utils::config::Settings,
   |     ^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `delete`, `get`, `post`, and `put`
 --> src/routes/user.rs:5:15
  |
5 |     routing::{delete, get, post, put},
  |               ^^^^^^  ^^^  ^^^^  ^^^

warning: unused imports: `Pool` and `Sqlite`
 --> src/services/database.rs:2:12
  |
2 | use sqlx::{Pool, Sqlite, SqlitePool};
  |            ^^^^  ^^^^^^

warning: unused variable: `qianwen`
  --> src/routes/health.rs:88:18
   |
88 |     State((_, _, qianwen)): State<AppState>,
   |                  ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_qianwen`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `qianwen`
   --> src/routes/health.rs:109:31
    |
109 |     State((settings, storage, qianwen)): State<AppState>,
    |                               ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_qianwen`

warning: variable does not need to be mutable
   --> src/routes/auth.rs:142:9
    |
142 |     let mut router = Router::new();
    |         ----^^^^^^
    |         |
    |         help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: variable does not need to be mutable
   --> src/routes/auth.rs:252:9
    |
252 |     let mut router = create_auth_routes();
    |         ----^^^^^^
    |         |
    |         help: remove this `mut`

warning: variable does not need to be mutable
  --> src/routes/conversation.rs:73:9
   |
73 |     let mut router = Router::new()
   |         ----^^^^^^
   |         |
   |         help: remove this `mut`

warning: variable does not need to be mutable
   --> src/routes/file.rs:106:9
    |
106 |     let mut router = Router::new();
    |         ----^^^^^^
    |         |
    |         help: remove this `mut`

warning: variable does not need to be mutable
   --> src/routes/user.rs:119:9
    |
119 |     let mut router = Router::new();
    |         ----^^^^^^
    |         |
    |         help: remove this `mut`

warning: struct `AuthUser` is never constructed
  --> src/middleware/auth.rs:17:12
   |
17 | pub struct AuthUser {
   |            ^^^^^^^^
   |
   = note: `AuthUser` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis
   = note: `#[warn(dead_code)]` on by default

warning: function `jwt_auth_middleware` is never used
  --> src/middleware/auth.rs:23:14
   |
23 | pub async fn jwt_auth_middleware(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `optional_jwt_auth_middleware` is never used
  --> src/middleware/auth.rs:66:14
   |
66 | pub async fn optional_jwt_auth_middleware(
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `admin_auth_middleware` is never used
  --> src/middleware/auth.rs:96:14
   |
96 | pub async fn admin_auth_middleware(request: Request, next: Next) -> AppResul...
   |              ^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_auth_user` is never used
   --> src/middleware/auth.rs:114:8
    |
114 | pub fn extract_auth_user(request: &Request) -> AppResult<&AuthUser> {
    |        ^^^^^^^^^^^^^^^^^

warning: function `check_resource_permission` is never used
   --> src/middleware/auth.rs:122:8
    |
122 | pub fn check_resource_permission(auth_user: &AuthUser, resource_owner_id: &...
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `token_refresh_middleware` is never used
   --> src/middleware/auth.rs:132:14
    |
132 | pub async fn token_refresh_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_permissive_cors_layer` is never used
  --> src/middleware/cors.rs:10:8
   |
10 | pub fn create_permissive_cors_layer() -> CorsLayer {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_production_cors_layer` is never used
  --> src/middleware/cors.rs:15:8
   |
15 | pub fn create_production_cors_layer(_allowed_origins: Vec<&str>) -> CorsLayer {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple variants are never constructed
  --> src/middleware/error.rs:37:5
   |
14 | pub enum AppError {
   |          -------- variants in this enum
...
37 |     UserNotFound,
   |     ^^^^^^^^^^^^
...
40 |     UserAlreadyExists,
   |     ^^^^^^^^^^^^^^^^^
...
43 |     InvalidPassword,
   |     ^^^^^^^^^^^^^^^
...
46 |     InvalidToken,
   |     ^^^^^^^^^^^^
...
49 |     TokenExpired,
   |     ^^^^^^^^^^^^
...
52 |     InsufficientPermissions,
   |     ^^^^^^^^^^^^^^^^^^^^^^^
...
58 |     MessageNotFound,
   |     ^^^^^^^^^^^^^^^
...
61 |     FileNotFound,
   |     ^^^^^^^^^^^^
...
64 |     UnsupportedFileType,
   |     ^^^^^^^^^^^^^^^^^^^
...
67 |     FileSizeExceeded,
   |     ^^^^^^^^^^^^^^^^
...
70 |     ApiCallFailed(String),
   |     ^^^^^^^^^^^^^
...
76 |     SpeechServiceError(String),
   |     ^^^^^^^^^^^^^^^^^^
...
82 |     BadRequest(String),
   |     ^^^^^^^^^^
   |
   = note: `AppError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: struct `ErrorHandler` is never constructed
   --> src/middleware/error.rs:214:12
    |
214 | pub struct ErrorHandler;
    |            ^^^^^^^^^^^^

warning: associated functions `handle_database_error`, `handle_validation_error`, `handle_file_upload_error`, and `handle_external_api_error` are never used
   --> src/middleware/error.rs:218:12
    |
216 | impl ErrorHandler {
    | ----------------- associated functions in this implementation
217 |     /// 处理数据库错误
218 |     pub fn handle_database_error(err: sqlx::Error) -> AppError {
    |            ^^^^^^^^^^^^^^^^^^^^^
...
233 |     pub fn handle_validation_error(
    |            ^^^^^^^^^^^^^^^^^^^^^^^
...
249 |     pub fn handle_file_upload_error(filename: &str, error: &str) -> AppError {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
...
255 |     pub fn handle_external_api_error(service: &str, error: &str) -> AppError {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `global_error_handler` is never used
   --> src/middleware/error.rs:262:14
    |
262 | pub async fn global_error_handler(err: AppError) -> Response {
    |              ^^^^^^^^^^^^^^^^^^^^

warning: function `performance_monitoring_middleware` is never used
   --> src/middleware/logging.rs:121:14
    |
121 | pub async fn performance_monitoring_middleware(request: Request, next: Next...
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `security_logging_middleware` is never used
   --> src/middleware/logging.rs:146:14
    |
146 | pub async fn security_logging_middleware(request: Request, next: Next) -> R...
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `check_suspicious_patterns` is never used
   --> src/middleware/logging.rs:203:4
    |
203 | fn check_suspicious_patterns(uri: &str, method: &str) -> bool {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `init_logging` is never used
   --> src/middleware/logging.rs:240:8
    |
240 | pub fn init_logging(log_level: &str) {
    |        ^^^^^^^^^^^^

warning: fields `username`, `email`, and `avatar_url` are never read
  --> src/models/user.rs:21:9
   |
20 | pub struct CreateUserRequest {
   |            ----------------- fields in this struct
21 |     pub username: String,
   |         ^^^^^^^^
22 |     pub email: String,
   |         ^^^^^
23 |     pub avatar_url: Option<String>,
   |         ^^^^^^^^^^
   |
   = note: `CreateUserRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `username`, `email`, and `avatar_url` are never read
  --> src/models/user.rs:29:9
   |
28 | pub struct UpdateUserRequest {
   |            ----------------- fields in this struct
29 |     pub username: Option<String>,
   |         ^^^^^^^^
30 |     pub email: Option<String>,
   |         ^^^^^
31 |     pub avatar_url: Option<String>,
   |         ^^^^^^^^^^
   |
   = note: `UpdateUserRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: methods `update`, `deactivate`, `activate`, and `to_response` are never used
   --> src/models/user.rs:85:12
    |
69  | impl User {
    | --------- methods in this implementation
...
85  |     pub fn update(&mut self, request: UpdateUserRequest) {
    |            ^^^^^^
...
99  |     pub fn deactivate(&mut self) {
    |            ^^^^^^^^^^
...
105 |     pub fn activate(&mut self) {
    |            ^^^^^^^^
...
111 |     pub fn to_response(self) -> UserResponse {
    |            ^^^^^^^^^^^

warning: fields `title` and `description` are never read
  --> src/models/conversation.rs:23:9
   |
22 | pub struct CreateConversationRequest {
   |            ------------------------- fields in this struct
23 |     pub title: Option<String>,
   |         ^^^^^
24 |     pub description: Option<String>,
   |         ^^^^^^^^^^^
   |
   = note: `CreateConversationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `title`, `description`, and `is_archived` are never read
  --> src/models/conversation.rs:30:9
   |
29 | pub struct UpdateConversationRequest {
   |            ------------------------- fields in this struct
30 |     pub title: Option<String>,
   |         ^^^^^
31 |     pub description: Option<String>,
   |         ^^^^^^^^^^^
32 |     pub is_archived: Option<bool>,
   |         ^^^^^^^^^^^
   |
   = note: `UpdateConversationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: methods `update`, `archive`, `unarchive`, `update_message_stats`, and `to_list_item` are never used
   --> src/models/conversation.rs:119:12
    |
99  | impl Conversation {
    | ----------------- methods in this implementation
...
119 |     pub fn update(&mut self, request: UpdateConversationRequest) {
    |            ^^^^^^
...
133 |     pub fn archive(&mut self) {
    |            ^^^^^^^
...
139 |     pub fn unarchive(&mut self) {
    |            ^^^^^^^^^
...
145 |     pub fn update_message_stats(&mut self, message_count: i32, last_message...
    |            ^^^^^^^^^^^^^^^^^^^^
...
164 |     pub fn to_list_item(self) -> ConversationListItem {
    |            ^^^^^^^^^^^^

warning: multiple fields are never read
  --> src/models/message.rs:43:9
   |
42 | pub struct CreateMessageRequest {
   |            -------------------- fields in this struct
43 |     pub conversation_id: Uuid,
   |         ^^^^^^^^^^^^^^^
44 |     pub role: MessageRole,
   |         ^^^^
45 |     pub message_type: MessageType,
   |         ^^^^^^^^^^^^
46 |     pub content: String,
   |         ^^^^^^^
47 |     pub metadata: Option<serde_json::Value>,
   |         ^^^^^^^^
48 |     pub file_id: Option<Uuid>,
   |         ^^^^^^^
49 |     pub parent_message_id: Option<Uuid>,
   |         ^^^^^^^^^^^^^^^^^
   |
   = note: `CreateMessageRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `content` and `metadata` are never read
  --> src/models/message.rs:55:9
   |
54 | pub struct UpdateMessageRequest {
   |            -------------------- fields in this struct
55 |     pub content: Option<String>,
   |         ^^^^^^^
56 |     pub metadata: Option<serde_json::Value>,
   |         ^^^^^^^^
   |
   = note: `UpdateMessageRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple associated items are never used
   --> src/models/message.rs:146:12
    |
116 | impl Message {
    | ------------ associated items in this implementation
...
146 |     pub fn from_request(request: CreateMessageRequest, user_id: Uuid) -> Se...
    |            ^^^^^^^^^^^^
...
160 |     pub fn update(&mut self, request: UpdateMessageRequest) {
    |            ^^^^^^
...
171 |     pub fn soft_delete(&mut self) {
    |            ^^^^^^^^^^^
...
177 |     pub fn restore(&mut self) {
    |            ^^^^^^^
...
183 |     pub fn is_user_message(&self) -> bool {
    |            ^^^^^^^^^^^^^^^
...
188 |     pub fn is_assistant_message(&self) -> bool {
    |            ^^^^^^^^^^^^^^^^^^^^
...
193 |     pub fn has_file(&self) -> bool {
    |            ^^^^^^^^
...
203 |     pub fn to_response_with_file(self, file_info: Option<FileInfo>) -> Mess...
    |            ^^^^^^^^^^^^^^^^^^^^^

warning: fields `filename`, `file_type`, `mime_type`, `file_size`, and `metadata` are never read
  --> src/models/file.rs:49:9
   |
48 | pub struct UploadFileRequest {
   |            ----------------- fields in this struct
49 |     pub filename: String,
   |         ^^^^^^^^
50 |     pub file_type: FileType,
   |         ^^^^^^^^^
51 |     pub mime_type: String,
   |         ^^^^^^^^^
52 |     pub file_size: i64,
   |         ^^^^^^^^^
53 |     pub metadata: Option<serde_json::Value>,
   |         ^^^^^^^^
   |
   = note: `UploadFileRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `filename`, `metadata`, and `status` are never read
  --> src/models/file.rs:59:9
   |
58 | pub struct UpdateFileRequest {
   |            ----------------- fields in this struct
59 |     pub filename: Option<String>,
   |         ^^^^^^^^
60 |     pub metadata: Option<serde_json::Value>,
   |         ^^^^^^^^
61 |     pub status: Option<FileStatus>,
   |         ^^^^^^
   |
   = note: `UpdateFileRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple associated items are never used
   --> src/models/file.rs:140:12
    |
138 | impl File {
    | --------- associated items in this implementation
139 |     /// 创建新文件记录
140 |     pub fn new(
    |            ^^^
...
172 |     pub fn from_upload_request(
    |            ^^^^^^^^^^^^^^^^^^^
...
193 |     pub fn update(&mut self, request: UpdateFileRequest) {
    |            ^^^^^^
...
207 |     pub fn mark_ready(&mut self, checksum: Option<String>) {
    |            ^^^^^^^^^^
...
214 |     pub fn mark_processing(&mut self) {
    |            ^^^^^^^^^^^^^^^
...
220 |     pub fn mark_failed(&mut self) {
    |            ^^^^^^^^^^^
...
226 |     pub fn mark_deleted(&mut self) {
    |            ^^^^^^^^^^^^
...
232 |     pub fn set_expiry(&mut self, expires_at: DateTime<Utc>) {
    |            ^^^^^^^^^^
...
238 |     pub fn is_expired(&self) -> bool {
    |            ^^^^^^^^^^
...
247 |     pub fn is_ready(&self) -> bool {
    |            ^^^^^^^^
...
252 |     pub fn is_image(&self) -> bool {
    |            ^^^^^^^^
...
257 |     pub fn is_audio(&self) -> bool {
    |            ^^^^^^^^
...
262 |     pub fn to_response(self) -> FileResponse {
    |            ^^^^^^^^^^^
...
267 |     pub fn to_list_item(self) -> FileListItem {
    |            ^^^^^^^^^^^^

warning: function `create_api_routes` is never used
  --> src/routes/mod.rs:50:8
   |
50 | pub fn create_api_routes() -> Router<AppState> {
   |        ^^^^^^^^^^^^^^^^^

warning: fields `enable_file_upload`, `enable_user_management`, `enable_authentication`, and `api_version` are never read
  --> src/routes/mod.rs:59:9
   |
57 | pub struct RouteConfig {
   |            ----------- fields in this struct
58 |     /// 是否启用文件上传功能
59 |     pub enable_file_upload: bool,
   |         ^^^^^^^^^^^^^^^^^^
60 |     /// 是否启用用户管理功能
61 |     pub enable_user_management: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^
62 |     /// 是否启用认证功能
63 |     pub enable_authentication: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^
64 |     /// API版本前缀
65 |     pub api_version: String,
   |         ^^^^^^^^^^^
   |
   = note: `RouteConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_conditional_routes` is never used
  --> src/routes/mod.rs:82:8
   |
82 | pub fn create_conditional_routes(config: RouteConfig) -> Router<AppState> {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
   --> src/routes/auth.rs:102:9
    |
100 | pub struct AuthRouteConfig {
    |            --------------- fields in this struct
101 |     /// 是否启用基础认证（用户名密码）
102 |     pub enable_basic_auth: bool,
    |         ^^^^^^^^^^^^^^^^^
103 |     /// 是否启用OAuth社交登录
104 |     pub enable_oauth: bool,
    |         ^^^^^^^^^^^^
105 |     /// 是否启用两步验证
106 |     pub enable_two_factor: bool,
    |         ^^^^^^^^^^^^^^^^^
107 |     /// 是否启用令牌刷新
108 |     pub enable_token_refresh: bool,
    |         ^^^^^^^^^^^^^^^^^^^^
109 |     /// 是否启用密码重置
110 |     pub enable_password_reset: bool,
    |         ^^^^^^^^^^^^^^^^^^^^^
111 |     /// 支持的OAuth提供商
112 |     pub oauth_providers: Vec<String>,
    |         ^^^^^^^^^^^^^^^
113 |     /// JWT令牌过期时间（小时）
114 |     pub token_expiration_hours: u64,
    |         ^^^^^^^^^^^^^^^^^^^^^^
115 |     /// 刷新令牌过期时间（天）
116 |     pub refresh_token_expiration_days: u64,
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: `AuthRouteConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_conditional_auth_routes` is never used
   --> src/routes/auth.rs:141:8
    |
141 | pub fn create_conditional_auth_routes(config: AuthRouteConfig) -> Router<Ap...
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_auth_routes_with_middleware` is never used
   --> src/routes/auth.rs:204:8
    |
204 | pub fn create_auth_routes_with_middleware() -> Router<AppState> {
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
   --> src/routes/auth.rs:217:9
    |
215 | pub struct AuthSecurityConfig {
    |            ------------------ fields in this struct
216 |     /// 最大登录尝试次数
217 |     pub max_login_attempts: u32,
    |         ^^^^^^^^^^^^^^^^^^
218 |     /// 账户锁定时间（分钟）
219 |     pub lockout_duration_minutes: u64,
    |         ^^^^^^^^^^^^^^^^^^^^^^^^
220 |     /// 是否启用CSRF保护
221 |     pub enable_csrf_protection: bool,
    |         ^^^^^^^^^^^^^^^^^^^^^^
222 |     /// 是否启用安全头
223 |     pub enable_security_headers: bool,
    |         ^^^^^^^^^^^^^^^^^^^^^^^
224 |     /// 是否启用审计日志
225 |     pub enable_audit_logging: bool,
    |         ^^^^^^^^^^^^^^^^^^^^
226 |     /// 密码最小长度
227 |     pub min_password_length: usize,
    |         ^^^^^^^^^^^^^^^^^^^
228 |     /// 是否要求强密码
229 |     pub require_strong_password: bool,
    |         ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: `AuthSecurityConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_auth_routes_with_security` is never used
   --> src/routes/auth.rs:249:8
    |
249 | pub fn create_auth_routes_with_security(
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `enable_image_chat`, `enable_debug_endpoints`, `enable_voice_chat`, and `enable_file_chat` are never read
  --> src/routes/chat.rs:43:9
   |
41 | pub struct ChatRouteConfig {
   |            --------------- fields in this struct
42 |     /// 是否启用图像聊天功能
43 |     pub enable_image_chat: bool,
   |         ^^^^^^^^^^^^^^^^^
44 |     /// 是否启用调试端点
45 |     pub enable_debug_endpoints: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^
46 |     /// 是否启用语音聊天功能（未来）
47 |     pub enable_voice_chat: bool,
   |         ^^^^^^^^^^^^^^^^^
48 |     /// 是否启用文件聊天功能（未来）
49 |     pub enable_file_chat: bool,
   |         ^^^^^^^^^^^^^^^^
   |
   = note: `ChatRouteConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_conditional_chat_routes` is never used
  --> src/routes/chat.rs:66:8
   |
66 | pub fn create_conditional_chat_routes(config: ChatRouteConfig) -> Router<App...
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_chat_routes_with_middleware` is never used
  --> src/routes/chat.rs:95:8
   |
95 | pub fn create_chat_routes_with_middleware() -> Router<AppState> {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `enable_conversation_crud`, `enable_conversation_archive`, `enable_conversation_export`, `enable_conversation_search`, and `enable_conversation_sharing` are never read
  --> src/routes/conversation.rs:44:9
   |
42 | pub struct ConversationRouteConfig {
   |            ----------------------- fields in this struct
43 |     /// 是否启用对话CRUD操作
44 |     pub enable_conversation_crud: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^
45 |     /// 是否启用对话归档功能
46 |     pub enable_conversation_archive: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
47 |     /// 是否启用对话导出功能
48 |     pub enable_conversation_export: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
49 |     /// 是否启用对话搜索功能
50 |     pub enable_conversation_search: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
51 |     /// 是否启用对话分享功能
52 |     pub enable_conversation_sharing: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `ConversationRouteConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_conditional_conversation_routes` is never used
  --> src/routes/conversation.rs:70:8
   |
70 | pub fn create_conditional_conversation_routes(
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_conversation_routes_with_middleware` is never used
   --> src/routes/conversation.rs:111:8
    |
111 | pub fn create_conversation_routes_with_middleware() -> Router<AppState> {
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_versioned_conversation_routes` is never used
   --> src/routes/conversation.rs:122:8
    |
122 | pub fn create_versioned_conversation_routes(version: &str) -> Router<AppSta...
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
  --> src/routes/file.rs:66:9
   |
64 | pub struct FileRouteConfig {
   |            --------------- fields in this struct
65 |     /// 是否启用文件上传功能
66 |     pub enable_file_upload: bool,
   |         ^^^^^^^^^^^^^^^^^^
67 |     /// 是否启用批量上传
68 |     pub enable_batch_upload: bool,
   |         ^^^^^^^^^^^^^^^^^^^
69 |     /// 是否启用文件预览
70 |     pub enable_file_preview: bool,
   |         ^^^^^^^^^^^^^^^^^^^
71 |     /// 是否启用文件分享
72 |     pub enable_file_sharing: bool,
   |         ^^^^^^^^^^^^^^^^^^^
73 |     /// 是否启用文件清理
74 |     pub enable_file_cleanup: bool,
   |         ^^^^^^^^^^^^^^^^^^^
75 |     /// 支持的文件类型
76 |     pub supported_file_types: Vec<String>,
   |         ^^^^^^^^^^^^^^^^^^^^
77 |     /// 最大文件大小（字节）
78 |     pub max_file_size: u64,
   |         ^^^^^^^^^^^^^
   |
   = note: `FileRouteConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_conditional_file_routes` is never used
   --> src/routes/file.rs:105:8
    |
105 | pub fn create_conditional_file_routes(config: FileRouteConfig) -> Router<Ap...
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_file_routes_with_middleware` is never used
   --> src/routes/file.rs:143:8
    |
143 | pub fn create_file_routes_with_middleware() -> Router<AppState> {
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `base_path` is never read
   --> src/routes/file.rs:156:13
    |
156 |     Local { base_path: String },
    |     -----   ^^^^^^^^^
    |     |
    |     field in this variant
    |
    = note: `FileStorageStrategy` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: variants `Cloud` and `Hybrid` are never constructed
   --> src/routes/file.rs:158:5
    |
154 | pub enum FileStorageStrategy {
    |          ------------------- variants in this enum
...
158 |     Cloud { bucket: String, region: String },
    |     ^^^^^
159 |     /// 混合存储策略
160 |     Hybrid { local_cache: bool },
    |     ^^^^^^
    |
    = note: `FileStorageStrategy` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_file_routes_with_storage` is never used
   --> src/routes/file.rs:174:8
    |
174 | pub fn create_file_routes_with_storage(
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
  --> src/routes/user.rs:86:9
   |
84 | pub struct UserRouteConfig {
   |            --------------- fields in this struct
85 |     /// 是否启用用户注册功能
86 |     pub enable_user_registration: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^
87 |     /// 是否启用邮箱验证
88 |     pub enable_email_verification: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^
89 |     /// 是否启用密码重置
90 |     pub enable_password_reset: bool,
   |         ^^^^^^^^^^^^^^^^^^^^^
91 |     /// 是否启用社交登录
92 |     pub enable_social_login: bool,
   |         ^^^^^^^^^^^^^^^^^^^
93 |     /// 是否启用用户头像上传
94 |     pub enable_avatar_upload: bool,
   |         ^^^^^^^^^^^^^^^^^^^^
95 |     /// 是否启用用户统计
96 |     pub enable_user_stats: bool,
   |         ^^^^^^^^^^^^^^^^^
97 |     /// 是否启用多因素认证
98 |     pub enable_mfa: bool,
   |         ^^^^^^^^^^
   |
   = note: `UserRouteConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_conditional_user_routes` is never used
   --> src/routes/user.rs:118:8
    |
118 | pub fn create_conditional_user_routes(config: UserRouteConfig) -> Router<Ap...
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_user_routes_with_middleware` is never used
   --> src/routes/user.rs:180:8
    |
180 | pub fn create_user_routes_with_middleware() -> Router<AppState> {
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `secret` and `expiration_hours` are never read
   --> src/routes/user.rs:194:9
    |
193 |     Jwt {
    |     --- fields in this variant
194 |         secret: String,
    |         ^^^^^^
195 |         expiration_hours: u64,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `UserAuthStrategy` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: variants `Session` and `OAuth` are never constructed
   --> src/routes/user.rs:198:5
    |
191 | pub enum UserAuthStrategy {
    |          ---------------- variants in this enum
...
198 |     Session {
    |     ^^^^^^^
...
202 |     OAuth {
    |     ^^^^^
    |
    = note: `UserAuthStrategy` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `create_user_routes_with_auth` is never used
   --> src/routes/user.rs:219:8
    |
219 | pub fn create_user_routes_with_auth(strategy: UserAuthStrategy) -> Router<A...
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `DatabaseService` is never constructed
  --> src/services/database.rs:12:12
   |
12 | pub struct DatabaseService {
   |            ^^^^^^^^^^^^^^^
   |
   = note: `DatabaseService` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: associated items `new`, `pool`, `migrate`, `health_check`, `get_stats`, and `cleanup_expired_data` are never used
  --> src/services/database.rs:18:18
   |
16 | impl DatabaseService {
   | -------------------- associated items in this implementation
17 |     /// 创建新的数据库服务实例
18 |     pub async fn new(settings: &Settings) -> AppResult<Self> {
   |                  ^^^
...
25 |     pub fn pool(&self) -> &SqlitePool {
   |            ^^^^
...
30 |     pub async fn migrate(&self) -> AppResult<()> {
   |                  ^^^^^^^
...
40 |     pub async fn health_check(&self) -> AppResult<()> {
   |                  ^^^^^^^^^^^^
...
50 |     pub async fn get_stats(&self) -> AppResult<DatabaseStats> {
   |                  ^^^^^^^^^
...
80 |     pub async fn cleanup_expired_data(&self) -> AppResult<CleanupResult> {
   |                  ^^^^^^^^^^^^^^^^^^^^

warning: function `create_connection_pool` is never used
   --> src/services/database.rs:111:10
    |
111 | async fn create_connection_pool(database_url: &str) -> AppResult<SqlitePool> {
    |          ^^^^^^^^^^^^^^^^^^^^^^

warning: struct `PaginationParams` is never constructed
   --> src/services/database.rs:144:12
    |
144 | pub struct PaginationParams {
    |            ^^^^^^^^^^^^^^^^
    |
    = note: `PaginationParams` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: associated items `new`, `offset`, and `limit` are never used
   --> src/services/database.rs:150:12
    |
149 | impl PaginationParams {
    | --------------------- associated items in this implementation
150 |     pub fn new(page: Option<u32>, per_page: Option<u32>) -> Self {
    |            ^^^
...
157 |     pub fn offset(&self) -> u32 {
    |            ^^^^^^
...
161 |     pub fn limit(&self) -> u32 {
    |            ^^^^^

warning: struct `SortParams` is never constructed
   --> src/services/database.rs:168:12
    |
168 | pub struct SortParams {
    |            ^^^^^^^^^^
    |
    = note: `SortParams` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: enum `SortDirection` is never used
   --> src/services/database.rs:174:10
    |
174 | pub enum SortDirection {
    |          ^^^^^^^^^^^^^
    |
    = note: `SortDirection` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: associated items `new` and `to_sql` are never used
   --> src/services/database.rs:180:12
    |
179 | impl SortParams {
    | --------------- associated items in this implementation
180 |     pub fn new(field: Option<String>, direction: Option<String>) -> Self {
    |            ^^^
...
190 |     pub fn to_sql(&self) -> String {
    |            ^^^^^^

warning: method `get_user_by_id` is never used
  --> src/services/memory.rs:58:18
   |
26 | impl MemoryStorageService {
   | ------------------------- method in this implementation
...
58 |     pub async fn get_user_by_id(&self, user_id: Uuid) -> AppResult<Option<Us...
   |                  ^^^^^^^^^^^^^^

warning: fields `usage` and `request_id` are never read
   --> src/services/qianwen.rs:100:9
    |
98  | pub struct QianwenResponse {
    |            --------------- fields in this struct
99  |     pub output: ResponseOutput,
100 |     pub usage: Option<Usage>,
    |         ^^^^^
101 |     pub request_id: String,
    |         ^^^^^^^^^^
    |
    = note: `QianwenResponse` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: field `finish_reason` is never read
   --> src/services/qianwen.rs:115:9
    |
113 | pub struct Choice {
    |            ------ field in this struct
114 |     pub message: ChatMessage,
115 |     pub finish_reason: String,
    |         ^^^^^^^^^^^^^
    |
    = note: `Choice` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `input_tokens`, `output_tokens`, and `total_tokens` are never read
   --> src/services/qianwen.rs:121:9
    |
120 | pub struct Usage {
    |            ----- fields in this struct
121 |     pub input_tokens: u32,
    |         ^^^^^^^^^^^^
122 |     pub output_tokens: u32,
    |         ^^^^^^^^^^^^^
123 |     pub total_tokens: u32,
    |         ^^^^^^^^^^^^
    |
    = note: `Usage` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: variant `Error` is never constructed
   --> src/services/qianwen.rs:146:5
    |
144 | pub enum StreamEventData {
    |          --------------- variant in this enum
145 |     Chunk(StreamChunk),
146 |     Error { message: String },
    |     ^^^^^
    |
    = note: `StreamEventData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `code`, `message`, and `request_id` are never read
   --> src/services/qianwen.rs:153:9
    |
152 | pub struct QianwenError {
    |            ------------ fields in this struct
153 |     pub code: String,
    |         ^^^^
154 |     pub message: String,
    |         ^^^^^^^
155 |     pub request_id: Option<String>,
    |         ^^^^^^^^^^
    |
    = note: `QianwenError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: associated items `health_check`, `parse_sse_chunk_static`, and `parse_sse_chunk` are never used
   --> src/services/qianwen.rs:313:18
    |
158 | impl QianwenService {
    | ------------------- associated items in this implementation
...
313 |     pub async fn health_check(&self) -> AppResult<()> {
    |                  ^^^^^^^^^^^^
...
642 |     fn parse_sse_chunk_static(chunk_str: &str) -> Result<StreamEvent, AppEr...
    |        ^^^^^^^^^^^^^^^^^^^^^^
...
828 |     fn parse_sse_chunk(&self, chunk_str: &str) -> Result<StreamEvent, AppEr...
    |        ^^^^^^^^^^^^^^^

warning: fields `database`, `upload`, `speech`, and `logging` are never read
  --> src/utils/config.rs:9:9
   |
7  | pub struct Settings {
   |            -------- fields in this struct
8  |     pub server: ServerConfig,
9  |     pub database: DatabaseConfig,
   |         ^^^^^^^^
...
12 |     pub upload: UploadConfig,
   |         ^^^^^^
13 |     pub speech: SpeechConfig,
   |         ^^^^^^
14 |     pub logging: LoggingConfig,
   |         ^^^^^^^
   |
   = note: `Settings` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: field `static_dir` is never read
  --> src/utils/config.rs:21:9
   |
18 | pub struct ServerConfig {
   |            ------------ field in this struct
...
21 |     pub static_dir: String,
   |         ^^^^^^^^^^
   |
   = note: `ServerConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: field `url` is never read
  --> src/utils/config.rs:26:9
   |
25 | pub struct DatabaseConfig {
   |            -------------- field in this struct
26 |     pub url: String,
   |         ^^^
   |
   = note: `DatabaseConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: field `jwt_expiration_hours` is never read
  --> src/utils/config.rs:40:9
   |
38 | pub struct AuthConfig {
   |            ---------- field in this struct
39 |     pub jwt_secret: String,
40 |     pub jwt_expiration_hours: u64,
   |         ^^^^^^^^^^^^^^^^^^^^
   |
   = note: `AuthConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `max_file_size`, `allowed_image_types`, `allowed_audio_types`, and `upload_dir` are never read
  --> src/utils/config.rs:45:9
   |
44 | pub struct UploadConfig {
   |            ------------ fields in this struct
45 |     pub max_file_size: u64,
   |         ^^^^^^^^^^^^^
46 |     pub allowed_image_types: Vec<String>,
   |         ^^^^^^^^^^^^^^^^^^^
47 |     pub allowed_audio_types: Vec<String>,
   |         ^^^^^^^^^^^^^^^^^^^
48 |     pub upload_dir: String,
   |         ^^^^^^^^^^
   |
   = note: `UploadConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `stt_provider`, `tts_provider`, `stt_api_key`, and `tts_api_key` are never read
  --> src/utils/config.rs:53:9
   |
52 | pub struct SpeechConfig {
   |            ------------ fields in this struct
53 |     pub stt_provider: Option<String>,
   |         ^^^^^^^^^^^^
54 |     pub tts_provider: Option<String>,
   |         ^^^^^^^^^^^^
55 |     pub stt_api_key: Option<String>,
   |         ^^^^^^^^^^^
56 |     pub tts_api_key: Option<String>,
   |         ^^^^^^^^^^^
   |
   = note: `SpeechConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: field `level` is never read
  --> src/utils/config.rs:61:9
   |
60 | pub struct LoggingConfig {
   |            ------------- field in this struct
61 |     pub level: String,
   |         ^^^^^
   |
   = note: `LoggingConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: method `database_url` is never used
  --> src/utils/config.rs:78:12
   |
64 | impl Settings {
   | ------------- method in this implementation
...
78 |     pub fn database_url(&self) -> &str {
   |            ^^^^^^^^^^^^

warning: struct `ValidationResult` is never constructed
 --> src/utils/validation.rs:8:12
  |
8 | pub struct ValidationResult {
  |            ^^^^^^^^^^^^^^^^
  |
  = note: `ValidationResult` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: associated items `new`, `add_error`, and `to_json` are never used
  --> src/utils/validation.rs:14:12
   |
13 | impl ValidationResult {
   | --------------------- associated items in this implementation
14 |     pub fn new() -> Self {
   |            ^^^
...
21 |     pub fn add_error(&mut self, field: &str, message: &str) {
   |            ^^^^^^^^^
...
29 |     pub fn to_json(&self) -> Value {
   |            ^^^^^^^

warning: struct `Validator` is never constructed
  --> src/utils/validation.rs:37:12
   |
37 | pub struct Validator;
   |            ^^^^^^^^^

warning: multiple associated functions are never used
   --> src/utils/validation.rs:41:12
    |
39  | impl Validator {
    | -------------- associated functions in this implementation
40  |     /// 验证邮箱格式
41  |     pub fn validate_email(email: &str) -> bool {
    |            ^^^^^^^^^^^^^^
...
47  |     pub fn validate_password(password: &str) -> ValidationResult {
    |            ^^^^^^^^^^^^^^^^^
...
78  |     pub fn validate_username(username: &str) -> ValidationResult {
    |            ^^^^^^^^^^^^^^^^^
...
103 |     pub fn validate_file_type(filename: &str, allowed_types: &[String]) -> ...
    |            ^^^^^^^^^^^^^^^^^^
...
116 |     pub fn validate_file_size(size: u64, max_size: u64) -> bool {
    |            ^^^^^^^^^^^^^^^^^^
...
121 |     pub fn validate_message_content(content: &str) -> ValidationResult {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^
...
146 |     pub fn validate_pagination(page: Option<u32>, per_page: Option<u32>) ->...
    |            ^^^^^^^^^^^^^^^^^^^
...
168 |     pub fn sanitize_html(input: &str) -> String {
    |            ^^^^^^^^^^^^^
...
175 |     pub fn sanitize_sql_input(input: &str) -> String {
    |            ^^^^^^^^^^^^^^^^^^
...
186 |     pub fn validate_uuid(uuid_str: &str) -> bool {
    |            ^^^^^^^^^^^^^
...
191 |     pub fn validate_phone_number(phone: &str) -> bool {
    |            ^^^^^^^^^^^^^^^^^^^^^

warning: function `validate_user_registration` is never used
   --> src/utils/validation.rs:198:8
    |
198 | pub fn validate_user_registration(
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: associated items `new` and `is_expired` are never used
  --> src/utils/crypto.rs:20:12
   |
18 | impl Claims {
   | ----------- associated items in this implementation
19 |     /// 创建新的JWT声明
20 |     pub fn new(user_id: String, username: String, expiration_hours: u64) -> ...
   |            ^^^
...
34 |     pub fn is_expired(&self) -> bool {
   |            ^^^^^^^^^^

warning: struct `PasswordUtils` is never constructed
  --> src/utils/crypto.rs:40:12
   |
40 | pub struct PasswordUtils;
   |            ^^^^^^^^^^^^^

warning: associated functions `hash_password`, `verify_password`, and `generate_random_password` are never used
  --> src/utils/crypto.rs:44:12
   |
42 | impl PasswordUtils {
   | ------------------ associated functions in this implementation
43 |     /// 哈希密码
44 |     pub fn hash_password(password: &str) -> Result<String, bcrypt::BcryptErr...
   |            ^^^^^^^^^^^^^
...
49 |     pub fn verify_password(password: &str, hash: &str) -> Result<bool, bcryp...
   |            ^^^^^^^^^^^^^^^
...
54 |     pub fn generate_random_password(length: usize) -> String {
   |            ^^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `JwtUtils` is never constructed
  --> src/utils/crypto.rs:72:12
   |
72 | pub struct JwtUtils;
   |            ^^^^^^^^

warning: associated functions `generate_token`, `verify_token`, `extract_user_id`, `extract_username`, `is_token_expiring_soon`, and `refresh_token_if_needed` are never used
   --> src/utils/crypto.rs:76:12
    |
74  | impl JwtUtils {
    | ------------- associated functions in this implementation
75  |     /// 生成JWT令牌
76  |     pub fn generate_token(
    |            ^^^^^^^^^^^^^^
...
90  |     pub fn verify_token(
    |            ^^^^^^^^^^^^
...
101 |     pub fn extract_user_id(token: &str, secret: &str) -> Result<String, jso...
    |            ^^^^^^^^^^^^^^^
...
107 |     pub fn extract_username(token: &str, secret: &str) -> Result<String, js...
    |            ^^^^^^^^^^^^^^^^
...
113 |     pub fn is_token_expiring_soon(token: &str, secret: &str) -> Result<bool...
    |            ^^^^^^^^^^^^^^^^^^^^^^
...
122 |     pub fn refresh_token_if_needed(
    |            ^^^^^^^^^^^^^^^^^^^^^^^

warning: associated function `success_with_message` is never used
  --> src/utils/response.rs:45:12
   |
30 | / impl<T> ApiResponse<T>
31 | | where
32 | |     T: Serialize,
   | |_________________- associated function in this implementation
...
45 |       pub fn success_with_message(data: T, message: String) -> Self {
   |              ^^^^^^^^^^^^^^^^^^^^

warning: associated functions `error_with_details`, `ok`, and `ok_with_message` are never used
  --> src/utils/response.rs:71:12
   |
55 | impl ApiResponse<()> {
   | -------------------- associated functions in this implementation
...
71 |     pub fn error_with_details(code: String, message: String, details: serde_...
   |            ^^^^^^^^^^^^^^^^^^
...
85 |     pub fn ok() -> Self {
   |            ^^
...
95 |     pub fn ok_with_message(message: String) -> Self {
   |            ^^^^^^^^^^^^^^^

warning: struct `ErrorResponses` is never constructed
   --> src/utils/response.rs:144:12
    |
144 | pub struct ErrorResponses;
    |            ^^^^^^^^^^^^^^

warning: associated functions `bad_request`, `unauthorized`, `forbidden`, `not_found`, `internal_error`, and `validation_error` are never used
   --> src/utils/response.rs:147:12
    |
146 | impl ErrorResponses {
    | ------------------- associated functions in this implementation
147 |     pub fn bad_request(message: &str) -> Response {
    |            ^^^^^^^^^^^
...
151 |     pub fn unauthorized() -> Response {
    |            ^^^^^^^^^^^^
...
155 |     pub fn forbidden() -> Response {
    |            ^^^^^^^^^
...
159 |     pub fn not_found(resource: &str) -> Response {
    |            ^^^^^^^^^
...
168 |     pub fn internal_error() -> Response {
    |            ^^^^^^^^^^^^^^
...
177 |     pub fn validation_error(details: serde_json::Value) -> Response {
    |            ^^^^^^^^^^^^^^^^

warning: associated function `new` is never used
   --> src/utils/response.rs:199:12
    |
198 | impl<T> PaginatedResponse<T> {
    | ---------------------------- associated function in this implementation
199 |     pub fn new(items: Vec<T>, total: u64, page: u32, per_page: u32) -> Self {
    |            ^^^

warning: `ai_chat_backend` (bin "ai_chat_backend") generated 107 warnings (run `cargo fix --bin "ai_chat_backend"` to apply 11 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.48s
     Running `target/debug/ai_chat_backend`
💾 内存存储服务初始化完成（开发模式）
🤖 通义千问API服务初始化完成
🚀 AI聊天服务器启动在 http://127.0.0.1:8080
📋 配置文件已加载
Error: Os { code: 48, kind: AddrInUse, message: "Address already in use" }
