[package]
name = "ai_chat_backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = { version = "0.7.9", features = ["multipart"] }
tokio = { version = "1.40", features = ["full"] }
tokio-stream = "0.1.16"
futures = "0.3.31"
tower = "0.4.13"
tower-http = { version = "0.5.2", features = ["cors", "fs", "trace"] }

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls",
    "sqlite",
    "chrono",
    "uuid",
] }

# HTTP客户端
reqwest = { version = "0.11", features = ["json", "multipart", "stream"] }

# 认证和安全
jsonwebtoken = "9.0"
bcrypt = "0.15"

# 配置管理
config = "0.14"
toml = "0.8"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 文件处理
mime = "0.3"
mime_guess = "2.0"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 正则表达式
regex = "1.0"

# 随机数生成
rand = "0.8"
sha2 = "0.10.9"
