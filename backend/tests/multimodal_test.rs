// 多模态聊天功能测试
use ai_chat_backend::services::qianwen::{ChatMessage, MessageContent, ContentPart, ContentData, ImageUrl};
use serde_json;

#[test]
fn test_text_message_creation() {
    // 测试纯文本消息创建
    let msg = ChatMessage::text("user", "你好，世界！");
    
    assert_eq!(msg.role, "user");
    assert_eq!(msg.get_text_content(), "你好，世界！");
    assert!(!msg.has_image());
    
    // 测试序列化
    let json = serde_json::to_string(&msg).unwrap();
    println!("文本消息JSON: {}", json);
    
    // 验证JSON包含正确的字段
    assert!(json.contains("\"role\":\"user\""));
    assert!(json.contains("你好，世界！"));
}

#[test]
fn test_multimodal_message_creation() {
    // 测试多模态消息创建
    let msg = ChatMessage::multimodal(
        "user", 
        "请分析这张图片", 
        "https://example.com/image.jpg"
    );
    
    assert_eq!(msg.role, "user");
    assert_eq!(msg.get_text_content(), "请分析这张图片");
    assert!(msg.has_image());
    
    // 测试序列化
    let json = serde_json::to_string(&msg).unwrap();
    println!("多模态消息JSON: {}", json);
    
    // 验证JSON包含正确的字段
    assert!(json.contains("\"role\":\"user\""));
    assert!(json.contains("\"type\":\"text\""));
    assert!(json.contains("\"type\":\"image_url\""));
    assert!(json.contains("请分析这张图片"));
    assert!(json.contains("https://example.com/image.jpg"));
}

#[test]
fn test_message_content_variants() {
    // 测试文本内容
    let text_content = MessageContent::Text("纯文本消息".to_string());
    let text_msg = ChatMessage {
        role: "user".to_string(),
        content: text_content,
    };
    
    assert_eq!(text_msg.get_text_content(), "纯文本消息");
    assert!(!text_msg.has_image());
    
    // 测试多模态内容
    let multimodal_content = MessageContent::Multimodal(vec![
        ContentPart {
            content_type: "text".to_string(),
            data: ContentData::Text {
                text: "描述文本".to_string(),
            },
        },
        ContentPart {
            content_type: "image_url".to_string(),
            data: ContentData::ImageUrl {
                image_url: ImageUrl {
                    url: "https://example.com/test.png".to_string(),
                },
            },
        },
    ]);
    
    let multimodal_msg = ChatMessage {
        role: "user".to_string(),
        content: multimodal_content,
    };
    
    assert_eq!(multimodal_msg.get_text_content(), "描述文本");
    assert!(multimodal_msg.has_image());
}

#[test]
fn test_legacy_message_conversion() {
    // 测试从旧版本消息格式转换
    let legacy_msg = ChatMessage::from_legacy(
        "assistant".to_string(),
        "这是一个助手回复".to_string()
    );
    
    assert_eq!(legacy_msg.role, "assistant");
    assert_eq!(legacy_msg.get_text_content(), "这是一个助手回复");
    assert!(!legacy_msg.has_image());
}

#[test]
fn test_openai_format_conversion() {
    use ai_chat_backend::utils::config::QianwenConfig;
    use ai_chat_backend::services::qianwen::QianwenService;
    
    let config = QianwenConfig {
        api_key: "test_key".to_string(),
        base_url: "https://dashscope.aliyuncs.com".to_string(),
        model_text: "qwen-turbo".to_string(),
        model_vision: "qwen-vl-plus".to_string(),
    };
    
    let service = QianwenService::new(config).unwrap();
    
    // 测试文本消息转换
    let text_msg = ChatMessage::text("user", "测试消息");
    let text_openai = service.convert_to_openai_format(text_msg);
    
    assert_eq!(text_openai["role"], "user");
    assert_eq!(text_openai["content"], "测试消息");
    
    // 测试多模态消息转换
    let multimodal_msg = ChatMessage::multimodal(
        "user",
        "分析图片",
        "https://example.com/image.jpg"
    );
    let multimodal_openai = service.convert_to_openai_format(multimodal_msg);
    
    assert_eq!(multimodal_openai["role"], "user");
    assert!(multimodal_openai["content"].is_array());
    
    let content_array = multimodal_openai["content"].as_array().unwrap();
    assert_eq!(content_array.len(), 2);
    
    // 验证文本部分
    let text_part = &content_array[0];
    assert_eq!(text_part["type"], "text");
    assert_eq!(text_part["text"], "分析图片");
    
    // 验证图片部分
    let image_part = &content_array[1];
    assert_eq!(image_part["type"], "image_url");
    assert_eq!(image_part["image_url"]["url"], "https://example.com/image.jpg");
}

#[test]
fn test_message_serialization_deserialization() {
    // 测试消息的序列化和反序列化
    let original_msg = ChatMessage::multimodal(
        "user",
        "请描述这张图片的内容",
        "https://example.com/sample.jpg"
    );
    
    // 序列化
    let json_str = serde_json::to_string(&original_msg).unwrap();
    println!("序列化结果: {}", json_str);
    
    // 反序列化
    let deserialized_msg: ChatMessage = serde_json::from_str(&json_str).unwrap();
    
    // 验证反序列化结果
    assert_eq!(deserialized_msg.role, original_msg.role);
    assert_eq!(deserialized_msg.get_text_content(), original_msg.get_text_content());
    assert_eq!(deserialized_msg.has_image(), original_msg.has_image());
}
