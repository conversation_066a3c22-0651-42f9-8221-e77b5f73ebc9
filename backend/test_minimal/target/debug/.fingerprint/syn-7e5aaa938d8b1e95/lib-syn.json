{"rustc": 5357548097637079788, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 4079388709775165775, "deps": [[1988483478007900009, "unicode_ident", false, 9651516684033817227], [3060637413840920116, "proc_macro2", false, 15371429859082410523], [17990358020177143287, "quote", false, 587853837814654211]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-7e5aaa938d8b1e95/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}