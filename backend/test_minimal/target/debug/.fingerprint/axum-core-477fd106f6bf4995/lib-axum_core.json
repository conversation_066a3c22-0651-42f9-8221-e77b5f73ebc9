{"rustc": 5357548097637079788, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 8276155916380437441, "path": 7665204502932618834, "deps": [[784494742817713399, "tower_service", false, 5057450961347251396], [1906322745568073236, "pin_project_lite", false, 12259467001724530771], [2517136641825875337, "sync_wrapper", false, 5357264397684343150], [7712452662827335977, "tower_layer", false, 8719612518752474785], [7858942147296547339, "rustversion", false, 18398547437516639477], [8606274917505247608, "tracing", false, 16930723477228665250], [9010263965687315507, "http", false, 14333727930744541344], [10229185211513642314, "mime", false, 9805488477205722694], [10629569228670356391, "futures_util", false, 16971651155996544480], [11946729385090170470, "async_trait", false, 8378084303939783657], [14084095096285906100, "http_body", false, 14356863346631132513], [16066129441945555748, "bytes", false, 1860887263742336947], [16900715236047033623, "http_body_util", false, 9807135964786229552]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-477fd106f6bf4995/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}