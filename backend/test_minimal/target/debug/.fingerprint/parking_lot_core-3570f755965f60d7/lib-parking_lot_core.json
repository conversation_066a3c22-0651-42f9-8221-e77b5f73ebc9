{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 8276155916380437441, "path": 9648656586626084042, "deps": [[2828590642173593838, "cfg_if", false, 5190443575508915530], [3666196340704888985, "smallvec", false, 1793547372503254466], [4269498962362888130, "build_script_build", false, 4397147639933269639], [4684437522915235464, "libc", false, 1686718363861009207]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot_core-3570f755965f60d7/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}