[package]
name = "ai_chat_backend"
version = "0.1.0"
edition = "2021"
default-run = "ai_chat_backend"

[dependencies]
# Web框架
axum = { version = "0.8.4", features = ["multipart"] }
tokio = { version = "1.40", features = ["full"] }
tokio-stream = "0.1.16"
tokio-util = { version = "0.7", features = ["io"] }
futures = "0.3.31"
futures-util = "0.3.31"
async-stream = "0.3"
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["cors", "fs", "trace"] }

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库
sqlx = { version = "0.8", features = [
    "runtime-tokio-rustls",
    "sqlite",
    "chrono",
    "uuid",
] }

# HTTP客户端
reqwest = { version = "0.12", features = ["json", "multipart", "stream"] }

# 认证和安全
jsonwebtoken = "9.0"
bcrypt = "0.17"

# 配置管理
config = "0.15"
toml = "0.9"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "fmt",
    "time",
] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 文件处理
mime = "0.3"
mime_guess = "2.0"

# 错误处理
anyhow = "1.0"
thiserror = "2.0"

# 正则表达式
regex = "1.0"

# 随机数生成
rand = "0.9"
sha2 = "0.10.9"

# URL编码
urlencoding = "2.1"

[dev-dependencies]
# 测试依赖
axum-test = "15.0"
tokio-test = "0.4"

[[example]]
name = "multimodal_test"
path = "examples/multimodal_test.rs"

[[example]]
name = "mcp_integration_test"
path = "examples/mcp_integration_test.rs"

[[example]]
name = "mcp_protocol_test"
path = "examples/mcp_protocol_test.rs"
