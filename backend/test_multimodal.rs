// 简单的多模态功能测试
use serde::{Serialize, Deserialize};

/// 聊天消息 - 支持多模态内容
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: String, // "user", "assistant", "system"
    #[serde(flatten)]
    pub content: MessageContent,
}

/// 消息内容 - 支持文本和多模态格式
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(untagged)]
pub enum MessageContent {
    /// 纯文本消息（向后兼容）
    Text(String),
    /// 多模态消息内容（文本+图片）
    Multimodal(Vec<ContentPart>),
}

/// 内容部分 - 文本或图片
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentPart {
    #[serde(rename = "type")]
    pub content_type: String, // "text" 或 "image_url"
    #[serde(flatten)]
    pub data: ContentData,
}

/// 内容数据
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ContentData {
    Text { text: String },
    ImageUrl { image_url: ImageUrl },
}

/// 图片URL（OpenAI兼容格式）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageUrl {
    pub url: String,
}

impl ChatMessage {
    /// 创建纯文本消息
    pub fn text(role: &str, content: &str) -> Self {
        Self {
            role: role.to_string(),
            content: MessageContent::Text(content.to_string()),
        }
    }

    /// 创建多模态消息（文本+图片）
    pub fn multimodal(role: &str, text: &str, image_url: &str) -> Self {
        Self {
            role: role.to_string(),
            content: MessageContent::Multimodal(vec![
                ContentPart {
                    content_type: "text".to_string(),
                    data: ContentData::Text {
                        text: text.to_string(),
                    },
                },
                ContentPart {
                    content_type: "image_url".to_string(),
                    data: ContentData::ImageUrl {
                        image_url: ImageUrl {
                            url: image_url.to_string(),
                        },
                    },
                },
            ]),
        }
    }

    /// 获取文本内容（用于显示和存储）
    pub fn get_text_content(&self) -> String {
        match &self.content {
            MessageContent::Text(text) => text.clone(),
            MessageContent::Multimodal(parts) => {
                parts
                    .iter()
                    .filter_map(|part| match &part.data {
                        ContentData::Text { text } => Some(text.clone()),
                        _ => None,
                    })
                    .collect::<Vec<_>>()
                    .join(" ")
            }
        }
    }

    /// 检查是否包含图片
    pub fn has_image(&self) -> bool {
        match &self.content {
            MessageContent::Text(_) => false,
            MessageContent::Multimodal(parts) => parts.iter().any(|part| {
                matches!(part.data, ContentData::ImageUrl { .. })
            }),
        }
    }
}

fn main() {
    println!("🧪 开始多模态聊天功能测试");

    // 测试1: 创建纯文本消息
    println!("\n📝 测试1: 纯文本消息");
    let text_msg = ChatMessage::text("user", "你好，世界！");
    println!("角色: {}", text_msg.role);
    println!("文本内容: {}", text_msg.get_text_content());
    println!("包含图片: {}", text_msg.has_image());
    
    let text_json = serde_json::to_string_pretty(&text_msg).unwrap();
    println!("JSON序列化:\n{}", text_json);

    // 测试2: 创建多模态消息
    println!("\n🖼️ 测试2: 多模态消息（文本+图片）");
    let multimodal_msg = ChatMessage::multimodal(
        "user", 
        "请分析这张图片的内容", 
        "https://example.com/sample.jpg"
    );
    println!("角色: {}", multimodal_msg.role);
    println!("文本内容: {}", multimodal_msg.get_text_content());
    println!("包含图片: {}", multimodal_msg.has_image());
    
    let multimodal_json = serde_json::to_string_pretty(&multimodal_msg).unwrap();
    println!("JSON序列化:\n{}", multimodal_json);

    // 测试3: 序列化和反序列化
    println!("\n🔄 测试3: 序列化和反序列化");
    let original = ChatMessage::multimodal(
        "user",
        "请描述这张图片",
        "https://example.com/test.png"
    );
    
    let json_str = serde_json::to_string(&original).unwrap();
    println!("序列化: {}", json_str);
    
    let deserialized: ChatMessage = serde_json::from_str(&json_str).unwrap();
    println!("反序列化成功");
    println!("角色匹配: {}", original.role == deserialized.role);
    println!("文本内容匹配: {}", original.get_text_content() == deserialized.get_text_content());
    println!("图片状态匹配: {}", original.has_image() == deserialized.has_image());

    println!("\n✅ 所有测试完成！");
}
