// 聊天处理器 - 处理聊天相关的HTTP请求
use axum::response::sse::Event;
use axum::{
    extract::{Path, Query, State},
    response::{IntoResponse, Sse},
    Json,
};
use futures::Stream;
use serde::{Deserialize, Serialize};
use std::{pin::Pin, sync::Arc};
use tokio_stream::StreamExt;
use uuid::Uuid;

use crate::{
    middleware::error::{AppError, AppResult},
    models::{
        conversation::{Conversation, ConversationResponse},
        message::{Message, MessageResponse, MessageRole, MessageType},
    },
    services::{memory::MemoryStorageService, qianwen::QianwenService},
    utils::{config::Settings, response::ApiResponse},
};

/// 发送文本消息请求
#[derive(Debug, Deserialize)]
pub struct SendTextMessageRequest {
    pub content: String,
    pub conversation_id: Option<Uuid>,
    /// 是否使用流式响应，默认为false（保持向后兼容性）
    #[serde(default)]
    pub stream: bool,
}

/// 发送图像消息请求
#[derive(Debug, Deserialize)]
pub struct SendImageMessageRequest {
    pub image_url: String,
    pub prompt: Option<String>,
    pub conversation_id: Option<Uuid>,
}

/// 多模态消息请求 - 支持文本和图片混合内容
#[derive(Debug, Deserialize)]
pub struct SendMultimodalMessageRequest {
    /// 消息内容，可以是纯文本字符串或多模态内容数组
    pub content: serde_json::Value,
    pub conversation_id: Option<Uuid>,
    /// 是否使用流式响应，默认为false
    #[serde(default)]
    pub stream: bool,
}

/// 聊天响应
#[derive(Debug, Serialize)]
pub struct ChatResponse {
    pub user_message: MessageResponse,
    pub assistant_message: MessageResponse,
    pub conversation: ConversationResponse,
}

/// 对话列表查询参数
#[derive(Debug, Deserialize)]
pub struct ConversationListQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub archived: Option<bool>,
}

/// 消息列表查询参数
#[derive(Debug, Deserialize)]
pub struct MessageListQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub message_type: Option<String>,
}

/// 统一的文本消息处理器 - 支持流式和非流式响应
pub async fn send_text_message(
    State((settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    Json(request): Json<SendTextMessageRequest>,
) -> AppResult<axum::response::Response> {
    tracing::info!("收到聊天请求，流式模式: {}", request.stream);

    // 根据请求中的stream字段决定响应类型
    if request.stream {
        // 流式响应
        let sse_response =
            send_text_message_stream_internal(State((settings, storage, qianwen)), request).await?;

        // 添加必要的HTTP头确保流式传输
        let mut response = sse_response.into_response();
        let headers = response.headers_mut();
        headers.insert("Cache-Control", "no-cache".parse().unwrap());
        headers.insert("Connection", "keep-alive".parse().unwrap());
        headers.insert("X-Accel-Buffering", "no".parse().unwrap()); // 禁用nginx缓冲

        Ok(response)
    } else {
        // 非流式响应
        let json_response =
            send_text_message_non_stream_internal(State((settings, storage, qianwen)), request)
                .await?;
        Ok(json_response.into_response())
    }
}

/// 内部非流式消息处理
async fn send_text_message_non_stream_internal(
    State((_settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    request: SendTextMessageRequest,
) -> AppResult<Json<ApiResponse<ChatResponse>>> {
    // 获取或创建默认用户（临时解决方案，实际应用中应从认证中间件获取）
    let user_id = get_or_create_default_user(&storage).await?;

    // 获取或创建对话
    let conversation = if let Some(conversation_id) = request.conversation_id {
        get_conversation_by_id(&storage, conversation_id).await?
    } else {
        create_new_conversation(&storage, user_id).await?
    };

    // 创建用户消息
    let user_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::User,
        MessageType::Text,
        request.content.clone(),
        None,
        None,
        None,
    );

    // 保存用户消息到内存存储
    let user_message = storage.save_message(user_message).await?;

    // 获取对话历史
    let conversation_history = get_conversation_history(&storage, conversation.id).await?;

    // 调用通义千问API
    let ai_response = qianwen.chat_text(conversation_history).await?;

    // 创建AI助手消息
    let assistant_message = Message::new(
        conversation.id,
        user_id, // 这里可以使用系统用户ID
        MessageRole::Assistant,
        MessageType::Text,
        ai_response,
        None,
        None,
        None,
    );

    // 保存AI消息到内存存储
    let assistant_message = storage.save_message(assistant_message).await?;

    // 更新对话统计
    update_conversation_stats(&storage, conversation.id).await?;

    // 重新获取更新后的对话信息
    let updated_conversation = get_conversation_by_id(&storage, conversation.id).await?;

    let response = ChatResponse {
        user_message: user_message.to_response(),
        assistant_message: assistant_message.to_response(),
        conversation: updated_conversation.to_response(),
    };

    Ok(Json(ApiResponse::success(response)))
}

/// 内部流式消息处理
async fn send_text_message_stream_internal(
    State((_settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    request: SendTextMessageRequest,
) -> AppResult<Sse<impl Stream<Item = Result<Event, AppError>>>> {
    // 获取或创建默认用户
    let user_id = get_or_create_default_user(&storage).await?;

    // 获取或创建对话
    let conversation = if let Some(conversation_id) = request.conversation_id {
        get_conversation_by_id(&storage, conversation_id).await?
    } else {
        create_new_conversation(&storage, user_id).await?
    };

    // 创建用户消息
    let user_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::User,
        MessageType::Text,
        request.content.clone(),
        None,
        None,
        None,
    );

    // 保存用户消息到内存存储
    let user_message = storage.save_message(user_message).await?;

    // 获取对话历史
    let conversation_history = get_conversation_history(&storage, conversation.id).await?;

    // 创建流式响应
    let stream = create_chat_stream(
        storage.clone(),
        qianwen.clone(),
        conversation_history,
        conversation.id,
        user_id,
        user_message,
    )
    .await?;

    // 配置SSE以确保实时传输，禁用keep-alive以避免干扰
    Ok(Sse::new(stream))
}

/// 创建聊天流
async fn create_chat_stream(
    storage: Arc<MemoryStorageService>,
    qianwen: Arc<QianwenService>,
    conversation_history: Vec<crate::services::qianwen::ChatMessage>,
    conversation_id: Uuid,
    _user_id: Uuid,
    user_message: Message,
) -> AppResult<Pin<Box<dyn Stream<Item = Result<Event, AppError>> + Send>>> {
    // 首先发送用户消息事件
    let user_event = Event::default()
        .event("user_message")
        .data(serde_json::to_string(&user_message.to_response()).unwrap());

    // 获取AI响应流
    let ai_stream = qianwen.chat_text_stream(conversation_history).await?;

    // 使用 map 来处理流事件，确保每个事件立即发送
    let accumulated_stream = ai_stream.map(move |event_result| {
        match event_result {
            Ok(stream_event) => {
                match &stream_event.data {
                    crate::services::qianwen::StreamEventData::ChunkWithDelta(chunk_with_delta) => {
                        // 处理包含JSON对象的数据块
                        let sse_event = Event::default()
                            .event("ai_chunk")
                            .data(serde_json::to_string(&stream_event).unwrap());

                        tracing::info!("立即发送AI数据块（JSON格式）");
                        Ok(sse_event)
                    }
                    crate::services::qianwen::StreamEventData::Chunk(chunk) => {
                        // 创建SSE事件，立即发送给客户端
                        let sse_event = Event::default()
                            .event("ai_chunk")
                            .data(serde_json::to_string(&stream_event).unwrap());

                        tracing::info!("立即发送AI数据块: {}", chunk.content);
                        Ok(sse_event)
                    }
                    crate::services::qianwen::StreamEventData::Done { .. } => {
                        // 异步保存消息并更新对话统计
                        let storage_clone = storage.clone();
                        tokio::spawn(async move {
                            // 这里应该累积完整的消息内容，但为了简化，我们暂时跳过
                            let _ =
                                update_conversation_stats(&storage_clone, conversation_id).await;
                        });

                        // 发送完成事件，标记流式响应结束
                        let sse_event = Event::default()
                            .event("ai_done")
                            .data(serde_json::to_string(&stream_event).unwrap());

                        tracing::debug!("AI响应完成");
                        Ok(sse_event)
                    }
                    crate::services::qianwen::StreamEventData::Error { message } => {
                        // 发送错误事件
                        let sse_event = Event::default().event("error").data(message.clone());

                        tracing::error!("AI响应错误: {}", message);
                        Ok(sse_event)
                    }
                }
            }
            Err(e) => {
                let error_event = Event::default()
                    .event("error")
                    .data(format!("流处理错误: {}", e));
                Ok(error_event)
            }
        }
    });

    // 创建完整的流，首先发送用户消息，然后是AI响应流
    let stream = tokio_stream::once(Ok(user_event)).chain(accumulated_stream);

    Ok(Box::pin(stream))
}

/// 调试流式聊天端点 - 用于测试和调试SSE解析
pub async fn debug_streaming_chat(
    State((_settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    Json(request): Json<SendTextMessageRequest>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    // 获取或创建默认用户
    let user_id = get_or_create_default_user(&storage).await?;

    // 获取或创建对话
    let conversation = if let Some(conversation_id) = request.conversation_id {
        get_conversation_by_id(&storage, conversation_id).await?
    } else {
        create_new_conversation(&storage, user_id).await?
    };

    // 获取对话历史
    let conversation_history = get_conversation_history(&storage, conversation.id).await?;

    // 尝试获取流式响应并收集所有事件
    let mut events = Vec::new();
    let mut stream = qianwen.chat_text_stream(conversation_history).await?;

    // 收集流中的所有事件
    while let Some(event_result) = stream.next().await {
        match event_result {
            Ok(event) => {
                tracing::info!("收到流式事件: {:?}", event);
                events.push(serde_json::json!({
                    "type": "success",
                    "event": event
                }));
            }
            Err(e) => {
                tracing::error!("流式事件错误: {}", e);
                events.push(serde_json::json!({
                    "type": "error",
                    "error": e.to_string()
                }));
            }
        }
    }

    let debug_info = serde_json::json!({
        "conversation_id": conversation.id,
        "user_id": user_id,
        "request_content": request.content,
        "events_count": events.len(),
        "events": events,
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    Ok(Json(ApiResponse::success(debug_info)))
}

/// 发送图像消息
pub async fn send_image_message(
    State((_settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    Json(request): Json<SendImageMessageRequest>,
) -> AppResult<Json<ApiResponse<ChatResponse>>> {
    // 获取或创建默认用户（临时解决方案，实际应用中应从认证中间件获取）
    let user_id = get_or_create_default_user(&storage).await?;

    // 获取或创建对话
    let conversation = if let Some(conversation_id) = request.conversation_id {
        get_conversation_by_id(&storage, conversation_id).await?
    } else {
        create_new_conversation(&storage, user_id).await?
    };

    // 创建用户图像消息
    let user_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::User,
        MessageType::Image,
        request
            .prompt
            .clone()
            .unwrap_or_else(|| "请分析这张图片".to_string()),
        Some(serde_json::json!({
            "image_url": request.image_url,
            "prompt": request.prompt
        })),
        None,
        None,
    );

    // 保存用户消息到内存存储
    let user_message = storage.save_message(user_message).await?;

    // 调用通义千问视觉API
    let ai_response = qianwen
        .analyze_image(request.image_url, request.prompt)
        .await?;

    // 创建AI助手消息
    let assistant_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::Assistant,
        MessageType::Text,
        ai_response,
        None,
        None,
        None,
    );

    // 保存AI消息到内存存储
    let assistant_message = storage.save_message(assistant_message).await?;

    // 更新对话统计
    update_conversation_stats(&storage, conversation.id).await?;

    // 重新获取更新后的对话信息
    let updated_conversation = get_conversation_by_id(&storage, conversation.id).await?;

    let response = ChatResponse {
        user_message: user_message.to_response(),
        assistant_message: assistant_message.to_response(),
        conversation: updated_conversation.to_response(),
    };

    Ok(Json(ApiResponse::success(response)))
}

/// 获取对话列表
pub async fn get_conversations(
    State((_, storage, _)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    Query(query): Query<ConversationListQuery>,
) -> AppResult<Json<ApiResponse<Vec<ConversationResponse>>>> {
    // 获取或创建默认用户（临时解决方案）
    let user_id = get_or_create_default_user(&storage).await?;

    let conversations = get_user_conversations(&storage, user_id, query).await?;
    let responses: Vec<ConversationResponse> =
        conversations.into_iter().map(|c| c.to_response()).collect();

    Ok(Json(ApiResponse::success(responses)))
}

/// 获取对话消息
pub async fn get_conversation_messages(
    State((_, storage, _)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    Path(conversation_id): Path<Uuid>,
    Query(query): Query<MessageListQuery>,
) -> AppResult<Json<ApiResponse<Vec<MessageResponse>>>> {
    let messages = get_conversation_messages_from_storage(&storage, conversation_id, query).await?;
    let responses: Vec<MessageResponse> = messages.into_iter().map(|m| m.to_response()).collect();

    Ok(Json(ApiResponse::success(responses)))
}

// 辅助函数

async fn get_conversation_by_id(
    storage: &MemoryStorageService,
    conversation_id: Uuid,
) -> AppResult<Conversation> {
    storage
        .get_conversation_by_id(conversation_id)
        .await?
        .ok_or(AppError::ConversationNotFound)
}

async fn create_new_conversation(
    storage: &MemoryStorageService,
    user_id: Uuid,
) -> AppResult<Conversation> {
    let conversation = Conversation::new(user_id, None, None);
    storage.save_conversation(conversation).await
}

async fn get_conversation_history(
    storage: &MemoryStorageService,
    conversation_id: Uuid,
) -> AppResult<Vec<crate::services::qianwen::ChatMessage>> {
    let messages = storage.get_conversation_history(conversation_id).await?;

    let chat_messages = messages
        .into_iter()
        .map(|m| {
            let role = match m.role {
                MessageRole::User => "user",
                MessageRole::Assistant => "assistant",
                MessageRole::System => "system",
            };
            // 使用新的ChatMessage::text()方法创建消息
            crate::services::qianwen::ChatMessage::text(role, &m.content)
        })
        .collect();

    Ok(chat_messages)
}

async fn update_conversation_stats(
    storage: &MemoryStorageService,
    conversation_id: Uuid,
) -> AppResult<()> {
    // 获取对话并更新消息计数
    if let Some(mut conversation) = storage.get_conversation_by_id(conversation_id).await? {
        conversation.increment_message_count();
        storage.update_conversation(conversation).await?;
    }
    Ok(())
}

/// 获取或创建默认用户（临时解决方案）
/// 在实际应用中，用户ID应该从认证中间件获取
async fn get_or_create_default_user(storage: &MemoryStorageService) -> AppResult<Uuid> {
    use crate::models::user::User;

    // 使用固定的默认用户ID，确保一致性
    let default_user_id = Uuid::parse_str("00000000-0000-0000-0000-000000000001")
        .map_err(|_| AppError::ValidationError("无效的默认用户ID".to_string()))?;

    // 检查默认用户是否存在
    let user_exists = storage.user_exists(default_user_id).await?;

    // 如果用户不存在，创建默认用户
    if !user_exists {
        let default_user = User::new(
            "默认用户".to_string(),
            "<EMAIL>".to_string(),
            None,
        );

        // 使用固定ID覆盖生成的ID
        let mut user_with_fixed_id = default_user;
        user_with_fixed_id.id = default_user_id;

        storage.save_user(user_with_fixed_id).await?;
    }

    Ok(default_user_id)
}

async fn get_user_conversations(
    storage: &MemoryStorageService,
    user_id: Uuid,
    query: ConversationListQuery,
) -> AppResult<Vec<Conversation>> {
    let page = query.page.unwrap_or(1).max(1);
    let per_page = query.per_page.unwrap_or(20).min(100).max(1);

    storage
        .get_user_conversations(user_id, page, per_page, query.archived)
        .await
}

async fn get_conversation_messages_from_storage(
    storage: &MemoryStorageService,
    conversation_id: Uuid,
    query: MessageListQuery,
) -> AppResult<Vec<Message>> {
    let page = query.page.unwrap_or(1).max(1);
    let per_page = query.per_page.unwrap_or(50).min(100).max(1);

    storage
        .get_conversation_messages(conversation_id, page, per_page, query.message_type)
        .await
}

/// 统一的多模态消息处理器 - 支持文本、图片和混合消息
pub async fn send_multimodal_message(
    State((settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    Json(request): Json<SendMultimodalMessageRequest>,
) -> AppResult<axum::response::Response> {
    tracing::info!("🔥 收到多模态聊天请求: stream={:?}", request.stream);
    tracing::info!("🔥 请求内容: {:?}", request.content);
    tracing::info!("收到多模态聊天请求，流式模式: {}", request.stream);

    // 根据请求中的stream字段决定响应类型
    if request.stream {
        // 流式响应
        let sse_response =
            send_multimodal_message_stream_internal(State((settings, storage, qianwen)), request)
                .await?;

        // 添加必要的HTTP头确保流式传输
        let mut response = sse_response.into_response();
        let headers = response.headers_mut();
        headers.insert("Cache-Control", "no-cache".parse().unwrap());
        headers.insert("Connection", "keep-alive".parse().unwrap());
        headers.insert("X-Accel-Buffering", "no".parse().unwrap()); // 禁用nginx缓冲

        Ok(response)
    } else {
        // 非流式响应
        let json_response = send_multimodal_message_non_stream_internal(
            State((settings, storage, qianwen)),
            request,
        )
        .await?;
        Ok(json_response.into_response())
    }
}

/// 内部非流式多模态消息处理
async fn send_multimodal_message_non_stream_internal(
    State((_settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    request: SendMultimodalMessageRequest,
) -> AppResult<Json<ApiResponse<ChatResponse>>> {
    tracing::info!("🔥 进入非流式多模态消息处理");
    tracing::info!("🔥 请求详情: {:?}", request);
    // 获取或创建默认用户（临时解决方案，实际应用中应从认证中间件获取）
    let user_id = get_or_create_default_user(&storage).await?;

    // 获取或创建对话
    let conversation = if let Some(conversation_id) = request.conversation_id {
        get_conversation_by_id(&storage, conversation_id).await?
    } else {
        create_new_conversation(&storage, user_id).await?
    };

    // 解析多模态内容并转换为ChatMessage
    let chat_message = parse_multimodal_content(request.content.clone())?;

    // 提取文本内容用于存储
    let text_content = chat_message.get_text_content();
    let has_image = chat_message.has_image();

    // 创建用户消息
    let user_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::User,
        if has_image {
            MessageType::Image
        } else {
            MessageType::Text
        },
        text_content,
        if has_image {
            Some(serde_json::json!({
                "multimodal": true,
                "has_image": true,
                "original_content": request.content
            }))
        } else {
            None
        },
        None,
        None,
    );

    // 保存用户消息到内存存储
    let user_message = storage.save_message(user_message).await?;

    // 获取对话历史（已包含刚保存的用户消息）
    let conversation_history = get_conversation_history(&storage, conversation.id).await?;

    // 调用通义千问多模态API
    let ai_response = qianwen.chat_multimodal(conversation_history).await?;

    // 创建AI助手消息
    let assistant_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::Assistant,
        MessageType::Text,
        ai_response,
        None,
        None,
        None,
    );

    // 保存AI助手消息到内存存储
    let assistant_message = storage.save_message(assistant_message).await?;

    // 更新对话统计
    update_conversation_stats(&storage, conversation.id).await?;

    // 构建响应
    let response = ChatResponse {
        user_message: user_message.to_response(),
        assistant_message: assistant_message.to_response(),
        conversation: ConversationResponse {
            id: conversation.id,
            user_id: conversation.user_id,
            title: conversation.title,
            description: conversation.description,
            created_at: conversation.created_at,
            updated_at: conversation.updated_at,
            is_archived: conversation.is_archived,
            message_count: conversation.message_count + 2,
            last_message_at: conversation.last_message_at,
        },
    };

    Ok(Json(ApiResponse::success(response)))
}

/// 内部流式多模态消息处理
async fn send_multimodal_message_stream_internal(
    State((_settings, storage, qianwen)): State<(
        Arc<Settings>,
        Arc<MemoryStorageService>,
        Arc<QianwenService>,
    )>,
    request: SendMultimodalMessageRequest,
) -> AppResult<Sse<impl Stream<Item = Result<Event, AppError>>>> {
    // 获取或创建默认用户
    let user_id = get_or_create_default_user(&storage).await?;

    // 获取或创建对话
    let conversation = if let Some(conversation_id) = request.conversation_id {
        get_conversation_by_id(&storage, conversation_id).await?
    } else {
        create_new_conversation(&storage, user_id).await?
    };

    // 解析多模态内容并转换为ChatMessage
    let chat_message = parse_multimodal_content(request.content.clone())?;

    // 提取文本内容用于存储
    let text_content = chat_message.get_text_content();
    let has_image = chat_message.has_image();

    // 创建用户消息
    let user_message = Message::new(
        conversation.id,
        user_id,
        MessageRole::User,
        if has_image {
            MessageType::Image
        } else {
            MessageType::Text
        },
        text_content,
        if has_image {
            Some(serde_json::json!({
                "multimodal": true,
                "has_image": true,
                "original_content": request.content
            }))
        } else {
            None
        },
        None,
        None,
    );

    // 保存用户消息到内存存储
    let user_message = storage.save_message(user_message).await?;

    // 获取对话历史（已包含刚保存的用户消息）
    let conversation_history = get_conversation_history(&storage, conversation.id).await?;

    // 创建流式响应
    let stream = create_multimodal_chat_stream(
        storage.clone(),
        qianwen.clone(),
        conversation_history,
        conversation.id,
        user_id,
        user_message,
    )
    .await?;

    // 配置SSE以确保实时传输
    Ok(Sse::new(stream))
}

/// 创建多模态聊天流
async fn create_multimodal_chat_stream(
    storage: Arc<MemoryStorageService>,
    qianwen: Arc<QianwenService>,
    conversation_history: Vec<crate::services::qianwen::ChatMessage>,
    conversation_id: Uuid,
    _user_id: Uuid,
    user_message: Message,
) -> AppResult<Pin<Box<dyn Stream<Item = Result<Event, AppError>> + Send>>> {
    // 首先发送用户消息事件
    let user_event = Event::default()
        .event("user_message")
        .data(serde_json::to_string(&user_message.to_response()).unwrap());

    // 获取AI响应流
    let ai_stream = qianwen.chat_multimodal_stream(conversation_history).await?;

    // 转换AI流事件为SSE事件（简化版本，不累积内容）
    let accumulated_stream = ai_stream.map(move |event_result| {
        match event_result {
            Ok(stream_event) => {
                match stream_event.event_type.as_str() {
                    "chunk" => {
                        // 根据StreamEventData的类型处理不同的数据格式
                        match &stream_event.data {
                            crate::services::qianwen::StreamEventData::ChunkWithDelta(
                                chunk_with_delta,
                            ) => {
                                // 返回解析后的JSON对象作为delta
                                Ok(Event::default().event("ai_chunk").data(
                                    serde_json::to_string(&serde_json::json!({
                                        "event_type": "chunk",
                                        "data": {
                                            "delta": chunk_with_delta.delta
                                        }
                                    }))
                                    .unwrap(),
                                ))
                            }
                            crate::services::qianwen::StreamEventData::Chunk(chunk) => {
                                // 兼容旧格式：返回文本内容
                                Ok(Event::default().event("ai_chunk").data(
                                    serde_json::to_string(&serde_json::json!({
                                        "event_type": "chunk",
                                        "data": {
                                            "delta": chunk.content
                                        }
                                    }))
                                    .unwrap(),
                                ))
                            }
                            _ => {
                                // 其他类型，返回空内容
                                Ok(Event::default().event("ai_chunk").data(
                                    serde_json::to_string(&serde_json::json!({
                                        "event_type": "chunk",
                                        "data": {
                                            "delta": ""
                                        }
                                    }))
                                    .unwrap(),
                                ))
                            }
                        }
                    }
                    "done" => {
                        // 提取token数量（根据StreamEventData的实际结构）
                        let total_tokens = match &stream_event.data {
                            crate::services::qianwen::StreamEventData::Done { total_tokens } => {
                                total_tokens.unwrap_or(0)
                            }
                            _ => 0,
                        };

                        // 发送完成事件（简化版本，不保存消息到存储）
                        // 注意：在实际应用中，您可能需要在客户端累积内容并发送保存请求
                        Ok(Event::default().event("ai_done").data(
                            serde_json::to_string(&serde_json::json!({
                                "event_type": "done",
                                "data": {
                                    "total_tokens": total_tokens
                                }
                            }))
                            .unwrap(),
                        ))
                    }
                    _ => {
                        // 其他事件类型
                        Ok(Event::default()
                            .event("ai_event")
                            .data(serde_json::to_string(&stream_event).unwrap()))
                    }
                }
            }
            Err(e) => {
                tracing::error!("流式事件错误: {}", e);
                Ok(Event::default().event("error").data(
                    serde_json::to_string(&serde_json::json!({
                        "error": e.to_string()
                    }))
                    .unwrap(),
                ))
            }
        }
    });

    // 创建完整的流，首先发送用户消息，然后是AI响应流
    let stream = tokio_stream::once(Ok(user_event)).chain(accumulated_stream);

    Ok(Box::pin(stream))
}

/// 解析多模态内容并转换为ChatMessage
fn parse_multimodal_content(
    content: serde_json::Value,
) -> AppResult<crate::services::qianwen::ChatMessage> {
    use crate::services::qianwen::{
        ChatMessage, ContentData, ContentPart, ImageUrl, MessageContent,
    };

    match content {
        // 纯文本消息
        serde_json::Value::String(text) => Ok(ChatMessage::text("user", &text)),
        // 多模态消息数组
        serde_json::Value::Array(parts) => {
            let mut content_parts = Vec::new();

            for part in parts {
                let part_obj = part.as_object().ok_or_else(|| {
                    AppError::ValidationError("多模态内容部分必须是对象".to_string())
                })?;

                let content_type =
                    part_obj
                        .get("type")
                        .and_then(|v| v.as_str())
                        .ok_or_else(|| {
                            AppError::ValidationError("多模态内容部分缺少type字段".to_string())
                        })?;

                match content_type {
                    "text" => {
                        let text =
                            part_obj
                                .get("text")
                                .and_then(|v| v.as_str())
                                .ok_or_else(|| {
                                    AppError::ValidationError(
                                        "文本内容部分缺少text字段".to_string(),
                                    )
                                })?;

                        content_parts.push(ContentPart {
                            content_type: "text".to_string(),
                            data: ContentData::Text {
                                text: text.to_string(),
                            },
                        });
                    }
                    "image_url" => {
                        let image_url_obj = part_obj
                            .get("image_url")
                            .and_then(|v| v.as_object())
                            .ok_or_else(|| {
                            AppError::ValidationError("图片内容部分缺少image_url字段".to_string())
                        })?;

                        let url = image_url_obj
                            .get("url")
                            .and_then(|v| v.as_str())
                            .ok_or_else(|| {
                                AppError::ValidationError("图片URL缺少url字段".to_string())
                            })?;

                        content_parts.push(ContentPart {
                            content_type: "image_url".to_string(),
                            data: ContentData::ImageUrl {
                                image_url: ImageUrl {
                                    url: url.to_string(),
                                },
                            },
                        });
                    }
                    _ => {
                        return Err(AppError::ValidationError(format!(
                            "不支持的内容类型: {}",
                            content_type
                        )));
                    }
                }
            }

            if content_parts.is_empty() {
                return Err(AppError::ValidationError("多模态内容不能为空".to_string()));
            }

            Ok(ChatMessage {
                role: "user".to_string(),
                content: MessageContent::Multimodal(content_parts),
            })
        }
        _ => Err(AppError::ValidationError(
            "内容必须是字符串或对象数组".to_string(),
        )),
    }
}
