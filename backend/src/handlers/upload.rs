// 文件上传处理器 - 处理文件上传相关的HTTP请求
use axum::{
    Json,
    extract::{Multipart, Path, Query, State},
    http::{StatusCode, header},
    response::Response,
};
use serde::{Deserialize, Serialize};
use sha2::Digest;
use std::sync::Arc;
use uuid::Uuid;

use crate::{
    middleware::error::{AppError, AppResult},
    models::file::{File, FileResponse, FileType},
    services::{database::DatabaseService, file::FileService},
    utils::{config::Settings, response::ApiResponse},
};

/// 文件上传响应
#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub file: FileResponse,
    pub message: String,
}

/// 文件列表查询参数
#[derive(Debug, Deserialize)]
pub struct FileListQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub file_type: Option<String>,
}

/// 上传单个文件
pub async fn upload_file(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    mut multipart: Multipart,
) -> AppResult<Json<ApiResponse<UploadResponse>>> {
    // 简化版本：暂时使用固定用户ID
    let user_id = Uuid::new_v4(); // 在实际应用中，这应该从认证中间件获取

    let mut uploaded_file: Option<File> = None;

    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::BadRequest(format!("多部分数据解析失败: {}", e)))?
    {
        let name = field.name().unwrap_or("").to_string();

        if name == "file" {
            let filename = field
                .file_name()
                .ok_or_else(|| AppError::BadRequest("缺少文件名".to_string()))?
                .to_string();

            let content_type = field
                .content_type()
                .ok_or_else(|| AppError::BadRequest("缺少文件类型".to_string()))?
                .to_string();

            let data = field
                .bytes()
                .await
                .map_err(|e| AppError::BadRequest(format!("文件数据读取失败: {}", e)))?;

            // 上传文件
            let upload_result = file_service
                .upload_file(user_id, &filename, &data, &content_type)
                .await?;

            // 计算校验和
            let checksum = sha2::Sha256::digest(&data);
            let checksum_str = format!("{:x}", checksum);

            // 保存到数据库
            let file = file_service
                .save_file_to_db(&database, upload_result.file, checksum_str)
                .await?;

            uploaded_file = Some(file);
            break;
        }
    }

    let file = uploaded_file.ok_or_else(|| AppError::BadRequest("未找到文件字段".to_string()))?;

    let response = UploadResponse {
        file: file.to_response(),
        message: "文件上传成功".to_string(),
    };

    Ok(Json(ApiResponse::success(response)))
}

/// 批量上传文件
pub async fn upload_multiple_files(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    mut multipart: Multipart,
) -> AppResult<Json<ApiResponse<Vec<UploadResponse>>>> {
    // 简化版本：暂时使用固定用户ID
    let user_id = Uuid::new_v4();

    let mut uploaded_files = Vec::new();

    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::BadRequest(format!("多部分数据解析失败: {}", e)))?
    {
        let name = field.name().unwrap_or("").to_string();

        if name == "files" {
            let filename = field
                .file_name()
                .ok_or_else(|| AppError::BadRequest("缺少文件名".to_string()))?
                .to_string();

            let content_type = field
                .content_type()
                .ok_or_else(|| AppError::BadRequest("缺少文件类型".to_string()))?
                .to_string();

            let data = field
                .bytes()
                .await
                .map_err(|e| AppError::BadRequest(format!("文件数据读取失败: {}", e)))?;

            // 上传文件
            match file_service
                .upload_file(user_id, &filename, &data, &content_type)
                .await
            {
                Ok(upload_result) => {
                    // 计算校验和
                    let checksum = sha2::Sha256::digest(&data);
                    let checksum_str = format!("{:x}", checksum);

                    // 保存到数据库
                    match file_service
                        .save_file_to_db(&database, upload_result.file, checksum_str)
                        .await
                    {
                        Ok(file) => {
                            uploaded_files.push(UploadResponse {
                                file: file.to_response(),
                                message: format!("文件 {} 上传成功", filename),
                            });
                        }
                        Err(e) => {
                            eprintln!("文件 {} 保存到数据库失败: {:?}", filename, e);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("文件 {} 上传失败: {:?}", filename, e);
                }
            }
        }
    }

    if uploaded_files.is_empty() {
        return Err(AppError::BadRequest("没有成功上传任何文件".to_string()));
    }

    Ok(Json(ApiResponse::success(uploaded_files)))
}

/// 下载文件
pub async fn download_file(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Response> {
    // 获取文件信息
    let file = file_service.get_file(&database, file_id).await?;

    // 检查文件状态
    if !file.is_ready() {
        return Err(AppError::BadRequest("文件尚未就绪".to_string()));
    }

    // 读取文件内容
    let content = file_service.read_file_content(&file).await?;

    // 构建响应
    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, &file.mime_type)
        .header(
            header::CONTENT_DISPOSITION,
            format!("attachment; filename=\"{}\"", file.original_filename),
        )
        .header(header::CONTENT_LENGTH, content.len())
        .body(content.into())
        .map_err(|e| AppError::InternalServerError(format!("响应构建失败: {}", e)))?;

    Ok(response)
}

/// 获取文件信息
pub async fn get_file_info(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Json<ApiResponse<FileResponse>>> {
    let file = file_service.get_file(&database, file_id).await?;
    Ok(Json(ApiResponse::success(file.to_response())))
}

/// 删除文件
pub async fn delete_file(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Json<ApiResponse<()>>> {
    file_service.delete_file(&database, file_id).await?;
    Ok(Json(ApiResponse::ok_with_message(
        "文件删除成功".to_string(),
    )))
}

/// 获取用户文件列表
pub async fn get_user_files(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    Query(query): Query<FileListQuery>,
) -> AppResult<Json<ApiResponse<Vec<FileResponse>>>> {
    // 简化版本：暂时使用固定用户ID
    let user_id = Uuid::new_v4();

    let page = query.page.unwrap_or(1).max(1);
    let per_page = query.per_page.unwrap_or(20).min(100).max(1);

    // 解析文件类型
    let file_type = if let Some(type_str) = query.file_type {
        match type_str.to_lowercase().as_str() {
            "image" => Some(FileType::Image),
            "audio" => Some(FileType::Audio),
            "video" => Some(FileType::Video),
            "document" => Some(FileType::Document),
            "other" => Some(FileType::Other),
            _ => None,
        }
    } else {
        None
    };

    let files = file_service
        .get_user_files(&database, user_id, page, per_page, file_type)
        .await?;

    let responses: Vec<FileResponse> = files.into_iter().map(|f| f.to_response()).collect();

    Ok(Json(ApiResponse::success(responses)))
}

/// 预览图片文件
pub async fn preview_image(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
    Path(file_id): Path<Uuid>,
) -> AppResult<Response> {
    // 获取文件信息
    let file = file_service.get_file(&database, file_id).await?;

    // 检查是否为图片文件
    if !file.is_image() {
        return Err(AppError::BadRequest("文件不是图片类型".to_string()));
    }

    // 检查文件状态
    if !file.is_ready() {
        return Err(AppError::BadRequest("文件尚未就绪".to_string()));
    }

    // 读取文件内容
    let content = file_service.read_file_content(&file).await?;

    // 构建响应
    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, &file.mime_type)
        .header(header::CACHE_CONTROL, "public, max-age=3600")
        .header(header::CONTENT_LENGTH, content.len())
        .body(content.into())
        .map_err(|e| AppError::InternalServerError(format!("响应构建失败: {}", e)))?;

    Ok(response)
}

/// 清理过期文件
pub async fn cleanup_expired_files(
    State((_, database, _, file_service)): State<(
        Arc<Settings>,
        Arc<DatabaseService>,
        Arc<crate::services::qianwen::QianwenService>,
        Arc<FileService>,
    )>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    let deleted_count = file_service.cleanup_expired_files(&database).await?;

    Ok(Json(ApiResponse::success(serde_json::json!({
        "deleted_count": deleted_count,
        "message": format!("成功清理 {} 个过期文件", deleted_count)
    }))))
}
