// 行程数据模型 - 定义旅游行程相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::poi::Poi;

/// 行程项目类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ItineraryItemType {
    /// 景点游览
    Sightseeing,
    /// 用餐
    Dining,
    /// 住宿
    Accommodation,
    /// 交通
    Transportation,
    /// 购物
    Shopping,
    /// 休息
    Rest,
    /// 自由活动
    FreeTime,
}

/// 交通方式枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TransportMode {
    /// 步行
    Walking,
    /// 公交
    Bus,
    /// 地铁
    Subway,
    /// 出租车
    Taxi,
    /// 网约车
    RideHailing,
    /// 自驾
    Driving,
    /// 飞机
    Flight,
    /// 火车
    Train,
    /// 轮船
    Ship,
}

/// 行程项目数据模型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ItineraryItem {
    pub id: Uuid,
    pub itinerary_id: Uuid,
    pub poi_id: Option<Uuid>,
    pub item_type: ItineraryItemType,
    pub title: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_minutes: i32,
    pub cost: Option<f64>,
    pub notes: Option<String>,
    pub transport_mode: Option<TransportMode>,
    pub transport_duration: Option<i32>,  // 交通时间（分钟）
    pub transport_cost: Option<f64>,
    pub order_index: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 行程数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Itinerary {
    pub id: Uuid,
    pub travel_guide_id: Uuid,
    pub day_number: i32,
    pub date: DateTime<Utc>,
    pub title: String,
    pub description: Option<String>,
    pub total_cost: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 行程创建请求
#[derive(Debug, Deserialize)]
pub struct CreateItineraryRequest {
    pub travel_guide_id: Uuid,
    pub day_number: i32,
    pub date: DateTime<Utc>,
    pub title: String,
    pub description: Option<String>,
}

/// 行程项目创建请求
#[derive(Debug, Deserialize)]
pub struct CreateItineraryItemRequest {
    pub itinerary_id: Uuid,
    pub poi_id: Option<Uuid>,
    pub item_type: ItineraryItemType,
    pub title: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_minutes: i32,
    pub cost: Option<f64>,
    pub notes: Option<String>,
    pub transport_mode: Option<TransportMode>,
    pub transport_duration: Option<i32>,
    pub transport_cost: Option<f64>,
    pub order_index: i32,
}

/// 行程更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateItineraryRequest {
    pub day_number: Option<i32>,
    pub date: Option<DateTime<Utc>>,
    pub title: Option<String>,
    pub description: Option<String>,
    pub total_cost: Option<f64>,
}

/// 行程项目更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateItineraryItemRequest {
    pub poi_id: Option<Uuid>,
    pub item_type: Option<ItineraryItemType>,
    pub title: Option<String>,
    pub description: Option<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub duration_minutes: Option<i32>,
    pub cost: Option<f64>,
    pub notes: Option<String>,
    pub transport_mode: Option<TransportMode>,
    pub transport_duration: Option<i32>,
    pub transport_cost: Option<f64>,
    pub order_index: Option<i32>,
}

/// 行程响应
#[derive(Debug, Serialize)]
pub struct ItineraryResponse {
    pub id: Uuid,
    pub travel_guide_id: Uuid,
    pub day_number: i32,
    pub date: DateTime<Utc>,
    pub title: String,
    pub description: Option<String>,
    pub total_cost: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub items: Vec<ItineraryItemResponse>,
}

/// 行程项目响应
#[derive(Debug, Serialize)]
pub struct ItineraryItemResponse {
    pub id: Uuid,
    pub itinerary_id: Uuid,
    pub poi_id: Option<Uuid>,
    pub poi: Option<Poi>,
    pub item_type: ItineraryItemType,
    pub title: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_minutes: i32,
    pub cost: Option<f64>,
    pub notes: Option<String>,
    pub transport_mode: Option<TransportMode>,
    pub transport_duration: Option<i32>,
    pub transport_cost: Option<f64>,
    pub order_index: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Itinerary {
    /// 创建新行程
    pub fn new(request: CreateItineraryRequest) -> Self {
        let now = Utc::now();
        
        Self {
            id: Uuid::new_v4(),
            travel_guide_id: request.travel_guide_id,
            day_number: request.day_number,
            date: request.date,
            title: request.title,
            description: request.description,
            total_cost: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 更新行程信息
    pub fn update(&mut self, request: UpdateItineraryRequest) {
        if let Some(day_number) = request.day_number {
            self.day_number = day_number;
        }
        if let Some(date) = request.date {
            self.date = date;
        }
        if let Some(title) = request.title {
            self.title = title;
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(total_cost) = request.total_cost {
            self.total_cost = Some(total_cost);
        }
        self.updated_at = Utc::now();
    }
}

impl ItineraryItem {
    /// 创建新行程项目
    pub fn new(request: CreateItineraryItemRequest) -> Self {
        let now = Utc::now();
        
        Self {
            id: Uuid::new_v4(),
            itinerary_id: request.itinerary_id,
            poi_id: request.poi_id,
            item_type: request.item_type,
            title: request.title,
            description: request.description,
            start_time: request.start_time,
            end_time: request.end_time,
            duration_minutes: request.duration_minutes,
            cost: request.cost,
            notes: request.notes,
            transport_mode: request.transport_mode,
            transport_duration: request.transport_duration,
            transport_cost: request.transport_cost,
            order_index: request.order_index,
            created_at: now,
            updated_at: now,
        }
    }

    /// 更新行程项目信息
    pub fn update(&mut self, request: UpdateItineraryItemRequest) {
        if let Some(poi_id) = request.poi_id {
            self.poi_id = Some(poi_id);
        }
        if let Some(item_type) = request.item_type {
            self.item_type = item_type;
        }
        if let Some(title) = request.title {
            self.title = title;
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(start_time) = request.start_time {
            self.start_time = start_time;
        }
        if let Some(end_time) = request.end_time {
            self.end_time = end_time;
        }
        if let Some(duration_minutes) = request.duration_minutes {
            self.duration_minutes = duration_minutes;
        }
        if let Some(cost) = request.cost {
            self.cost = Some(cost);
        }
        if let Some(notes) = request.notes {
            self.notes = Some(notes);
        }
        if let Some(transport_mode) = request.transport_mode {
            self.transport_mode = Some(transport_mode);
        }
        if let Some(transport_duration) = request.transport_duration {
            self.transport_duration = Some(transport_duration);
        }
        if let Some(transport_cost) = request.transport_cost {
            self.transport_cost = Some(transport_cost);
        }
        if let Some(order_index) = request.order_index {
            self.order_index = order_index;
        }
        self.updated_at = Utc::now();
    }
}
