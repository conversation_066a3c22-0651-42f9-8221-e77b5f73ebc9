// 消息数据模型 - 定义消息相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 消息类型枚举
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum MessageType {
    Text,  // 文本消息
    Image, // 图片消息
    Audio, // 音频消息
    File,  // 文件消息
}

/// 消息角色枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    User,      // 用户消息
    Assistant, // AI助手消息
    System,    // 系统消息
}

/// 消息数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub conversation_id: Uuid,
    pub user_id: Uuid,
    pub role: MessageRole,
    pub message_type: MessageType,
    pub content: String,
    pub metadata: Option<serde_json::Value>, // 存储额外的元数据，如文件信息、图片尺寸等
    pub file_id: Option<Uuid>,               // 关联的文件ID
    pub parent_message_id: Option<Uuid>,     // 父消息ID（用于回复功能）
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_deleted: bool,
}

/// 消息创建请求
#[derive(Debug, Deserialize)]
pub struct CreateMessageRequest {
    pub conversation_id: Uuid,
    pub role: MessageRole,
    pub message_type: MessageType,
    pub content: String,
    pub metadata: Option<serde_json::Value>,
    pub file_id: Option<Uuid>,
    pub parent_message_id: Option<Uuid>,
}

/// 消息更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateMessageRequest {
    pub content: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

/// 消息响应
#[derive(Debug, Serialize)]
pub struct MessageResponse {
    pub id: Uuid,
    pub conversation_id: Uuid,
    pub user_id: Uuid,
    pub role: MessageRole,
    pub message_type: MessageType,
    pub content: String,
    pub metadata: Option<serde_json::Value>,
    pub file_id: Option<Uuid>,
    pub parent_message_id: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub file_info: Option<FileInfo>, // 关联的文件信息
}

/// 文件信息（用于消息响应）
#[derive(Debug, Serialize)]
pub struct FileInfo {
    pub id: Uuid,
    pub filename: String,
    pub file_type: String,
    pub file_size: i64,
    pub url: String,
}

/// 消息统计信息
#[derive(Debug, Serialize)]
pub struct MessageStats {
    pub total_text_messages: i64,
    pub total_image_messages: i64,
    pub total_audio_messages: i64,
    pub total_file_messages: i64,
    pub average_message_length: f64,
    pub most_active_hour: Option<i32>,
}

impl From<Message> for MessageResponse {
    fn from(message: Message) -> Self {
        Self {
            id: message.id,
            conversation_id: message.conversation_id,
            user_id: message.user_id,
            role: message.role,
            message_type: message.message_type,
            content: message.content,
            metadata: message.metadata,
            file_id: message.file_id,
            parent_message_id: message.parent_message_id,
            created_at: message.created_at,
            updated_at: message.updated_at,
            file_info: None, // 需要单独查询填充
        }
    }
}

impl Message {
    /// 创建新消息
    pub fn new(
        conversation_id: Uuid,
        user_id: Uuid,
        role: MessageRole,
        message_type: MessageType,
        content: String,
        metadata: Option<serde_json::Value>,
        file_id: Option<Uuid>,
        parent_message_id: Option<Uuid>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            conversation_id,
            user_id,
            role,
            message_type,
            content,
            metadata,
            file_id,
            parent_message_id,
            created_at: now,
            updated_at: now,
            is_deleted: false,
        }
    }

    /// 从创建请求创建消息
    pub fn from_request(request: CreateMessageRequest, user_id: Uuid) -> Self {
        Self::new(
            request.conversation_id,
            user_id,
            request.role,
            request.message_type,
            request.content,
            request.metadata,
            request.file_id,
            request.parent_message_id,
        )
    }

    /// 更新消息内容
    pub fn update(&mut self, request: UpdateMessageRequest) {
        if let Some(content) = request.content {
            self.content = content;
        }
        if let Some(metadata) = request.metadata {
            self.metadata = Some(metadata);
        }
        self.updated_at = Utc::now();
    }

    /// 软删除消息
    pub fn soft_delete(&mut self) {
        self.is_deleted = true;
        self.updated_at = Utc::now();
    }

    /// 恢复已删除的消息
    pub fn restore(&mut self) {
        self.is_deleted = false;
        self.updated_at = Utc::now();
    }

    /// 检查是否为用户消息
    pub fn is_user_message(&self) -> bool {
        matches!(self.role, MessageRole::User)
    }

    /// 检查是否为AI助手消息
    pub fn is_assistant_message(&self) -> bool {
        matches!(self.role, MessageRole::Assistant)
    }

    /// 检查是否包含文件
    pub fn has_file(&self) -> bool {
        self.file_id.is_some()
    }

    /// 转换为响应格式
    pub fn to_response(self) -> MessageResponse {
        MessageResponse::from(self)
    }

    /// 转换为响应格式（带文件信息）
    pub fn to_response_with_file(self, file_info: Option<FileInfo>) -> MessageResponse {
        let mut response = MessageResponse::from(self);
        response.file_info = file_info;
        response
    }
}
