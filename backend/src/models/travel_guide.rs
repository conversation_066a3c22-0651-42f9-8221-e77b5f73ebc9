// 旅游攻略数据模型 - 定义旅游攻略相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::destination::Destination;
use super::itinerary::Itinerary;

/// 旅游攻略类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TravelGuideType {
    /// 自由行攻略
    SelfGuided,
    /// 跟团游攻略
    GroupTour,
    /// 定制行程
    Custom,
}

/// 旅游偏好类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TravelPreferenceType {
    /// 美食
    Food,
    /// 历史文化
    History,
    /// 自然风光
    Nature,
    /// 购物
    Shopping,
    /// 艺术
    Art,
    /// 冒险
    Adventure,
    /// 休闲
    Relaxation,
}

/// 旅游预算级别枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BudgetLevel {
    /// 经济型
    Budget,
    /// 中等
    Moderate,
    /// 高端
    Luxury,
}

/// 旅游攻略数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TravelGuide {
    pub id: Uuid,
    pub user_id: Uuid,
    pub conversation_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub duration_days: i32,
    pub guide_type: TravelGuideType,
    pub budget_level: BudgetLevel,
    pub budget_amount: Option<f64>,
    pub preferences: Vec<TravelPreferenceType>,
    pub traveler_count: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_archived: bool,
}

/// 旅游攻略创建请求
#[derive(Debug, Deserialize)]
pub struct CreateTravelGuideRequest {
    pub conversation_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub duration_days: i32,
    pub guide_type: TravelGuideType,
    pub budget_level: BudgetLevel,
    pub budget_amount: Option<f64>,
    pub preferences: Vec<TravelPreferenceType>,
    pub traveler_count: i32,
    pub destination_ids: Vec<Uuid>,
}

/// 旅游攻略更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateTravelGuideRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub duration_days: Option<i32>,
    pub guide_type: Option<TravelGuideType>,
    pub budget_level: Option<BudgetLevel>,
    pub budget_amount: Option<f64>,
    pub preferences: Option<Vec<TravelPreferenceType>>,
    pub traveler_count: Option<i32>,
    pub is_archived: Option<bool>,
}

/// 旅游攻略响应
#[derive(Debug, Serialize)]
pub struct TravelGuideResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub conversation_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub duration_days: i32,
    pub guide_type: TravelGuideType,
    pub budget_level: BudgetLevel,
    pub budget_amount: Option<f64>,
    pub preferences: Vec<TravelPreferenceType>,
    pub traveler_count: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_archived: bool,
    pub destinations: Vec<Destination>,
    pub itineraries: Vec<Itinerary>,
}

/// 旅游攻略列表项（简化版本）
#[derive(Debug, Serialize)]
pub struct TravelGuideListItem {
    pub id: Uuid,
    pub title: String,
    pub duration_days: i32,
    pub guide_type: TravelGuideType,
    pub budget_level: BudgetLevel,
    pub created_at: DateTime<Utc>,
    pub is_archived: bool,
}

impl TravelGuide {
    /// 创建新旅游攻略
    pub fn new(user_id: Uuid, request: CreateTravelGuideRequest) -> Self {
        let now = Utc::now();

        Self {
            id: Uuid::new_v4(),
            user_id,
            conversation_id: request.conversation_id,
            title: request.title,
            description: request.description,
            start_date: request.start_date,
            end_date: request.end_date,
            duration_days: request.duration_days,
            guide_type: request.guide_type,
            budget_level: request.budget_level,
            budget_amount: request.budget_amount,
            preferences: request.preferences,
            traveler_count: request.traveler_count,
            created_at: now,
            updated_at: now,
            is_archived: false,
        }
    }

    /// 更新旅游攻略信息
    pub fn update(&mut self, request: UpdateTravelGuideRequest) {
        if let Some(title) = request.title {
            self.title = title;
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(start_date) = request.start_date {
            self.start_date = Some(start_date);
        }
        if let Some(end_date) = request.end_date {
            self.end_date = Some(end_date);
        }
        if let Some(duration_days) = request.duration_days {
            self.duration_days = duration_days;
        }
        if let Some(guide_type) = request.guide_type {
            self.guide_type = guide_type;
        }
        if let Some(budget_level) = request.budget_level {
            self.budget_level = budget_level;
        }
        if let Some(budget_amount) = request.budget_amount {
            self.budget_amount = Some(budget_amount);
        }
        if let Some(preferences) = request.preferences {
            self.preferences = preferences;
        }
        if let Some(traveler_count) = request.traveler_count {
            self.traveler_count = traveler_count;
        }
        if let Some(is_archived) = request.is_archived {
            self.is_archived = is_archived;
        }
        self.updated_at = Utc::now();
    }

    /// 归档旅游攻略
    pub fn archive(&mut self) {
        self.is_archived = true;
        self.updated_at = Utc::now();
    }

    /// 取消归档
    pub fn unarchive(&mut self) {
        self.is_archived = false;
        self.updated_at = Utc::now();
    }
}

impl From<TravelGuide> for TravelGuideListItem {
    fn from(guide: TravelGuide) -> Self {
        Self {
            id: guide.id,
            title: guide.title,
            duration_days: guide.duration_days,
            guide_type: guide.guide_type,
            budget_level: guide.budget_level,
            created_at: guide.created_at,
            is_archived: guide.is_archived,
        }
    }
}
