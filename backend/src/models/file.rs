// 文件数据模型 - 定义文件相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 文件类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileType {
    Image,    // 图片文件
    Audio,    // 音频文件
    Document, // 文档文件
    Video,    // 视频文件
    Other,    // 其他类型
}

/// 文件状态枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileStatus {
    Uploading,  // 上传中
    Processing, // 处理中
    Ready,      // 就绪
    Failed,     // 失败
    Deleted,    // 已删除
}

/// 文件数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct File {
    pub id: Uuid,
    pub user_id: Uuid,
    pub filename: String,
    pub original_filename: String,
    pub file_type: FileType,
    pub mime_type: String,
    pub file_size: i64,
    pub file_path: String,
    pub url: String,
    pub status: FileStatus,
    pub metadata: Option<serde_json::Value>, // 存储文件的额外信息，如图片尺寸、音频时长等
    pub checksum: Option<String>,            // 文件校验和
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>, // 文件过期时间（可选）
}

/// 文件上传请求
#[derive(Debug, Deserialize)]
pub struct UploadFileRequest {
    pub filename: String,
    pub file_type: FileType,
    pub mime_type: String,
    pub file_size: i64,
    pub metadata: Option<serde_json::Value>,
}

/// 文件更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateFileRequest {
    pub filename: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub status: Option<FileStatus>,
}

/// 文件响应
#[derive(Debug, Serialize)]
pub struct FileResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub filename: String,
    pub original_filename: String,
    pub file_type: FileType,
    pub mime_type: String,
    pub file_size: i64,
    pub url: String,
    pub status: FileStatus,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
}

/// 文件列表项（简化版本）
#[derive(Debug, Serialize)]
pub struct FileListItem {
    pub id: Uuid,
    pub filename: String,
    pub file_type: FileType,
    pub file_size: i64,
    pub status: FileStatus,
    pub created_at: DateTime<Utc>,
    pub url: String,
}

/// 文件统计信息
#[derive(Debug, Serialize)]
pub struct FileStats {
    pub total_files: i64,
    pub total_size: i64,
    pub files_by_type: std::collections::HashMap<FileType, i64>,
    pub average_file_size: f64,
    pub largest_file_size: i64,
}

impl From<File> for FileResponse {
    fn from(file: File) -> Self {
        Self {
            id: file.id,
            user_id: file.user_id,
            filename: file.filename,
            original_filename: file.original_filename,
            file_type: file.file_type,
            mime_type: file.mime_type,
            file_size: file.file_size,
            url: file.url,
            status: file.status,
            metadata: file.metadata,
            created_at: file.created_at,
            updated_at: file.updated_at,
            expires_at: file.expires_at,
        }
    }
}

impl From<File> for FileListItem {
    fn from(file: File) -> Self {
        Self {
            id: file.id,
            filename: file.filename,
            file_type: file.file_type,
            file_size: file.file_size,
            status: file.status,
            created_at: file.created_at,
            url: file.url,
        }
    }
}

impl File {
    /// 创建新文件记录
    pub fn new(
        user_id: Uuid,
        filename: String,
        original_filename: String,
        file_type: FileType,
        mime_type: String,
        file_size: i64,
        file_path: String,
        url: String,
        metadata: Option<serde_json::Value>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            user_id,
            filename,
            original_filename,
            file_type,
            mime_type,
            file_size,
            file_path,
            url,
            status: FileStatus::Uploading,
            metadata,
            checksum: None,
            created_at: now,
            updated_at: now,
            expires_at: None,
        }
    }

    /// 从上传请求创建文件记录
    pub fn from_upload_request(
        request: UploadFileRequest,
        user_id: Uuid,
        file_path: String,
        url: String,
    ) -> Self {
        let filename = format!("{}_{}", Uuid::new_v4(), request.filename);
        Self::new(
            user_id,
            filename,
            request.filename,
            request.file_type,
            request.mime_type,
            request.file_size,
            file_path,
            url,
            request.metadata,
        )
    }

    /// 更新文件信息
    pub fn update(&mut self, request: UpdateFileRequest) {
        if let Some(filename) = request.filename {
            self.filename = filename;
        }
        if let Some(metadata) = request.metadata {
            self.metadata = Some(metadata);
        }
        if let Some(status) = request.status {
            self.status = status;
        }
        self.updated_at = Utc::now();
    }

    /// 标记文件上传完成
    pub fn mark_ready(&mut self, checksum: Option<String>) {
        self.status = FileStatus::Ready;
        self.checksum = checksum;
        self.updated_at = Utc::now();
    }

    /// 标记文件处理中
    pub fn mark_processing(&mut self) {
        self.status = FileStatus::Processing;
        self.updated_at = Utc::now();
    }

    /// 标记文件处理失败
    pub fn mark_failed(&mut self) {
        self.status = FileStatus::Failed;
        self.updated_at = Utc::now();
    }

    /// 标记文件已删除
    pub fn mark_deleted(&mut self) {
        self.status = FileStatus::Deleted;
        self.updated_at = Utc::now();
    }

    /// 设置过期时间
    pub fn set_expiry(&mut self, expires_at: DateTime<Utc>) {
        self.expires_at = Some(expires_at);
        self.updated_at = Utc::now();
    }

    /// 检查文件是否已过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// 检查文件是否就绪
    pub fn is_ready(&self) -> bool {
        matches!(self.status, FileStatus::Ready)
    }

    /// 检查是否为图片文件
    pub fn is_image(&self) -> bool {
        matches!(self.file_type, FileType::Image)
    }

    /// 检查是否为音频文件
    pub fn is_audio(&self) -> bool {
        matches!(self.file_type, FileType::Audio)
    }

    /// 转换为响应格式
    pub fn to_response(self) -> FileResponse {
        FileResponse::from(self)
    }

    /// 转换为列表项格式
    pub fn to_list_item(self) -> FileListItem {
        FileListItem::from(self)
    }
}
