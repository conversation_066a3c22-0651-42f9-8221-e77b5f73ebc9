// 目的地数据模型 - 定义旅游目的地相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 目的地类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DestinationType {
    /// 城市
    City,
    /// 景区
    ScenicArea,
    /// 国家
    Country,
    /// 省份/州
    Province,
}

/// 气候类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ClimateType {
    /// 热带
    Tropical,
    /// 温带
    Temperate,
    /// 寒带
    Arctic,
    /// 沙漠
    Desert,
    /// 高山
    Alpine,
}

/// 目的地数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Destination {
    pub id: Uuid,
    pub name: String,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub destination_type: DestinationType,
    pub climate: Option<ClimateType>,
    pub country: String,
    pub province: Option<String>,
    pub city: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub timezone: Option<String>,
    pub popular_season: Option<String>,
    pub tags: Vec<String>,
    pub image_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub amap_id: Option<String>,  // 高德地图POI ID
}

/// 目的地创建请求
#[derive(Debug, Deserialize)]
pub struct CreateDestinationRequest {
    pub name: String,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub destination_type: DestinationType,
    pub climate: Option<ClimateType>,
    pub country: String,
    pub province: Option<String>,
    pub city: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub timezone: Option<String>,
    pub popular_season: Option<String>,
    pub tags: Vec<String>,
    pub image_url: Option<String>,
    pub amap_id: Option<String>,
}

/// 目的地更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateDestinationRequest {
    pub name: Option<String>,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub destination_type: Option<DestinationType>,
    pub climate: Option<ClimateType>,
    pub country: Option<String>,
    pub province: Option<String>,
    pub city: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub timezone: Option<String>,
    pub popular_season: Option<String>,
    pub tags: Option<Vec<String>>,
    pub image_url: Option<String>,
    pub amap_id: Option<String>,
}

/// 目的地响应
#[derive(Debug, Serialize)]
pub struct DestinationResponse {
    pub id: Uuid,
    pub name: String,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub destination_type: DestinationType,
    pub climate: Option<ClimateType>,
    pub country: String,
    pub province: Option<String>,
    pub city: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub timezone: Option<String>,
    pub popular_season: Option<String>,
    pub tags: Vec<String>,
    pub image_url: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub amap_id: Option<String>,
}

/// 目的地列表项（简化版本）
#[derive(Debug, Serialize)]
pub struct DestinationListItem {
    pub id: Uuid,
    pub name: String,
    pub destination_type: DestinationType,
    pub country: String,
    pub city: Option<String>,
    pub image_url: Option<String>,
}

/// 目的地搜索请求
#[derive(Debug, Deserialize)]
pub struct DestinationSearchRequest {
    pub query: String,
    pub destination_type: Option<DestinationType>,
    pub country: Option<String>,
    pub province: Option<String>,
    pub city: Option<String>,
    pub tags: Option<Vec<String>>,
    pub limit: Option<i32>,
    pub offset: Option<i32>,
}

impl Destination {
    /// 创建新目的地
    pub fn new(request: CreateDestinationRequest) -> Self {
        let now = Utc::now();
        
        Self {
            id: Uuid::new_v4(),
            name: request.name,
            name_en: request.name_en,
            description: request.description,
            destination_type: request.destination_type,
            climate: request.climate,
            country: request.country,
            province: request.province,
            city: request.city,
            latitude: request.latitude,
            longitude: request.longitude,
            timezone: request.timezone,
            popular_season: request.popular_season,
            tags: request.tags,
            image_url: request.image_url,
            created_at: now,
            updated_at: now,
            amap_id: request.amap_id,
        }
    }

    /// 更新目的地信息
    pub fn update(&mut self, request: UpdateDestinationRequest) {
        if let Some(name) = request.name {
            self.name = name;
        }
        if let Some(name_en) = request.name_en {
            self.name_en = Some(name_en);
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(destination_type) = request.destination_type {
            self.destination_type = destination_type;
        }
        if let Some(climate) = request.climate {
            self.climate = Some(climate);
        }
        if let Some(country) = request.country {
            self.country = country;
        }
        if let Some(province) = request.province {
            self.province = Some(province);
        }
        if let Some(city) = request.city {
            self.city = Some(city);
        }
        if let Some(latitude) = request.latitude {
            self.latitude = Some(latitude);
        }
        if let Some(longitude) = request.longitude {
            self.longitude = Some(longitude);
        }
        if let Some(timezone) = request.timezone {
            self.timezone = Some(timezone);
        }
        if let Some(popular_season) = request.popular_season {
            self.popular_season = Some(popular_season);
        }
        if let Some(tags) = request.tags {
            self.tags = tags;
        }
        if let Some(image_url) = request.image_url {
            self.image_url = Some(image_url);
        }
        if let Some(amap_id) = request.amap_id {
            self.amap_id = Some(amap_id);
        }
        self.updated_at = Utc::now();
    }

    /// 转换为响应格式
    pub fn to_response(self) -> DestinationResponse {
        DestinationResponse {
            id: self.id,
            name: self.name,
            name_en: self.name_en,
            description: self.description,
            destination_type: self.destination_type,
            climate: self.climate,
            country: self.country,
            province: self.province,
            city: self.city,
            latitude: self.latitude,
            longitude: self.longitude,
            timezone: self.timezone,
            popular_season: self.popular_season,
            tags: self.tags,
            image_url: self.image_url,
            created_at: self.created_at,
            updated_at: self.updated_at,
            amap_id: self.amap_id,
        }
    }

    /// 转换为列表项格式
    pub fn to_list_item(self) -> DestinationListItem {
        DestinationListItem {
            id: self.id,
            name: self.name,
            destination_type: self.destination_type,
            country: self.country,
            city: self.city,
            image_url: self.image_url,
        }
    }
}

impl From<Destination> for DestinationResponse {
    fn from(destination: Destination) -> Self {
        destination.to_response()
    }
}

impl From<Destination> for DestinationListItem {
    fn from(destination: Destination) -> Self {
        destination.to_list_item()
    }
}
