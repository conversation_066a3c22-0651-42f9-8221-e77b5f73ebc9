// 对话数据模型 - 定义对话相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 对话数据模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Conversation {
    pub id: Uuid,
    pub user_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_archived: bool,
    pub message_count: i32,
    pub last_message_at: Option<DateTime<Utc>>,
}

/// 对话创建请求
#[derive(Debug, Deserialize)]
pub struct CreateConversationRequest {
    pub title: Option<String>,
    pub description: Option<String>,
}

/// 对话更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateConversationRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub is_archived: Option<bool>,
}

/// 对话响应
#[derive(Debug, Serialize)]
pub struct ConversationResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub title: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_archived: bool,
    pub message_count: i32,
    pub last_message_at: Option<DateTime<Utc>>,
}

/// 对话列表项（简化版本）
#[derive(Debug, Serialize)]
pub struct ConversationListItem {
    pub id: Uuid,
    pub title: String,
    pub message_count: i32,
    pub last_message_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub is_archived: bool,
}

/// 对话统计信息
#[derive(Debug, Serialize)]
pub struct ConversationStats {
    pub total_messages: i64,
    pub total_files: i64,
    pub first_message_at: Option<DateTime<Utc>>,
    pub last_message_at: Option<DateTime<Utc>>,
    pub average_messages_per_day: f64,
}

impl From<Conversation> for ConversationResponse {
    fn from(conversation: Conversation) -> Self {
        Self {
            id: conversation.id,
            user_id: conversation.user_id,
            title: conversation.title,
            description: conversation.description,
            created_at: conversation.created_at,
            updated_at: conversation.updated_at,
            is_archived: conversation.is_archived,
            message_count: conversation.message_count,
            last_message_at: conversation.last_message_at,
        }
    }
}

impl From<Conversation> for ConversationListItem {
    fn from(conversation: Conversation) -> Self {
        Self {
            id: conversation.id,
            title: conversation.title,
            message_count: conversation.message_count,
            last_message_at: conversation.last_message_at,
            created_at: conversation.created_at,
            is_archived: conversation.is_archived,
        }
    }
}

impl Conversation {
    /// 创建新对话
    pub fn new(user_id: Uuid, title: Option<String>, description: Option<String>) -> Self {
        let now = Utc::now();
        let default_title = format!("对话 {}", now.format("%Y-%m-%d %H:%M"));

        Self {
            id: Uuid::new_v4(),
            user_id,
            title: title.unwrap_or(default_title),
            description,
            created_at: now,
            updated_at: now,
            is_archived: false,
            message_count: 0,
            last_message_at: None,
        }
    }

    /// 更新对话信息
    pub fn update(&mut self, request: UpdateConversationRequest) {
        if let Some(title) = request.title {
            self.title = title;
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(is_archived) = request.is_archived {
            self.is_archived = is_archived;
        }
        self.updated_at = Utc::now();
    }

    /// 归档对话
    pub fn archive(&mut self) {
        self.is_archived = true;
        self.updated_at = Utc::now();
    }

    /// 取消归档
    pub fn unarchive(&mut self) {
        self.is_archived = false;
        self.updated_at = Utc::now();
    }

    /// 更新消息计数和最后消息时间
    pub fn update_message_stats(&mut self, message_count: i32, last_message_at: DateTime<Utc>) {
        self.message_count = message_count;
        self.last_message_at = Some(last_message_at);
        self.updated_at = Utc::now();
    }

    /// 增加消息计数
    pub fn increment_message_count(&mut self) {
        self.message_count += 1;
        self.last_message_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 转换为响应格式
    pub fn to_response(self) -> ConversationResponse {
        ConversationResponse::from(self)
    }

    /// 转换为列表项格式
    pub fn to_list_item(self) -> ConversationListItem {
        ConversationListItem::from(self)
    }
}
