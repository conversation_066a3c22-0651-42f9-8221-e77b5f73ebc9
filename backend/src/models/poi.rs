// POI（兴趣点）数据模型 - 定义兴趣点相关的数据结构
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// POI类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PoiType {
    /// 景点
    Attraction,
    /// 餐厅
    Restaurant,
    /// 酒店
    Hotel,
    /// 购物
    Shopping,
    /// 交通
    Transportation,
    /// 娱乐
    Entertainment,
    /// 医疗
    Medical,
    /// 银行
    Bank,
    /// 其他
    Other,
}

/// POI评级枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PoiRating {
    /// 一星
    OneStar,
    /// 二星
    TwoStar,
    /// 三星
    ThreeStar,
    /// 四星
    FourStar,
    /// 五星
    FiveStar,
}

/// POI数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Poi {
    pub id: Uuid,
    pub name: String,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub poi_type: PoiType,
    pub rating: Option<PoiRating>,
    pub score: Option<f32>,  // 评分 0.0-5.0
    pub address: String,
    pub latitude: f64,
    pub longitude: f64,
    pub phone: Option<String>,
    pub website: Option<String>,
    pub opening_hours: Option<String>,
    pub price_range: Option<String>,
    pub tags: Vec<String>,
    pub images: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub amap_id: Option<String>,  // 高德地图POI ID
    pub amap_data: Option<serde_json::Value>,  // 高德地图原始数据
}

/// POI创建请求
#[derive(Debug, Deserialize)]
pub struct CreatePoiRequest {
    pub name: String,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub poi_type: PoiType,
    pub rating: Option<PoiRating>,
    pub score: Option<f32>,
    pub address: String,
    pub latitude: f64,
    pub longitude: f64,
    pub phone: Option<String>,
    pub website: Option<String>,
    pub opening_hours: Option<String>,
    pub price_range: Option<String>,
    pub tags: Vec<String>,
    pub images: Vec<String>,
    pub amap_id: Option<String>,
    pub amap_data: Option<serde_json::Value>,
}

/// POI更新请求
#[derive(Debug, Deserialize)]
pub struct UpdatePoiRequest {
    pub name: Option<String>,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub poi_type: Option<PoiType>,
    pub rating: Option<PoiRating>,
    pub score: Option<f32>,
    pub address: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub phone: Option<String>,
    pub website: Option<String>,
    pub opening_hours: Option<String>,
    pub price_range: Option<String>,
    pub tags: Option<Vec<String>>,
    pub images: Option<Vec<String>>,
    pub amap_id: Option<String>,
    pub amap_data: Option<serde_json::Value>,
}

/// POI响应
#[derive(Debug, Serialize)]
pub struct PoiResponse {
    pub id: Uuid,
    pub name: String,
    pub name_en: Option<String>,
    pub description: Option<String>,
    pub poi_type: PoiType,
    pub rating: Option<PoiRating>,
    pub score: Option<f32>,
    pub address: String,
    pub latitude: f64,
    pub longitude: f64,
    pub phone: Option<String>,
    pub website: Option<String>,
    pub opening_hours: Option<String>,
    pub price_range: Option<String>,
    pub tags: Vec<String>,
    pub images: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub amap_id: Option<String>,
}

/// POI列表项（简化版本）
#[derive(Debug, Serialize)]
pub struct PoiListItem {
    pub id: Uuid,
    pub name: String,
    pub poi_type: PoiType,
    pub rating: Option<PoiRating>,
    pub score: Option<f32>,
    pub address: String,
    pub latitude: f64,
    pub longitude: f64,
}

/// POI搜索请求
#[derive(Debug, Deserialize)]
pub struct PoiSearchRequest {
    pub query: Option<String>,
    pub poi_type: Option<PoiType>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
    pub radius: Option<f64>,  // 搜索半径（米）
    pub tags: Option<Vec<String>>,
    pub min_score: Option<f32>,
    pub limit: Option<i32>,
    pub offset: Option<i32>,
}

impl Poi {
    /// 创建新POI
    pub fn new(request: CreatePoiRequest) -> Self {
        let now = Utc::now();
        
        Self {
            id: Uuid::new_v4(),
            name: request.name,
            name_en: request.name_en,
            description: request.description,
            poi_type: request.poi_type,
            rating: request.rating,
            score: request.score,
            address: request.address,
            latitude: request.latitude,
            longitude: request.longitude,
            phone: request.phone,
            website: request.website,
            opening_hours: request.opening_hours,
            price_range: request.price_range,
            tags: request.tags,
            images: request.images,
            created_at: now,
            updated_at: now,
            amap_id: request.amap_id,
            amap_data: request.amap_data,
        }
    }

    /// 更新POI信息
    pub fn update(&mut self, request: UpdatePoiRequest) {
        if let Some(name) = request.name {
            self.name = name;
        }
        if let Some(name_en) = request.name_en {
            self.name_en = Some(name_en);
        }
        if let Some(description) = request.description {
            self.description = Some(description);
        }
        if let Some(poi_type) = request.poi_type {
            self.poi_type = poi_type;
        }
        if let Some(rating) = request.rating {
            self.rating = Some(rating);
        }
        if let Some(score) = request.score {
            self.score = Some(score);
        }
        if let Some(address) = request.address {
            self.address = address;
        }
        if let Some(latitude) = request.latitude {
            self.latitude = latitude;
        }
        if let Some(longitude) = request.longitude {
            self.longitude = longitude;
        }
        if let Some(phone) = request.phone {
            self.phone = Some(phone);
        }
        if let Some(website) = request.website {
            self.website = Some(website);
        }
        if let Some(opening_hours) = request.opening_hours {
            self.opening_hours = Some(opening_hours);
        }
        if let Some(price_range) = request.price_range {
            self.price_range = Some(price_range);
        }
        if let Some(tags) = request.tags {
            self.tags = tags;
        }
        if let Some(images) = request.images {
            self.images = images;
        }
        if let Some(amap_id) = request.amap_id {
            self.amap_id = Some(amap_id);
        }
        if let Some(amap_data) = request.amap_data {
            self.amap_data = Some(amap_data);
        }
        self.updated_at = Utc::now();
    }

    /// 转换为响应格式
    pub fn to_response(self) -> PoiResponse {
        PoiResponse {
            id: self.id,
            name: self.name,
            name_en: self.name_en,
            description: self.description,
            poi_type: self.poi_type,
            rating: self.rating,
            score: self.score,
            address: self.address,
            latitude: self.latitude,
            longitude: self.longitude,
            phone: self.phone,
            website: self.website,
            opening_hours: self.opening_hours,
            price_range: self.price_range,
            tags: self.tags,
            images: self.images,
            created_at: self.created_at,
            updated_at: self.updated_at,
            amap_id: self.amap_id,
        }
    }

    /// 转换为列表项格式
    pub fn to_list_item(self) -> PoiListItem {
        PoiListItem {
            id: self.id,
            name: self.name,
            poi_type: self.poi_type,
            rating: self.rating,
            score: self.score,
            address: self.address,
            latitude: self.latitude,
            longitude: self.longitude,
        }
    }
}

impl From<Poi> for PoiResponse {
    fn from(poi: Poi) -> Self {
        poi.to_response()
    }
}

impl From<Poi> for PoiListItem {
    fn from(poi: Poi) -> Self {
        poi.to_list_item()
    }
}
