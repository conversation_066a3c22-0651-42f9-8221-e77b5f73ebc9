// 数据验证工具 - 输入数据验证和清理
use regex::Regex;
use serde_json::{json, Value};
use std::collections::HashMap;

/// 验证结果
#[derive(Debug)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: HashMap<String, Vec<String>>,
}

impl ValidationResult {
    pub fn new() -> Self {
        Self {
            is_valid: true,
            errors: HashMap::new(),
        }
    }

    pub fn add_error(&mut self, field: &str, message: &str) {
        self.is_valid = false;
        self.errors
            .entry(field.to_string())
            .or_insert_with(Vec::new)
            .push(message.to_string());
    }

    pub fn to_json(&self) -> Value {
        json!({
            "errors": self.errors
        })
    }
}

/// 验证器
pub struct Validator;

impl Validator {
    /// 验证邮箱格式
    pub fn validate_email(email: &str) -> bool {
        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();
        email_regex.is_match(email)
    }

    /// 验证密码强度
    pub fn validate_password(password: &str) -> ValidationResult {
        let mut result = ValidationResult::new();

        if password.len() < 8 {
            result.add_error("password", "密码长度至少8位");
        }

        if password.len() > 128 {
            result.add_error("password", "密码长度不能超过128位");
        }

        if !password.chars().any(|c| c.is_ascii_lowercase()) {
            result.add_error("password", "密码必须包含小写字母");
        }

        if !password.chars().any(|c| c.is_ascii_uppercase()) {
            result.add_error("password", "密码必须包含大写字母");
        }

        if !password.chars().any(|c| c.is_ascii_digit()) {
            result.add_error("password", "密码必须包含数字");
        }

        if !password
            .chars()
            .any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c))
        {
            result.add_error("password", "密码必须包含特殊字符");
        }

        result
    }

    /// 验证用户名
    pub fn validate_username(username: &str) -> ValidationResult {
        let mut result = ValidationResult::new();

        if username.is_empty() {
            result.add_error("username", "用户名不能为空");
            return result;
        }

        if username.len() < 3 {
            result.add_error("username", "用户名长度至少3位");
        }

        if username.len() > 50 {
            result.add_error("username", "用户名长度不能超过50位");
        }

        let username_regex = Regex::new(r"^[a-zA-Z0-9_\u4e00-\u9fa5]+$").unwrap();
        if !username_regex.is_match(username) {
            result.add_error("username", "用户名只能包含字母、数字、下划线和中文字符");
        }

        result
    }

    /// 验证文件类型
    pub fn validate_file_type(filename: &str, allowed_types: &[String]) -> bool {
        if let Some(extension) = filename.split('.').last() {
            let extension = extension.to_lowercase();

            // 创建扩展名到MIME类型的映射
            let mime_mapping = |ext: &str| -> Vec<&str> {
                match ext {
                    "jpg" | "jpeg" => vec!["image/jpeg"],
                    "png" => vec!["image/png"],
                    "gif" => vec!["image/gif"],
                    "webp" => vec!["image/webp"],
                    "pdf" => vec!["application/pdf"],
                    "txt" => vec!["text/plain"],
                    "md" => vec!["text/markdown"],
                    "mp3" => vec!["audio/mpeg"],
                    "wav" => vec!["audio/wav"],
                    "mp4" => vec!["video/mp4"],
                    _ => vec![],
                }
            };

            let possible_mimes = mime_mapping(&extension);
            allowed_types.iter().any(|allowed_type| {
                possible_mimes.contains(&allowed_type.as_str())
                    || allowed_type.split('/').last().unwrap_or("").to_lowercase() == extension
                    || allowed_type.ends_with(&format!("/{}", extension))
            })
        } else {
            false
        }
    }

    /// 验证文件大小
    pub fn validate_file_size(size: u64, max_size: u64) -> bool {
        size <= max_size
    }

    /// 验证消息内容
    pub fn validate_message_content(content: &str) -> ValidationResult {
        let mut result = ValidationResult::new();

        if content.is_empty() {
            result.add_error("content", "消息内容不能为空");
            return result;
        }

        if content.len() > 10000 {
            result.add_error("content", "消息内容不能超过10000字符");
        }

        // 检查是否包含恶意内容（简单示例）
        let forbidden_patterns = ["<script", "javascript:", "data:text/html"];
        for pattern in &forbidden_patterns {
            if content.to_lowercase().contains(pattern) {
                result.add_error("content", "消息内容包含不允许的内容");
                break;
            }
        }

        result
    }

    /// 验证分页参数
    pub fn validate_pagination(page: Option<u32>, per_page: Option<u32>) -> ValidationResult {
        let mut result = ValidationResult::new();

        if let Some(page) = page {
            if page == 0 {
                result.add_error("page", "页码必须大于0");
            }
        }

        if let Some(per_page) = per_page {
            if per_page == 0 {
                result.add_error("per_page", "每页数量必须大于0");
            }
            if per_page > 100 {
                result.add_error("per_page", "每页数量不能超过100");
            }
        }

        result
    }

    /// 清理HTML标签
    pub fn sanitize_html(input: &str) -> String {
        // 简单的HTML标签清理，实际项目中建议使用专门的库如ammonia
        let html_regex = Regex::new(r"<[^>]*>").unwrap();
        html_regex.replace_all(input, "").to_string()
    }

    /// 清理SQL注入风险字符
    pub fn sanitize_sql_input(input: &str) -> String {
        input
            .replace("'", "''")
            .replace("\"", "\\\"")
            .replace(";", "\\;")
            .replace("--", "\\--")
            .replace("/*", "\\/*")
            .replace("*/", "\\*/")
    }

    /// 验证UUID格式
    pub fn validate_uuid(uuid_str: &str) -> bool {
        uuid::Uuid::parse_str(uuid_str).is_ok()
    }

    /// 验证手机号码（中国大陆）
    pub fn validate_phone_number(phone: &str) -> bool {
        let phone_regex = Regex::new(r"^1[3-9]\d{9}$").unwrap();
        phone_regex.is_match(phone)
    }
}

/// 用户注册数据验证
pub fn validate_user_registration(username: &str, email: &str, password: &str) -> ValidationResult {
    let mut result = ValidationResult::new();

    // 验证用户名
    let username_validation = Validator::validate_username(username);
    if !username_validation.is_valid {
        for (field, errors) in username_validation.errors {
            for error in errors {
                result.add_error(&field, &error);
            }
        }
    }

    // 验证邮箱
    if !Validator::validate_email(email) {
        result.add_error("email", "邮箱格式不正确");
    }

    // 验证密码
    let password_validation = Validator::validate_password(password);
    if !password_validation.is_valid {
        for (field, errors) in password_validation.errors {
            for error in errors {
                result.add_error(&field, &error);
            }
        }
    }

    result
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_email_validation() {
        assert!(Validator::validate_email("<EMAIL>"));
        assert!(Validator::validate_email("<EMAIL>"));
        assert!(!Validator::validate_email("invalid-email"));
        assert!(!Validator::validate_email("@domain.com"));
        assert!(!Validator::validate_email("user@"));
    }

    #[test]
    fn test_password_validation() {
        let result = Validator::validate_password("StrongPass123!");
        assert!(result.is_valid);

        let result = Validator::validate_password("weak");
        assert!(!result.is_valid);
        assert!(result.errors.contains_key("password"));
    }

    #[test]
    fn test_username_validation() {
        let result = Validator::validate_username("validuser123");
        assert!(result.is_valid);

        let result = Validator::validate_username("中文用户名");
        assert!(result.is_valid);

        let result = Validator::validate_username("ab");
        assert!(!result.is_valid);
    }

    #[test]
    fn test_file_type_validation() {
        let allowed_types = vec!["image/jpeg".to_string(), "image/png".to_string()];
        assert!(Validator::validate_file_type("photo.jpg", &allowed_types));
        assert!(Validator::validate_file_type("image.png", &allowed_types));
        assert!(!Validator::validate_file_type(
            "document.pdf",
            &allowed_types
        ));
    }
}
