// 加密工具 - 密码哈希和JWT令牌处理
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON>er, TokenData, Validation};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// JWT声明结构
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // 用户ID
    pub username: String, // 用户名
    pub exp: i64,        // 过期时间
    pub iat: i64,        // 签发时间
    pub jti: String,     // JWT ID
}

impl Claims {
    /// 创建新的JWT声明
    pub fn new(user_id: String, username: String, expiration_hours: u64) -> Self {
        let now = Utc::now();
        let exp = now + Duration::hours(expiration_hours as i64);
        
        Self {
            sub: user_id,
            username,
            exp: exp.timestamp(),
            iat: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
        }
    }

    /// 检查令牌是否过期
    pub fn is_expired(&self) -> bool {
        Utc::now().timestamp() > self.exp
    }
}

/// 密码工具
pub struct PasswordUtils;

impl PasswordUtils {
    /// 哈希密码
    pub fn hash_password(password: &str) -> Result<String, bcrypt::BcryptError> {
        hash(password, DEFAULT_COST)
    }

    /// 验证密码
    pub fn verify_password(password: &str, hash: &str) -> Result<bool, bcrypt::BcryptError> {
        verify(password, hash)
    }

    /// 生成随机密码
    pub fn generate_random_password(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789\
                                !@#$%^&*";
        let mut rng = rand::thread_rng();
        
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }
}

/// JWT工具
pub struct JwtUtils;

impl JwtUtils {
    /// 生成JWT令牌
    pub fn generate_token(
        user_id: String,
        username: String,
        secret: &str,
        expiration_hours: u64,
    ) -> Result<String, jsonwebtoken::errors::Error> {
        let claims = Claims::new(user_id, username, expiration_hours);
        let header = Header::default();
        let encoding_key = EncodingKey::from_secret(secret.as_ref());
        
        encode(&header, &claims, &encoding_key)
    }

    /// 验证JWT令牌
    pub fn verify_token(
        token: &str,
        secret: &str,
    ) -> Result<TokenData<Claims>, jsonwebtoken::errors::Error> {
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        let validation = Validation::default();
        
        decode::<Claims>(token, &decoding_key, &validation)
    }

    /// 从令牌中提取用户ID
    pub fn extract_user_id(token: &str, secret: &str) -> Result<String, jsonwebtoken::errors::Error> {
        let token_data = Self::verify_token(token, secret)?;
        Ok(token_data.claims.sub)
    }

    /// 从令牌中提取用户名
    pub fn extract_username(token: &str, secret: &str) -> Result<String, jsonwebtoken::errors::Error> {
        let token_data = Self::verify_token(token, secret)?;
        Ok(token_data.claims.username)
    }

    /// 检查令牌是否即将过期（1小时内）
    pub fn is_token_expiring_soon(token: &str, secret: &str) -> Result<bool, jsonwebtoken::errors::Error> {
        let token_data = Self::verify_token(token, secret)?;
        let now = Utc::now().timestamp();
        let expires_in = token_data.claims.exp - now;
        
        Ok(expires_in < 3600) // 1小时 = 3600秒
    }

    /// 刷新令牌（如果即将过期）
    pub fn refresh_token_if_needed(
        token: &str,
        secret: &str,
        expiration_hours: u64,
    ) -> Result<Option<String>, jsonwebtoken::errors::Error> {
        if Self::is_token_expiring_soon(token, secret)? {
            let token_data = Self::verify_token(token, secret)?;
            let new_token = Self::generate_token(
                token_data.claims.sub,
                token_data.claims.username,
                secret,
                expiration_hours,
            )?;
            Ok(Some(new_token))
        } else {
            Ok(None)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hashing() {
        let password = "test_password_123";
        let hash = PasswordUtils::hash_password(password).unwrap();
        
        assert!(PasswordUtils::verify_password(password, &hash).unwrap());
        assert!(!PasswordUtils::verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_jwt_generation_and_verification() {
        let secret = "test_secret";
        let user_id = "user123".to_string();
        let username = "testuser".to_string();
        
        let token = JwtUtils::generate_token(user_id.clone(), username.clone(), secret, 24).unwrap();
        let token_data = JwtUtils::verify_token(&token, secret).unwrap();
        
        assert_eq!(token_data.claims.sub, user_id);
        assert_eq!(token_data.claims.username, username);
        assert!(!token_data.claims.is_expired());
    }

    #[test]
    fn test_random_password_generation() {
        let password = PasswordUtils::generate_random_password(12);
        assert_eq!(password.len(), 12);
        
        // 生成两个密码应该不同
        let password2 = PasswordUtils::generate_random_password(12);
        assert_ne!(password, password2);
    }
}
