// 配置管理工具 - 加载和管理应用配置
use config::{Config, ConfigError, File};
use serde::Deserialize;
use std::env;

#[derive(Debug, Deserialize, Clone)]
pub struct Settings {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub qianwen: QianwenConfig,
    pub auth: AuthConfig,
    pub upload: UploadConfig,
    pub speech: SpeechConfig,
    pub amap: AmapConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Deserialize, Clone)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub static_dir: String,
}

#[derive(Debug, Deserialize, Clone)]
pub struct DatabaseConfig {
    pub url: String,
}

#[derive(Debug, Deserialize, Clone)]
pub struct QianwenConfig {
    pub api_key: String,
    pub base_url: String,
    pub model_text: String,
    pub model_vision: String,
    // MCP集成配置
    #[serde(default)]
    pub enable_mcp_integration: bool,
    #[serde(default = "default_mcp_context_window")]
    pub mcp_context_window: u32,
    #[serde(default = "default_mcp_fallback_enabled")]
    pub mcp_fallback_enabled: bool,
}

fn default_mcp_context_window() -> u32 {
    8192
}

fn default_mcp_fallback_enabled() -> bool {
    true
}

#[derive(Debug, Deserialize, Clone)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub jwt_expiration_hours: u64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct UploadConfig {
    pub max_file_size: u64,
    pub allowed_image_types: Vec<String>,
    pub allowed_audio_types: Vec<String>,
    pub upload_dir: String,
}

#[derive(Debug, Deserialize, Clone)]
pub struct SpeechConfig {
    pub stt_provider: Option<String>,
    pub tts_provider: Option<String>,
    pub stt_api_key: Option<String>,
    pub tts_api_key: Option<String>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct AmapConfig {
    pub api_key: String,
    pub base_url: String,
    pub geocoding_url: String,
    pub reverse_geocoding_url: String,
    pub poi_search_url: String,
    pub poi_around_url: String,
    pub direction_driving_url: String,
    pub direction_walking_url: String,
    pub direction_transit_url: String,
    pub weather_url: String,
    // MCP服务配置
    #[serde(default)]
    pub mcp_enabled: bool,
    #[serde(default = "default_mcp_server_url")]
    pub mcp_server_url: String,
    #[serde(default = "default_mcp_api_key")]
    pub mcp_api_key: String,
    #[serde(default = "default_mcp_max_connections")]
    pub mcp_max_connections: u32,
    #[serde(default = "default_mcp_timeout_seconds")]
    pub mcp_timeout_seconds: u64,
    #[serde(default = "default_mcp_retry_attempts")]
    pub mcp_retry_attempts: u32,
    #[serde(default)]
    pub mcp_enable_caching: bool,
    #[serde(default = "default_mcp_cache_ttl_seconds")]
    pub mcp_cache_ttl_seconds: u64,
}

fn default_mcp_server_url() -> String {
    "https://mcp.amap.com/sse".to_string()
}

fn default_mcp_api_key() -> String {
    "".to_string()
}

fn default_mcp_max_connections() -> u32 {
    100
}

fn default_mcp_timeout_seconds() -> u64 {
    30
}

fn default_mcp_retry_attempts() -> u32 {
    3
}

fn default_mcp_cache_ttl_seconds() -> u64 {
    300
}

#[derive(Debug, Deserialize, Clone)]
pub struct LoggingConfig {
    pub level: String,
}

impl Settings {
    /// 从配置文件加载设置
    pub fn new() -> Result<Self, ConfigError> {
        let config_path =
            env::var("CONFIG_PATH").unwrap_or_else(|_| "../config/config.toml".to_string());

        let settings = Config::builder()
            .add_source(File::with_name(&config_path))
            .build()?;

        settings.try_deserialize()
    }

    /// 获取数据库连接URL
    pub fn database_url(&self) -> &str {
        &self.database.url
    }

    /// 获取服务器监听地址
    pub fn server_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }

    /// 检查配置是否有效
    pub fn validate(&self) -> Result<(), String> {
        // 在开发环境中，允许使用默认配置
        let is_dev = std::env::var("RUST_ENV").unwrap_or_else(|_| "development".to_string())
            == "development";

        if !is_dev {
            // 生产环境检查
            if self.qianwen.api_key.is_empty()
                || self.qianwen.api_key == "your_qianwen_api_key_here"
            {
                return Err("通义千问API密钥未配置".to_string());
            }

            if self.auth.jwt_secret.is_empty()
                || self.auth.jwt_secret == "your_jwt_secret_key_here_change_in_production"
            {
                return Err("JWT密钥未配置或使用默认值".to_string());
            }
        }

        if self.server.port == 0 {
            return Err("服务器端口配置无效".to_string());
        }

        Ok(())
    }
}
