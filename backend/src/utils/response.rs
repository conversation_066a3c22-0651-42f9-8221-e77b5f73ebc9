// 响应格式化工具 - 统一API响应格式
use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use serde::{Deserialize, Serialize};

/// 标准API响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<ApiError>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
}

/// API错误结构
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<serde_json::Value>,
}

impl<T> ApiResponse<T>
where
    T: Serialize,
{
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: None,
        }
    }

    /// 创建成功响应（带消息）
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: Some(message),
        }
    }
}

impl ApiResponse<()> {
    /// 创建错误响应
    pub fn error(code: String, message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(ApiError {
                code,
                message,
                details: None,
            }),
            message: None,
        }
    }

    /// 创建错误响应（带详细信息）
    pub fn error_with_details(code: String, message: String, details: serde_json::Value) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(ApiError {
                code,
                message,
                details: Some(details),
            }),
            message: None,
        }
    }

    /// 创建简单成功响应
    pub fn ok() -> Self {
        Self {
            success: true,
            data: None,
            error: None,
            message: None,
        }
    }

    /// 创建简单成功响应（带消息）
    pub fn ok_with_message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            error: None,
            message: Some(message),
        }
    }
}

/// 快速创建成功响应的宏
#[macro_export]
macro_rules! success_response {
    ($data:expr) => {
        (StatusCode::OK, Json(ApiResponse::success($data)))
    };
    ($data:expr, $message:expr) => {
        (
            StatusCode::OK,
            Json(ApiResponse::success_with_message(
                $data,
                $message.to_string(),
            )),
        )
    };
}

/// 快速创建错误响应的宏
#[macro_export]
macro_rules! error_response {
    ($status:expr, $code:expr, $message:expr) => {
        (
            $status,
            Json(ApiResponse::error($code.to_string(), $message.to_string())),
        )
    };
    ($status:expr, $code:expr, $message:expr, $details:expr) => {
        (
            $status,
            Json(ApiResponse::error_with_details(
                $code.to_string(),
                $message.to_string(),
                $details,
            )),
        )
    };
}

/// 常用错误响应
pub struct ErrorResponses;

impl ErrorResponses {
    pub fn bad_request(message: &str) -> Response {
        error_response!(StatusCode::BAD_REQUEST, "BAD_REQUEST", message).into_response()
    }

    pub fn unauthorized() -> Response {
        error_response!(StatusCode::UNAUTHORIZED, "UNAUTHORIZED", "未授权访问").into_response()
    }

    pub fn forbidden() -> Response {
        error_response!(StatusCode::FORBIDDEN, "FORBIDDEN", "禁止访问").into_response()
    }

    pub fn not_found(resource: &str) -> Response {
        error_response!(
            StatusCode::NOT_FOUND,
            "NOT_FOUND",
            &format!("{}未找到", resource)
        )
        .into_response()
    }

    pub fn internal_error() -> Response {
        error_response!(
            StatusCode::INTERNAL_SERVER_ERROR,
            "INTERNAL_ERROR",
            "服务器内部错误"
        )
        .into_response()
    }

    pub fn validation_error(details: serde_json::Value) -> Response {
        error_response!(
            StatusCode::BAD_REQUEST,
            "VALIDATION_ERROR",
            "数据验证失败",
            details
        )
        .into_response()
    }
}

/// 分页响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
    pub total_pages: u32,
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: u64, page: u32, per_page: u32) -> Self {
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
        Self {
            items,
            total,
            page,
            per_page,
            total_pages,
        }
    }
}
