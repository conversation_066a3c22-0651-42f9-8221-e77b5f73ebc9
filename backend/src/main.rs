// AI聊天应用后端服务器
// 使用Axum框架构建的多模态聊天API服务

mod handlers;
mod middleware;
mod models;
mod routes;
mod services;
mod utils;

use axum::middleware as axum_middleware;
use std::{net::SocketAddr, sync::Arc};
use tower_http::trace::TraceLayer;
use tracing_subscriber::{fmt, EnvFilter};

use crate::{
    middleware::{cors::create_cors_layer, logging::request_logging_middleware},
    services::{memory::MemoryStorageService, qianwen::QianwenService},
    utils::config::Settings,
};

fn init_tracing() {
    tracing_subscriber::fmt()
        // 1. 通过环境变量动态控制日志级别（可选）
        .with_env_filter(EnvFilter::from_default_env())
        // 2. 显示时间（UTC 时间戳）
        .with_timer(fmt::time::UtcTime::rfc_3339())
        // 3. 显示目标模块路径
        .with_target(true)
        // 4. 显示文件和行号
        .with_file(true)
        .with_line_number(true)
        // 5. 显示线程名（对异步调试有用）
        .with_thread_names(true)
        .with_thread_ids(true)
        // 6. 设置日志级别
        .with_max_level(tracing::Level::DEBUG)
        // 7. 启用日志
        .init();
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    let settings = Settings::new().expect("无法加载配置文件");

    // 验证配置
    if let Err(err) = settings.validate() {
        eprintln!("配置验证失败: {}", err);
        std::process::exit(1);
    }

    // 初始化日志系统
    init_tracing();

    // 初始化内存存储服务（开发阶段使用）
    let storage = MemoryStorageService::new();

    println!("💾 内存存储服务初始化完成（开发模式）");

    // 初始化通义千问服务（带MCP支持）
    let mut qianwen = if settings.qianwen.enable_mcp_integration && settings.amap.mcp_enabled {
        println!("🗺️ 启用高德地图MCP集成");
        let mut service =
            QianwenService::new_with_amap(settings.qianwen.clone(), settings.amap.clone())?;

        // 初始化MCP连接
        println!("🔗 正在初始化MCP连接...");
        if let Err(e) = service.initialize_mcp().await {
            eprintln!("⚠️ MCP连接初始化失败: {}", e);
            if !settings.qianwen.mcp_fallback_enabled {
                return Err(e.into());
            }
            println!("🔄 MCP降级模式已启用，继续启动服务");
        } else {
            println!("✅ MCP连接初始化成功");
        }

        service
    } else {
        QianwenService::new(settings.qianwen.clone())?
    };

    println!("🤖 通义千问API服务初始化完成");

    // 创建共享状态
    let shared_settings = Arc::new(settings.clone());
    let shared_storage = Arc::new(storage);
    let shared_qianwen = Arc::new(qianwen);

    // 创建应用路由 - 使用模块化路由结构
    let app = routes::create_app_routes()
        // 添加中间件
        .layer(axum_middleware::from_fn(request_logging_middleware))
        .layer(create_cors_layer())
        .layer(TraceLayer::new_for_http())
        // 添加共享状态
        .with_state((shared_settings, shared_storage, shared_qianwen));

    // 服务器地址配置
    let addr: SocketAddr = settings.server_address().parse()?;

    println!("🚀 AI聊天服务器启动在 http://{}", addr);
    println!("📋 配置文件已加载");

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
