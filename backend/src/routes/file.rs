// 文件管理路由模块
// 处理文件上传、下载、预览等文件相关的路由

use axum::{
    extract::{Multipart, Path as AxumPath, State},
    http::{header, StatusCode},
    response::Response,
    routing::{get, post},
    Json, Router,
};
use serde::Serialize;
use std::sync::Arc;
use uuid::Uuid;

use crate::{
    middleware::error::{AppError, AppResult},
    models::file::{File, FileResponse, FileType},
    utils::{response::ApiResponse, validation::Validator},
};

use super::AppState;

/// 创建文件管理相关路由
///
/// 定义所有文件管理功能的API端点：
/// - 文件上传（单个和批量）
/// - 文件下载和预览
/// - 文件信息查询和管理
///
/// 注意：开发阶段这些路由被禁用，但保留结构以便未来启用
pub fn create_file_routes() -> Router<AppState> {
    Router::new()
        // 单文件上传
        // POST /api/files/upload
        .route("/upload", post(upload_file))
        // 批量文件上传
        // POST /api/files/upload/multiple
        .route("/upload/multiple", post(upload_multiple_files))
        // 文件下载
        // GET /api/files/{filename}/download
        .route("/{filename}/download", get(download_file))
        // 文件内联查看（主要用于图片）
        // GET /api/files/{filename}/view
        .route("/{filename}", get(view_file))

    // 获取用户文件列表
    // GET /api/files?page=1&per_page=20&file_type=image
    // .route("/", get(get_user_files))

    // 获取文件信息
    // GET /api/files/{id}
    // .route("/{id}", get(get_file_info))

    // 删除文件
    // DELETE /api/files/{id}
    // .route("/{id}", delete(delete_file))

    // 图片预览
    // GET /api/files/{id}/preview
    // .route("/{id}/preview", get(preview_image))

    // 清理过期文件
    // POST /api/files/cleanup
    // .route("/cleanup", post(cleanup_expired_files))

    // 文件分享
    // POST /api/files/{id}/share
    // .route("/{id}/share", post(create_file_share))

    // 获取分享文件
    // GET /api/files/shared/{share_id}
    // .route("/shared/{share_id}", get(get_shared_file))
}

/// 文件路由配置选项
#[derive(Debug, Clone)]
pub struct FileRouteConfig {
    /// 是否启用文件上传功能
    pub enable_file_upload: bool,
    /// 是否启用批量上传
    pub enable_batch_upload: bool,
    /// 是否启用文件预览
    pub enable_file_preview: bool,
    /// 是否启用文件分享
    pub enable_file_sharing: bool,
    /// 是否启用文件清理
    pub enable_file_cleanup: bool,
    /// 支持的文件类型
    pub supported_file_types: Vec<String>,
    /// 最大文件大小（字节）
    pub max_file_size: u64,
}

impl Default for FileRouteConfig {
    fn default() -> Self {
        Self {
            enable_file_upload: false,  // 开发阶段禁用
            enable_batch_upload: false, // 开发阶段禁用
            enable_file_preview: false, // 开发阶段禁用
            enable_file_sharing: false, // 未来功能
            enable_file_cleanup: false, // 未来功能
            supported_file_types: vec![
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "image/webp".to_string(),
                "application/pdf".to_string(),
                "text/plain".to_string(),
            ],
            max_file_size: 10 * 1024 * 1024, // 10MB
        }
    }
}

/// 根据配置创建条件性文件路由
///
/// 允许根据配置动态启用或禁用某些文件管理功能
pub fn create_conditional_file_routes(config: FileRouteConfig) -> Router<AppState> {
    let mut router = Router::new();

    // 根据配置条件性添加路由
    if config.enable_file_upload {
        // router = router.route("/upload", post(upload_file));

        if config.enable_batch_upload {
            // router = router.route("/upload/multiple", post(upload_multiple_files));
        }

        // router = router
        //     .route("/", get(get_user_files))
        //     .route("/{id}", get(get_file_info))
        //     .route("/{id}", delete(delete_file))
        //     .route("/{id}/download", get(download_file));
    }

    if config.enable_file_preview {
        // router = router.route("/{id}/preview", get(preview_image));
    }

    if config.enable_file_sharing {
        // router = router
        //     .route("/{id}/share", post(create_file_share))
        //     .route("/shared/{share_id}", get(get_shared_file));
    }

    if config.enable_file_cleanup {
        // router = router.route("/cleanup", post(cleanup_expired_files));
    }

    router
}

/// 文件路由的中间件配置
///
/// 为文件路由添加特定的中间件，如文件大小限制、类型验证等
pub fn create_file_routes_with_middleware() -> Router<AppState> {
    create_file_routes()
    // 这里可以添加文件特定的中间件
    // .layer(file_size_limit_middleware())
    // .layer(file_type_validation_middleware())
    // .layer(file_virus_scan_middleware())
    // .layer(file_permission_middleware())
}

/// 文件存储策略配置
#[derive(Debug, Clone)]
pub enum FileStorageStrategy {
    /// 本地文件系统存储
    Local { base_path: String },
    /// 云存储（如阿里云OSS）
    Cloud { bucket: String, region: String },
    /// 混合存储策略
    Hybrid { local_cache: bool },
}

impl Default for FileStorageStrategy {
    fn default() -> Self {
        Self::Local {
            base_path: "./uploads".to_string(),
        }
    }
}

/// 根据存储策略创建文件路由
///
/// 不同的存储策略可能需要不同的路由处理逻辑
pub fn create_file_routes_with_storage(strategy: FileStorageStrategy) -> Router<AppState> {
    match strategy {
        FileStorageStrategy::Local { .. } => {
            // 本地存储的路由配置
            create_file_routes()
        }
        FileStorageStrategy::Cloud { .. } => {
            // 云存储的路由配置
            create_file_routes()
            // .layer(cloud_storage_middleware())
        }
        FileStorageStrategy::Hybrid { .. } => {
            // 混合存储的路由配置
            create_file_routes()
            // .layer(hybrid_storage_middleware())
        }
    }
}

// 文件上传处理函数

/// 文件上传响应
#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub file: FileResponse,
    pub message: String,
}

/// 批量文件上传响应
#[derive(Debug, Serialize)]
pub struct BatchUploadResponse {
    pub files: Vec<UploadResponse>,
    pub total_count: usize,
    pub success_count: usize,
    pub failed_count: usize,
}

/// 单文件上传处理函数
///
/// 处理单个文件的上传请求
/// - 支持多种文件类型（图片、文档等）
/// - 进行文件大小和类型验证
/// - 将文件保存到内存存储服务
pub async fn upload_file(
    State((_settings, storage, _)): State<AppState>,
    mut multipart: Multipart,
) -> AppResult<Json<ApiResponse<UploadResponse>>> {
    // 临时使用固定用户ID，实际应用中应从认证中间件获取
    let user_id = Uuid::new_v4();

    let mut uploaded_file: Option<File> = None;

    // 解析multipart数据
    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::BadRequest(format!("多部分数据解析失败: {}", e)))?
    {
        let field_name = field.name().unwrap_or("").to_string();

        if field_name == "file" {
            // 获取文件信息
            let filename = field
                .file_name()
                .ok_or_else(|| AppError::BadRequest("缺少文件名".to_string()))?
                .to_string();

            let content_type = field
                .content_type()
                .ok_or_else(|| AppError::BadRequest("缺少文件类型".to_string()))?
                .to_string();

            // 读取文件数据
            let data = field
                .bytes()
                .await
                .map_err(|e| AppError::BadRequest(format!("文件数据读取失败: {}", e)))?;

            // 验证文件大小
            let max_size = 10 * 1024 * 1024; // 10MB
            if data.len() > max_size {
                return Err(AppError::BadRequest(format!(
                    "文件大小超过限制，最大允许 {} MB",
                    max_size / (1024 * 1024)
                )));
            }

            // 验证文件类型
            let allowed_types = vec![
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "image/webp".to_string(),
                "application/pdf".to_string(),
                "text/plain".to_string(),
                "text/markdown".to_string(),
                "audio/mpeg".to_string(),
                "audio/wav".to_string(),
                "video/mp4".to_string(),
            ];

            if !Validator::validate_file_type(&filename, &allowed_types) {
                return Err(AppError::BadRequest(format!(
                    "不支持的文件类型: {}",
                    content_type
                )));
            }

            // 确定文件类型
            let file_type = determine_file_type(&content_type);

            // 生成唯一文件名
            let unique_filename = format!("{}_{}", Uuid::new_v4(), filename);
            let file_path = format!("./uploads/{}", unique_filename);

            // 确保上传目录存在
            if let Err(e) = tokio::fs::create_dir_all("./uploads").await {
                tracing::warn!("创建上传目录失败: {}", e);
            }

            // 将文件数据写入磁盘
            if let Err(e) = tokio::fs::write(&file_path, &data).await {
                return Err(AppError::InternalServerError(format!(
                    "文件保存失败: {}",
                    e
                )));
            }

            tracing::info!("文件已保存到磁盘: {}", file_path);

            // 创建文件记录
            let file = File::new(
                user_id,
                unique_filename.clone(),
                filename,
                file_type,
                content_type,
                data.len() as i64,
                file_path.clone(),                                  // 文件路径
                format!("/api/files/{}/download", unique_filename), // 下载URL
                None,                                               // 暂不设置元数据
            );

            // 保存到内存存储
            let saved_file = storage.save_file(file).await?;

            tracing::info!("文件元数据已保存到内存存储: {}", saved_file.id);

            uploaded_file = Some(saved_file);
            break;
        }
    }

    let file = uploaded_file.ok_or_else(|| AppError::BadRequest("未找到文件字段".to_string()))?;

    let response = UploadResponse {
        file: file.to_response(),
        message: "文件上传成功".to_string(),
    };

    Ok(Json(ApiResponse::success(response)))
}

/// 批量文件上传处理函数
///
/// 处理多个文件的批量上传请求
/// - 支持一次上传多个文件
/// - 对每个文件进行独立验证
/// - 返回所有文件的上传结果
pub async fn upload_multiple_files(
    State((_settings, storage, _)): State<AppState>,
    mut multipart: Multipart,
) -> AppResult<Json<ApiResponse<BatchUploadResponse>>> {
    // 临时使用固定用户ID，实际应用中应从认证中间件获取
    let user_id = Uuid::new_v4();

    let mut uploaded_files: Vec<UploadResponse> = Vec::new();
    let mut failed_count = 0;

    // 解析multipart数据
    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::BadRequest(format!("多部分数据解析失败: {}", e)))?
    {
        let field_name = field.name().unwrap_or("").to_string();

        if field_name == "files" || field_name == "file" {
            // 处理单个文件
            match process_single_file_upload(field, user_id, &storage).await {
                Ok(upload_response) => {
                    uploaded_files.push(upload_response);
                }
                Err(e) => {
                    failed_count += 1;
                    eprintln!("文件上传失败: {:?}", e);
                    // 继续处理其他文件，不中断整个批量上传
                }
            }
        }
    }

    if uploaded_files.is_empty() && failed_count == 0 {
        return Err(AppError::BadRequest("未找到文件字段".to_string()));
    }

    let total_count = uploaded_files.len() + failed_count;
    let success_count = uploaded_files.len();

    let response = BatchUploadResponse {
        files: uploaded_files,
        total_count,
        success_count,
        failed_count,
    };

    Ok(Json(ApiResponse::success(response)))
}

/// 文件下载处理函数
///
/// 根据文件名下载文件
/// - 从内存存储中查找文件元数据
/// - 从磁盘读取文件内容
/// - 返回文件内容和正确的Content-Type头
pub async fn download_file(
    State((_, storage, _)): State<AppState>,
    AxumPath(filename): AxumPath<String>,
) -> AppResult<Response> {
    // 从内存存储中查找文件
    let files = storage.get_all_files().await?;
    let file = files
        .iter()
        .find(|f| f.filename == filename)
        .ok_or_else(|| AppError::NotFound("文件未找到".to_string()))?;

    // 读取文件内容
    let file_content = tokio::fs::read(&file.file_path)
        .await
        .map_err(|e| AppError::InternalServerError(format!("文件读取失败: {}", e)))?;

    // 构建响应
    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, &file.mime_type)
        .header(
            header::CONTENT_DISPOSITION,
            format!("attachment; filename=\"{}\"", file.original_filename),
        )
        .header(header::CONTENT_LENGTH, file_content.len())
        .body(file_content.into())
        .map_err(|e| AppError::InternalServerError(format!("响应构建失败: {}", e)))?;

    Ok(response)
}

/// 文件内联查看处理函数
///
/// 根据文件名在浏览器中内联显示文件（主要用于图片）
/// - 从内存存储中查找文件元数据
/// - 验证文件类型（仅允许图片类型）
/// - 从磁盘读取文件内容
/// - 返回文件内容和正确的Content-Type头，设置为内联显示
pub async fn view_file(
    State((_, storage, _)): State<AppState>,
    AxumPath(filename): AxumPath<String>,
) -> AppResult<Response> {
    // 从内存存储中查找文件
    let files = storage.get_all_files().await?;
    let file = files
        .iter()
        .find(|f| f.filename == filename)
        .ok_or_else(|| AppError::NotFound("文件未找到".to_string()))?;

    // 验证文件类型 - 仅允许图片类型
    if !file.mime_type.starts_with("image/") {
        return Err(AppError::BadRequest(format!(
            "不支持的文件类型用于内联查看: {}。仅支持图片文件。",
            file.mime_type
        )));
    }

    // 读取文件内容
    let file_content = tokio::fs::read(&file.file_path)
        .await
        .map_err(|e| AppError::InternalServerError(format!("文件读取失败: {}", e)))?;

    tracing::info!("内联查看文件: {} ({})", filename, file.mime_type);

    // 构建响应 - 设置为内联显示
    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, &file.mime_type)
        .header(header::CONTENT_DISPOSITION, "inline") // 关键：设置为内联显示
        .header(header::CONTENT_LENGTH, file_content.len())
        .header(header::CACHE_CONTROL, "public, max-age=3600") // 添加缓存控制
        .body(file_content.into())
        .map_err(|e| AppError::InternalServerError(format!("响应构建失败: {}", e)))?;

    Ok(response)
}

// 辅助函数

/// 根据MIME类型确定文件类型
fn determine_file_type(mime_type: &str) -> FileType {
    match mime_type {
        mime if mime.starts_with("image/") => FileType::Image,
        mime if mime.starts_with("audio/") => FileType::Audio,
        mime if mime.starts_with("video/") => FileType::Video,
        "application/pdf" | "text/plain" | "text/markdown" => FileType::Document,
        _ => FileType::Other,
    }
}

/// 处理单个文件上传的辅助函数
async fn process_single_file_upload(
    field: axum::extract::multipart::Field<'_>,
    user_id: Uuid,
    storage: &Arc<crate::services::memory::MemoryStorageService>,
) -> AppResult<UploadResponse> {
    // 获取文件信息
    let filename = field
        .file_name()
        .ok_or_else(|| AppError::BadRequest("缺少文件名".to_string()))?
        .to_string();

    let content_type = field
        .content_type()
        .ok_or_else(|| AppError::BadRequest("缺少文件类型".to_string()))?
        .to_string();

    // 读取文件数据
    let data = field
        .bytes()
        .await
        .map_err(|e| AppError::BadRequest(format!("文件数据读取失败: {}", e)))?;

    // 验证文件大小
    let max_size = 10 * 1024 * 1024; // 10MB
    if data.len() > max_size {
        return Err(AppError::BadRequest(format!(
            "文件大小超过限制，最大允许 {} MB",
            max_size / (1024 * 1024)
        )));
    }

    // 验证文件类型
    let allowed_types = vec![
        "image/jpeg".to_string(),
        "image/png".to_string(),
        "image/gif".to_string(),
        "image/webp".to_string(),
        "application/pdf".to_string(),
        "text/plain".to_string(),
        "text/markdown".to_string(),
        "audio/mpeg".to_string(),
        "audio/wav".to_string(),
        "video/mp4".to_string(),
    ];

    if !Validator::validate_file_type(&filename, &allowed_types) {
        return Err(AppError::BadRequest(format!(
            "不支持的文件类型: {}",
            content_type
        )));
    }

    // 确定文件类型
    let file_type = determine_file_type(&content_type);

    // 生成唯一文件名
    let unique_filename = format!("{}_{}", Uuid::new_v4(), filename);
    let file_path = format!("./uploads/{}", unique_filename);

    // 确保上传目录存在
    if let Err(e) = tokio::fs::create_dir_all("./uploads").await {
        tracing::warn!("创建上传目录失败: {}", e);
    }

    // 将文件数据写入磁盘
    if let Err(e) = tokio::fs::write(&file_path, &data).await {
        return Err(AppError::InternalServerError(format!(
            "文件保存失败: {}",
            e
        )));
    }

    tracing::info!("批量上传文件已保存到磁盘: {}", file_path);

    // 创建文件记录
    let file = File::new(
        user_id,
        unique_filename.clone(),
        filename.clone(),
        file_type,
        content_type,
        data.len() as i64,
        file_path.clone(),                                  // 文件路径
        format!("/api/files/{}/download", unique_filename), // 下载URL
        None,                                               // 暂不设置元数据
    );

    // 保存到内存存储
    let saved_file = storage.save_file(file).await?;

    Ok(UploadResponse {
        file: saved_file.to_response(),
        message: format!("文件 {} 上传成功", filename),
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试文件路由创建
    #[tokio::test]
    async fn test_file_routes_creation() {
        let routes = create_file_routes();
        // 由于开发阶段路由被注释，这里主要测试结构
        let _router = routes; // 确保路由创建成功
    }

    /// 测试条件性路由创建
    #[tokio::test]
    async fn test_conditional_file_routes() {
        let config = FileRouteConfig {
            enable_file_upload: true,
            ..Default::default()
        };

        let routes = create_conditional_file_routes(config);
        // 测试路由是否根据配置正确创建
        let _router = routes; // 确保路由创建成功
    }

    /// 测试存储策略路由
    #[tokio::test]
    async fn test_storage_strategy_routes() {
        let local_strategy = FileStorageStrategy::Local {
            base_path: "./test_uploads".to_string(),
        };

        let routes = create_file_routes_with_storage(local_strategy);
        let _router = routes; // 确保路由创建成功
    }

    /// 测试文件类型确定函数
    #[test]
    fn test_determine_file_type() {
        assert_eq!(determine_file_type("image/jpeg"), FileType::Image);
        assert_eq!(determine_file_type("image/png"), FileType::Image);
        assert_eq!(determine_file_type("audio/mpeg"), FileType::Audio);
        assert_eq!(determine_file_type("video/mp4"), FileType::Video);
        assert_eq!(determine_file_type("application/pdf"), FileType::Document);
        assert_eq!(determine_file_type("text/plain"), FileType::Document);
        assert_eq!(determine_file_type("application/unknown"), FileType::Other);
    }
}
