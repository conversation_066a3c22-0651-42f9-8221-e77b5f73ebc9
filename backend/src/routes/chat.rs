// 聊天路由模块
// 处理所有聊天相关的路由定义，包括文本消息、图像消息和流式响应

use axum::{routing::post, Router};

use crate::handlers::chat::{
    debug_streaming_chat, send_image_message, send_multimodal_message, send_text_message,
};

use super::AppState;

/// 创建聊天相关路由
///
/// 定义所有聊天功能的API端点：
/// - 文本消息发送（支持流式和非流式）
/// - 图像消息发送
/// - 调试和测试端点
pub fn create_chat_routes() -> Router<AppState> {
    Router::new()
        // 发送文本消息 - 统一端点，支持流式和非流式响应
        // POST /api/chat/text
        // Body: { "content": "消息内容", "conversation_id": "可选", "stream": false }
        .route("/text", post(send_text_message))
        // 发送图像消息 - 多模态聊天功能
        // POST /api/chat/image
        // Body: { "image_url": "图片URL", "prompt": "可选提示", "conversation_id": "可选" }
        .route("/image", post(send_image_message))
        // 统一多模态消息端点 - 支持文本、图片和混合消息
        // POST /api/chat/multimodal
        // Body: { "content": "文本" | [{"type": "text", "text": "..."}, {"type": "image_url", "image_url": {"url": "..."}}], "conversation_id": "可选", "stream": false }
        .route("/multimodal", post(send_multimodal_message))
        // 调试流式聊天 - 开发和测试用途
        // POST /api/chat/debug/stream
        // 用于调试SSE解析和流式响应功能
        .route("/debug/stream", post(debug_streaming_chat))
}

/// 聊天路由配置选项
#[derive(Debug, Clone)]
pub struct ChatRouteConfig {
    /// 是否启用图像聊天功能
    pub enable_image_chat: bool,
    /// 是否启用调试端点
    pub enable_debug_endpoints: bool,
    /// 是否启用语音聊天功能（未来）
    pub enable_voice_chat: bool,
    /// 是否启用文件聊天功能（未来）
    pub enable_file_chat: bool,
}

impl Default for ChatRouteConfig {
    fn default() -> Self {
        Self {
            enable_image_chat: true,
            enable_debug_endpoints: true, // 开发阶段启用
            enable_voice_chat: false,     // 未来功能
            enable_file_chat: false,      // 未来功能
        }
    }
}

/// 根据配置创建条件性聊天路由
///
/// 允许根据配置动态启用或禁用某些聊天功能
pub fn create_conditional_chat_routes(config: ChatRouteConfig) -> Router<AppState> {
    let mut router = Router::new()
        // 文本聊天是核心功能，始终启用
        .route("/text", post(send_text_message));

    // 根据配置条件性添加路由
    if config.enable_image_chat {
        router = router.route("/image", post(send_image_message));
    }

    if config.enable_debug_endpoints {
        router = router.route("/debug/stream", post(debug_streaming_chat));
    }

    // 未来功能的占位符
    if config.enable_voice_chat {
        // router = router.route("/voice", post(send_voice_message));
    }

    if config.enable_file_chat {
        // router = router.route("/file", post(send_file_message));
    }

    router
}

/// 聊天路由的中间件配置
///
/// 为聊天路由添加特定的中间件，如速率限制、认证等
pub fn create_chat_routes_with_middleware() -> Router<AppState> {
    create_chat_routes()
    // 这里可以添加聊天特定的中间件
    // .layer(rate_limiting_middleware())
    // .layer(chat_authentication_middleware())
    // .layer(message_validation_middleware())
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试聊天路由创建
    #[tokio::test]
    async fn test_chat_routes_creation() {
        let routes = create_chat_routes();

        // 验证路由是否正确创建和配置
        // 由于Router没有直接的方法检查路由数量，我们只验证创建成功
        let _router = routes; // 确保路由创建成功
    }

    /// 测试条件性路由创建
    #[tokio::test]
    async fn test_conditional_chat_routes() {
        let config = ChatRouteConfig {
            enable_image_chat: false,
            enable_debug_endpoints: false,
            ..Default::default()
        };

        let routes = create_conditional_chat_routes(config);

        // 验证只有文本聊天路由被创建
        let _router = routes; // 确保路由创建成功
    }
}
