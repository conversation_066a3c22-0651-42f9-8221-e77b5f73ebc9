// 路由模块 - 模块化的路由定义和组织
// 将不同功能域的路由分离到独立模块中，提高代码可维护性

use axum::Router;
use std::sync::Arc;

use crate::{
    services::{memory::MemoryStorageService, qianwen::QianwenService},
    utils::config::Settings,
};

// 导入各个路由模块
pub mod auth;
pub mod chat;
pub mod conversation;
pub mod file;
pub mod health;
pub mod travel;
pub mod user;

/// 应用状态类型别名，简化类型声明
pub type AppState = (
    Arc<Settings>,
    Arc<MemoryStorageService>,
    Arc<QianwenService>,
);

/// 创建完整的应用路由
///
/// 这个函数组合所有功能域的路由，创建完整的应用路由树
/// 使用Axum的nest()方法将不同的路由模块组织在一起
pub fn create_app_routes() -> Router<AppState> {
    Router::new()
        // 健康检查路由 - 根路径和基础健康检查
        .merge(health::create_health_routes())
        // 聊天功能路由 - /api/chat/*
        .nest("/api/chat", chat::create_chat_routes())
        // 对话管理路由 - /api/conversations/*
        .nest(
            "/api/conversations",
            conversation::create_conversation_routes(),
        )
        // 文件管理路由 - /api/files/*（开发阶段可选）
        .nest("/api/files", file::create_file_routes())
        // 用户管理路由 - /api/users/*（未来功能）
        .nest("/api/users", user::create_user_routes())
        // 认证路由 - /api/auth/*（未来功能）
        .nest("/api/auth", auth::create_auth_routes())
        // 旅行规划路由 - /api/travel/*
        .nest("/api/travel", travel::create_travel_routes())
}

/// 创建API路由的便捷函数
///
/// 为API路由添加统一的前缀和中间件
pub fn create_api_routes() -> Router<AppState> {
    Router::new().nest("/api", create_app_routes())
}

/// 路由配置选项
#[derive(Debug, Clone)]
pub struct RouteConfig {
    /// 是否启用文件上传功能
    pub enable_file_upload: bool,
    /// 是否启用用户管理功能
    pub enable_user_management: bool,
    /// 是否启用认证功能
    pub enable_authentication: bool,
    /// API版本前缀
    pub api_version: String,
}

impl Default for RouteConfig {
    fn default() -> Self {
        Self {
            enable_file_upload: false,     // 开发阶段默认禁用
            enable_user_management: false, // 未来功能
            enable_authentication: false,  // 未来功能
            api_version: "v1".to_string(),
        }
    }
}

/// 根据配置创建条件路由
///
/// 允许根据配置动态启用或禁用某些路由模块
pub fn create_conditional_routes(config: RouteConfig) -> Router<AppState> {
    let mut router = Router::new()
        // 核心功能始终启用
        .merge(health::create_health_routes())
        .nest("/api/chat", chat::create_chat_routes())
        .nest(
            "/api/conversations",
            conversation::create_conversation_routes(),
        );

    // 根据配置条件性添加路由
    if config.enable_file_upload {
        router = router.nest("/api/files", file::create_file_routes());
    }

    if config.enable_user_management {
        router = router.nest("/api/users", user::create_user_routes());
    }

    if config.enable_authentication {
        router = router.nest("/api/auth", auth::create_auth_routes());
    }

    router
}
