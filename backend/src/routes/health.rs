// 健康检查路由模块
// 提供应用和各个服务组件的健康状态检查端点

use axum::{extract::State, routing::get, Json, Router};
use serde_json::Value;

use crate::middleware::error::AppResult;

use super::AppState;

/// 创建健康检查相关路由
pub fn create_health_routes() -> Router<AppState> {
    Router::new()
        // 根路径健康检查
        .route("/", get(basic_health_check))
        // API健康检查
        .route("/api/health", get(basic_health_check))
        // 存储服务健康检查
        .route("/api/health/storage", get(storage_health_check))
        // AI服务健康检查
        .route("/api/health/ai", get(ai_service_health_check))
        // 综合健康检查
        .route("/api/health/full", get(full_health_check))
}

/// 基础健康检查端点
///
/// 返回简单的状态信息，确认服务正在运行
async fn basic_health_check() -> &'static str {
    "AI Chat Backend is running! 🤖"
}

/// 存储服务健康检查
///
/// 检查内存存储服务的状态和统计信息
async fn storage_health_check(State((_, storage, _)): State<AppState>) -> AppResult<Json<Value>> {
    match storage.health_check().await {
        Ok(_) => {
            let stats = storage.get_stats().await.unwrap_or_else(|_| {
                crate::services::memory::MemoryStorageStats {
                    user_count: 0,
                    conversation_count: 0,
                    message_count: 0,
                    file_count: 0,
                    travel_plan_count: 0,
                }
            });

            Ok(Json(serde_json::json!({
                "status": "healthy",
                "storage_type": "memory",
                "mode": "development",
                "stats": {
                    "users": stats.user_count,
                    "conversations": stats.conversation_count,
                    "messages": stats.message_count,
                    "files": stats.file_count
                },
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            tracing::error!("存储健康检查失败: {}", e);
            Ok(Json(serde_json::json!({
                "status": "unhealthy",
                "storage_type": "memory",
                "error": e.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
    }
}

/// AI服务健康检查
///
/// 检查通义千问API服务的连接状态
async fn ai_service_health_check(
    State((_, _, qianwen)): State<AppState>,
) -> AppResult<Json<Value>> {
    // 这里可以添加对AI服务的实际健康检查
    // 目前返回基础状态信息
    Ok(Json(serde_json::json!({
        "status": "healthy",
        "service": "qianwen",
        "provider": "阿里云通义千问",
        "features": [
            "text_generation",
            "image_understanding",
            "streaming_response"
        ],
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// 综合健康检查
///
/// 检查所有服务组件的状态，提供完整的健康报告
async fn full_health_check(
    State((settings, storage, qianwen)): State<AppState>,
) -> AppResult<Json<Value>> {
    let mut health_report = serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "version": env!("CARGO_PKG_VERSION"),
        "services": {}
    });

    // 检查存储服务
    let storage_status = match storage.health_check().await {
        Ok(_) => {
            let stats = storage.get_stats().await.ok();
            serde_json::json!({
                "status": "healthy",
                "type": "memory",
                "stats": stats
            })
        }
        Err(e) => serde_json::json!({
            "status": "unhealthy",
            "type": "memory",
            "error": e.to_string()
        }),
    };

    // 检查AI服务（基础检查）
    let ai_status = serde_json::json!({
        "status": "healthy",
        "provider": "qianwen",
        "features": ["text", "image", "streaming"]
    });

    // 检查配置状态
    let config_status = serde_json::json!({
        "status": "healthy",
        "server_host": settings.server.host,
        "server_port": settings.server.port,
        "environment": "development"
    });

    health_report["services"]["storage"] = storage_status;
    health_report["services"]["ai"] = ai_status;
    health_report["services"]["config"] = config_status;

    // 根据各个服务状态确定整体状态
    let overall_healthy = health_report["services"]
        .as_object()
        .unwrap()
        .values()
        .all(|service| service["status"] == "healthy");

    health_report["status"] = if overall_healthy {
        serde_json::Value::String("healthy".to_string())
    } else {
        serde_json::Value::String("degraded".to_string())
    };

    Ok(Json(health_report))
}
