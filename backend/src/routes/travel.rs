// 旅行规划路由 - 智能旅行规划表生成和管理
// 集成AI聊天后端和高德地图MCP服务，提供旅行规划数据API

use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::{
    middleware::error::{AppError, AppResult},
    routes::AppState,
    services::qianwen::ChatMessage,
};

/// 旅行规划数据结构 - 兼容前端和MCP服务格式
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TravelPlanData {
    pub destination: String,
    pub dates: TravelDates,
    pub weather: WeatherInfo,
    pub itinerary: Vec<ItineraryItem>,
    pub transportation: Vec<TransportationItem>,
    pub accommodation: AccommodationInfo,
    pub budget: BudgetInfo,
    pub generated_at: String,
    pub conversation_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TravelDates {
    pub start: String,
    pub end: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WeatherInfo {
    pub temperature: String,
    pub condition: String,
    pub wind: String,
    pub icon: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ItineraryItem {
    pub time: String,
    pub activity: String,
    pub location: String,
    #[serde(rename = "type")]
    pub item_type: String,
    pub price: String,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TransportationItem {
    pub from: String,
    pub to: String,
    pub method: String,
    pub duration: String,
    pub cost: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AccommodationInfo {
    pub name: String,
    pub address: String,
    pub checkin: String,
    pub checkout: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BudgetInfo {
    pub total: i32,
    pub breakdown: HashMap<String, i32>,
}

/// 生成旅行规划请求
#[derive(Debug, Deserialize)]
pub struct GenerateTravelPlanRequest {
    pub destination: String,
    pub start_date: String,
    pub end_date: String,
    pub budget: Option<i32>,
    pub preferences: Option<Vec<String>>,
    pub conversation_id: Option<String>,
}

/// 旅行规划查询参数
#[derive(Debug, Deserialize)]
pub struct TravelPlanQuery {
    pub format: Option<String>, // html, json, pdf
}

/// 创建旅行规划路由
pub fn create_travel_routes() -> Router<AppState> {
    Router::new()
        .route("/generate", post(generate_travel_plan))
        .route("/plan/{conversation_id}", get(get_travel_plan))
        .route("/template", get(get_travel_template))
        .route("/plans", get(list_travel_plans))
}

/// 生成智能旅行规划
///
/// 基于用户输入和AI聊天历史，生成个性化旅行规划
async fn generate_travel_plan(
    State((settings, memory_service, qianwen_service)): State<AppState>,
    Json(request): Json<GenerateTravelPlanRequest>,
) -> AppResult<Json<TravelPlanData>> {
    tracing::info!(
        "生成旅行规划: {} ({} - {})",
        request.destination,
        request.start_date,
        request.end_date
    );

    // 构建AI提示词，生成旅行规划
    let prompt = format!(
        "请为{}制定详细的旅行规划，时间从{}到{}。{}{}请以JSON格式返回，包含景点、交通、住宿、餐饮和预算信息。",
        request.destination,
        request.start_date,
        request.end_date,
        request.budget.map(|b| format!("预算约{}元。", b)).unwrap_or_default(),
        request.preferences.as_ref()
            .map(|prefs| format!("偏好：{}。", prefs.join("、")))
            .unwrap_or_default()
    );

    let messages = vec![ChatMessage::text("user", &prompt)];

    // 调用AI服务生成规划（会自动触发MCP服务获取地图数据）
    let ai_response = qianwen_service.chat_multimodal(messages).await?;

    // 解析AI响应并构建旅行规划数据
    let travel_plan = parse_ai_response_to_travel_plan(
        &ai_response,
        &request.destination,
        &request.start_date,
        &request.end_date,
        request.conversation_id,
    )?;

    // 保存到内存存储
    if let Some(conv_id) = &travel_plan.conversation_id {
        memory_service
            .store_travel_plan(conv_id, &travel_plan)
            .await?;
    }

    tracing::info!(
        "旅行规划生成成功，包含{}个行程项目",
        travel_plan.itinerary.len()
    );

    Ok(Json(travel_plan))
}

/// 获取指定对话的旅行规划
async fn get_travel_plan(
    State((_, memory_service, _)): State<AppState>,
    Path(conversation_id): Path<String>,
    Query(_query): Query<TravelPlanQuery>,
) -> AppResult<Json<TravelPlanData>> {
    tracing::info!("获取旅行规划: {}", conversation_id);

    let travel_plan = memory_service
        .get_travel_plan(&conversation_id)
        .await?
        .ok_or_else(|| AppError::NotFound(format!("旅行规划未找到: {}", conversation_id)))?;

    Ok(Json(travel_plan))
}

/// 获取旅行规划HTML模板
async fn get_travel_template(Query(_query): Query<TravelPlanQuery>) -> AppResult<String> {
    tracing::info!("获取旅行规划模板");

    // 读取HTML模板文件
    let template_content = include_str!("../travel_planner_template.html");

    Ok(template_content.to_string())
}

/// 列出所有旅行规划
async fn list_travel_plans(
    State((_, memory_service, _)): State<AppState>,
) -> AppResult<Json<Vec<TravelPlanData>>> {
    tracing::info!("列出所有旅行规划");

    let plans = memory_service.list_travel_plans().await?;

    Ok(Json(plans))
}

/// 解析AI响应为旅行规划数据
fn parse_ai_response_to_travel_plan(
    ai_response: &str,
    destination: &str,
    start_date: &str,
    end_date: &str,
    conversation_id: Option<String>,
) -> AppResult<TravelPlanData> {
    // 尝试从AI响应中提取JSON数据
    if let Some(json_start) = ai_response.find('{') {
        if let Some(json_end) = ai_response.rfind('}') {
            let json_str = &ai_response[json_start..=json_end];
            if let Ok(parsed_plan) = serde_json::from_str::<TravelPlanData>(json_str) {
                return Ok(parsed_plan);
            }
        }
    }

    // 如果无法解析JSON，则生成默认规划数据
    tracing::warn!("无法解析AI响应为JSON，使用默认模板");

    Ok(create_default_travel_plan(
        destination,
        start_date,
        end_date,
        conversation_id,
    ))
}

/// 创建默认旅行规划数据
fn create_default_travel_plan(
    destination: &str,
    start_date: &str,
    end_date: &str,
    conversation_id: Option<String>,
) -> TravelPlanData {
    let mut breakdown = HashMap::new();
    breakdown.insert("transport".to_string(), 100);
    breakdown.insert("food".to_string(), 300);
    breakdown.insert("attractions".to_string(), 200);
    breakdown.insert("shopping".to_string(), 100);

    TravelPlanData {
        destination: format!("{}旅行规划", destination),
        dates: TravelDates {
            start: start_date.to_string(),
            end: end_date.to_string(),
        },
        weather: WeatherInfo {
            temperature: "20°C/15°C".to_string(),
            condition: "晴".to_string(),
            wind: "微风".to_string(),
            icon: "fa-sun".to_string(),
        },
        itinerary: vec![
            ItineraryItem {
                time: "09:00-11:00".to_string(),
                activity: format!("游览{}著名景点", destination),
                location: "市中心".to_string(),
                item_type: "attraction".to_string(),
                price: "50元".to_string(),
                notes: Some("建议提前预订".to_string()),
            },
            ItineraryItem {
                time: "12:00-13:00".to_string(),
                activity: "品尝当地美食".to_string(),
                location: "美食街".to_string(),
                item_type: "dining".to_string(),
                price: "80元".to_string(),
                notes: None,
            },
            ItineraryItem {
                time: "14:00-17:00".to_string(),
                activity: "文化体验活动".to_string(),
                location: "文化区".to_string(),
                item_type: "activity".to_string(),
                price: "120元".to_string(),
                notes: Some("适合拍照".to_string()),
            },
        ],
        transportation: vec![TransportationItem {
            from: "酒店".to_string(),
            to: "景点".to_string(),
            method: "地铁".to_string(),
            duration: "30分钟".to_string(),
            cost: "10元".to_string(),
        }],
        accommodation: AccommodationInfo {
            name: format!("{}精品酒店", destination),
            address: "市中心商业区".to_string(),
            checkin: "15:00".to_string(),
            checkout: "12:00".to_string(),
        },
        budget: BudgetInfo {
            total: 700,
            breakdown,
        },
        generated_at: chrono::Utc::now()
            .format("%Y-%m-%d %H:%M:%S UTC")
            .to_string(),
        conversation_id,
    }
}
