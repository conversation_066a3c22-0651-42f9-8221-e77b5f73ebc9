// 内存存储服务 - 用于开发阶段的临时数据存储
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use uuid::Uuid;

use crate::{
    middleware::error::{AppError, AppResult},
    models::{conversation::Conversation, file::File, message::Message, user::User},
    routes::travel::TravelPlanData,
};

/// 内存存储服务结构
#[derive(Debug, Clone)]
pub struct MemoryStorageService {
    /// 用户数据存储
    users: Arc<RwLock<HashMap<Uuid, User>>>,
    /// 对话数据存储
    conversations: Arc<RwLock<HashMap<Uuid, Conversation>>>,
    /// 消息数据存储
    messages: Arc<RwLock<HashMap<Uuid, Message>>>,
    /// 文件数据存储
    files: Arc<RwLock<HashMap<Uuid, File>>>,
    /// 按对话ID索引的消息列表
    messages_by_conversation: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    /// 按用户ID索引的对话列表
    conversations_by_user: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    /// 按用户ID索引的文件列表
    files_by_user: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    /// 旅行规划数据存储（按对话ID索引）
    travel_plans: Arc<RwLock<HashMap<String, TravelPlanData>>>,
}

impl MemoryStorageService {
    /// 创建新的内存存储服务实例
    pub fn new() -> Self {
        Self {
            users: Arc::new(RwLock::new(HashMap::new())),
            conversations: Arc::new(RwLock::new(HashMap::new())),
            messages: Arc::new(RwLock::new(HashMap::new())),
            files: Arc::new(RwLock::new(HashMap::new())),
            messages_by_conversation: Arc::new(RwLock::new(HashMap::new())),
            conversations_by_user: Arc::new(RwLock::new(HashMap::new())),
            files_by_user: Arc::new(RwLock::new(HashMap::new())),
            travel_plans: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 健康检查
    pub async fn health_check(&self) -> AppResult<()> {
        // 内存存储总是健康的
        Ok(())
    }

    // 用户相关操作

    /// 保存用户
    pub async fn save_user(&self, user: User) -> AppResult<User> {
        let mut users = self
            .users
            .write()
            .map_err(|_| AppError::InternalServerError("获取用户存储锁失败".to_string()))?;

        users.insert(user.id, user.clone());
        Ok(user)
    }

    /// 根据ID获取用户
    pub async fn get_user_by_id(&self, user_id: Uuid) -> AppResult<Option<User>> {
        let users = self
            .users
            .read()
            .map_err(|_| AppError::InternalServerError("获取用户存储锁失败".to_string()))?;

        Ok(users.get(&user_id).cloned())
    }

    /// 检查用户是否存在
    pub async fn user_exists(&self, user_id: Uuid) -> AppResult<bool> {
        let users = self
            .users
            .read()
            .map_err(|_| AppError::InternalServerError("获取用户存储锁失败".to_string()))?;

        Ok(users.contains_key(&user_id))
    }

    // 对话相关操作

    /// 保存对话
    pub async fn save_conversation(&self, conversation: Conversation) -> AppResult<Conversation> {
        let mut conversations = self
            .conversations
            .write()
            .map_err(|_| AppError::InternalServerError("获取对话存储锁失败".to_string()))?;

        let mut conversations_by_user = self
            .conversations_by_user
            .write()
            .map_err(|_| AppError::InternalServerError("获取用户对话索引锁失败".to_string()))?;

        // 保存对话
        conversations.insert(conversation.id, conversation.clone());

        // 更新用户对话索引
        conversations_by_user
            .entry(conversation.user_id)
            .or_insert_with(Vec::new)
            .push(conversation.id);

        Ok(conversation)
    }

    /// 根据ID获取对话
    pub async fn get_conversation_by_id(
        &self,
        conversation_id: Uuid,
    ) -> AppResult<Option<Conversation>> {
        let conversations = self
            .conversations
            .read()
            .map_err(|_| AppError::InternalServerError("获取对话存储锁失败".to_string()))?;

        Ok(conversations.get(&conversation_id).cloned())
    }

    /// 获取用户的对话列表
    pub async fn get_user_conversations(
        &self,
        user_id: Uuid,
        page: u32,
        per_page: u32,
        archived: Option<bool>,
    ) -> AppResult<Vec<Conversation>> {
        let conversations = self
            .conversations
            .read()
            .map_err(|_| AppError::InternalServerError("获取对话存储锁失败".to_string()))?;

        let conversations_by_user = self
            .conversations_by_user
            .read()
            .map_err(|_| AppError::InternalServerError("获取用户对话索引锁失败".to_string()))?;

        let conversation_ids = conversations_by_user
            .get(&user_id)
            .cloned()
            .unwrap_or_default();

        let mut user_conversations: Vec<Conversation> = conversation_ids
            .iter()
            .filter_map(|id| conversations.get(id).cloned())
            .filter(|conv| {
                if let Some(archived_filter) = archived {
                    conv.is_archived == archived_filter
                } else {
                    true
                }
            })
            .collect();

        // 按更新时间降序排序
        user_conversations.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));

        // 分页
        let offset = ((page - 1) * per_page) as usize;
        let limit = per_page as usize;

        let result = user_conversations
            .into_iter()
            .skip(offset)
            .take(limit)
            .collect();

        Ok(result)
    }

    /// 更新对话
    pub async fn update_conversation(&self, conversation: Conversation) -> AppResult<Conversation> {
        let mut conversations = self
            .conversations
            .write()
            .map_err(|_| AppError::InternalServerError("获取对话存储锁失败".to_string()))?;

        conversations.insert(conversation.id, conversation.clone());
        Ok(conversation)
    }

    // 消息相关操作

    /// 保存消息
    pub async fn save_message(&self, message: Message) -> AppResult<Message> {
        let mut messages = self
            .messages
            .write()
            .map_err(|_| AppError::InternalServerError("获取消息存储锁失败".to_string()))?;

        let mut messages_by_conversation = self
            .messages_by_conversation
            .write()
            .map_err(|_| AppError::InternalServerError("获取对话消息索引锁失败".to_string()))?;

        // 保存消息
        messages.insert(message.id, message.clone());

        // 更新对话消息索引
        messages_by_conversation
            .entry(message.conversation_id)
            .or_insert_with(Vec::new)
            .push(message.id);

        Ok(message)
    }

    /// 获取对话的消息列表
    pub async fn get_conversation_messages(
        &self,
        conversation_id: Uuid,
        page: u32,
        per_page: u32,
        message_type: Option<String>,
    ) -> AppResult<Vec<Message>> {
        let messages = self
            .messages
            .read()
            .map_err(|_| AppError::InternalServerError("获取消息存储锁失败".to_string()))?;

        let messages_by_conversation = self
            .messages_by_conversation
            .read()
            .map_err(|_| AppError::InternalServerError("获取对话消息索引锁失败".to_string()))?;

        let message_ids = messages_by_conversation
            .get(&conversation_id)
            .cloned()
            .unwrap_or_default();

        let mut conversation_messages: Vec<Message> = message_ids
            .iter()
            .filter_map(|id| messages.get(id).cloned())
            .filter(|msg| !msg.is_deleted)
            .filter(|msg| {
                if let Some(ref msg_type) = message_type {
                    format!("{:?}", msg.message_type).to_lowercase() == msg_type.to_lowercase()
                } else {
                    true
                }
            })
            .collect();

        // 按创建时间升序排序
        conversation_messages.sort_by(|a, b| a.created_at.cmp(&b.created_at));

        // 分页
        let offset = ((page - 1) * per_page) as usize;
        let limit = per_page as usize;

        let result = conversation_messages
            .into_iter()
            .skip(offset)
            .take(limit)
            .collect();

        Ok(result)
    }

    /// 获取对话历史（用于AI API调用）
    pub async fn get_conversation_history(&self, conversation_id: Uuid) -> AppResult<Vec<Message>> {
        let messages = self
            .messages
            .read()
            .map_err(|_| AppError::InternalServerError("获取消息存储锁失败".to_string()))?;

        let messages_by_conversation = self
            .messages_by_conversation
            .read()
            .map_err(|_| AppError::InternalServerError("获取对话消息索引锁失败".to_string()))?;

        let message_ids = messages_by_conversation
            .get(&conversation_id)
            .cloned()
            .unwrap_or_default();

        let mut conversation_messages: Vec<Message> = message_ids
            .iter()
            .filter_map(|id| messages.get(id).cloned())
            .filter(|msg| !msg.is_deleted)
            .collect();

        // 按创建时间升序排序，限制最近20条消息
        conversation_messages.sort_by(|a, b| a.created_at.cmp(&b.created_at));
        conversation_messages.truncate(20);

        Ok(conversation_messages)
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> AppResult<MemoryStorageStats> {
        let users = self
            .users
            .read()
            .map_err(|_| AppError::InternalServerError("获取用户存储锁失败".to_string()))?;

        let conversations = self
            .conversations
            .read()
            .map_err(|_| AppError::InternalServerError("获取对话存储锁失败".to_string()))?;

        let messages = self
            .messages
            .read()
            .map_err(|_| AppError::InternalServerError("获取消息存储锁失败".to_string()))?;

        let files = self
            .files
            .read()
            .map_err(|_| AppError::InternalServerError("获取文件存储锁失败".to_string()))?;

        let travel_plans = self
            .travel_plans
            .read()
            .map_err(|_| AppError::InternalServerError("无法获取旅行规划存储读锁".to_string()))?;

        Ok(MemoryStorageStats {
            user_count: users.len() as i64,
            conversation_count: conversations.len() as i64,
            message_count: messages.len() as i64,
            file_count: files.len() as i64,
            travel_plan_count: travel_plans.len() as i64,
        })
    }

    // 文件相关操作

    /// 保存文件
    pub async fn save_file(&self, file: File) -> AppResult<File> {
        let mut files = self
            .files
            .write()
            .map_err(|_| AppError::InternalServerError("获取文件存储锁失败".to_string()))?;

        let mut files_by_user = self
            .files_by_user
            .write()
            .map_err(|_| AppError::InternalServerError("获取用户文件索引锁失败".to_string()))?;

        // 保存文件
        files.insert(file.id, file.clone());

        // 更新用户文件索引
        files_by_user
            .entry(file.user_id)
            .or_insert_with(Vec::new)
            .push(file.id);

        Ok(file)
    }

    /// 根据ID获取文件
    pub async fn get_file_by_id(&self, file_id: Uuid) -> AppResult<Option<File>> {
        let files = self
            .files
            .read()
            .map_err(|_| AppError::InternalServerError("获取文件存储锁失败".to_string()))?;

        Ok(files.get(&file_id).cloned())
    }

    /// 获取用户的文件列表
    pub async fn get_files_by_user(&self, user_id: Uuid) -> AppResult<Vec<File>> {
        let files = self
            .files
            .read()
            .map_err(|_| AppError::InternalServerError("获取文件存储锁失败".to_string()))?;

        let files_by_user = self
            .files_by_user
            .read()
            .map_err(|_| AppError::InternalServerError("获取用户文件索引锁失败".to_string()))?;

        let file_ids = files_by_user.get(&user_id).cloned().unwrap_or_default();

        let mut user_files = Vec::new();
        for file_id in file_ids {
            if let Some(file) = files.get(&file_id) {
                user_files.push(file.clone());
            }
        }

        // 按创建时间降序排序
        user_files.sort_by(|a, b| b.created_at.cmp(&a.created_at));

        Ok(user_files)
    }

    /// 获取所有文件列表
    pub async fn get_all_files(&self) -> AppResult<Vec<File>> {
        let files = self
            .files
            .read()
            .map_err(|_| AppError::InternalServerError("获取文件存储锁失败".to_string()))?;

        let mut all_files: Vec<File> = files.values().cloned().collect();

        // 按创建时间降序排序
        all_files.sort_by(|a, b| b.created_at.cmp(&a.created_at));

        Ok(all_files)
    }

    /// 删除文件
    pub async fn delete_file(&self, file_id: Uuid) -> AppResult<bool> {
        let mut files = self
            .files
            .write()
            .map_err(|_| AppError::InternalServerError("获取文件存储锁失败".to_string()))?;

        let mut files_by_user = self
            .files_by_user
            .write()
            .map_err(|_| AppError::InternalServerError("获取用户文件索引锁失败".to_string()))?;

        if let Some(file) = files.remove(&file_id) {
            // 从用户文件索引中移除
            if let Some(user_files) = files_by_user.get_mut(&file.user_id) {
                user_files.retain(|&id| id != file_id);
            }
            Ok(true)
        } else {
            Ok(false)
        }
    }
    // 旅行规划相关操作

    /// 存储旅行规划
    pub async fn store_travel_plan(
        &self,
        conversation_id: &str,
        plan: &TravelPlanData,
    ) -> AppResult<()> {
        let mut plans = self
            .travel_plans
            .write()
            .map_err(|_| AppError::InternalServerError("无法获取旅行规划存储写锁".to_string()))?;

        plans.insert(conversation_id.to_string(), plan.clone());

        tracing::info!("已存储旅行规划: {}", conversation_id);
        Ok(())
    }

    /// 获取旅行规划
    pub async fn get_travel_plan(
        &self,
        conversation_id: &str,
    ) -> AppResult<Option<TravelPlanData>> {
        let plans = self
            .travel_plans
            .read()
            .map_err(|_| AppError::InternalServerError("无法获取旅行规划存储读锁".to_string()))?;

        Ok(plans.get(conversation_id).cloned())
    }

    /// 列出所有旅行规划
    pub async fn list_travel_plans(&self) -> AppResult<Vec<TravelPlanData>> {
        let plans = self
            .travel_plans
            .read()
            .map_err(|_| AppError::InternalServerError("无法获取旅行规划存储读锁".to_string()))?;

        Ok(plans.values().cloned().collect())
    }

    /// 删除旅行规划
    pub async fn delete_travel_plan(&self, conversation_id: &str) -> AppResult<bool> {
        let mut plans = self
            .travel_plans
            .write()
            .map_err(|_| AppError::InternalServerError("无法获取旅行规划存储写锁".to_string()))?;

        let removed = plans.remove(conversation_id).is_some();

        if removed {
            tracing::info!("已删除旅行规划: {}", conversation_id);
        }

        Ok(removed)
    }

    /// 更新旅行规划
    pub async fn update_travel_plan(
        &self,
        conversation_id: &str,
        plan: &TravelPlanData,
    ) -> AppResult<()> {
        let mut plans = self
            .travel_plans
            .write()
            .map_err(|_| AppError::InternalServerError("无法获取旅行规划存储写锁".to_string()))?;

        if plans.contains_key(conversation_id) {
            plans.insert(conversation_id.to_string(), plan.clone());
            tracing::info!("已更新旅行规划: {}", conversation_id);
            Ok(())
        } else {
            Err(AppError::NotFound(format!(
                "旅行规划未找到: {}",
                conversation_id
            )))
        }
    }
}

impl Default for MemoryStorageService {
    fn default() -> Self {
        Self::new()
    }
}

/// 内存存储统计信息
#[derive(Debug, serde::Serialize)]
pub struct MemoryStorageStats {
    pub user_count: i64,
    pub conversation_count: i64,
    pub message_count: i64,
    pub file_count: i64,
    pub travel_plan_count: i64,
}
