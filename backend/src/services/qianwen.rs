// 通义千问API服务 - 集成阿里云通义千问大模型
use futures_util::StreamExt as FuturesStreamExt;
use regex::Regex;
use reqwest::{
    header::{HeaderMap, HeaderValue, AUTHORIZATION, CONTENT_TYPE},
    Client,
};
use serde::{Deserialize, Serialize};
use std::{pin::Pin, time::Duration};
use tokio_stream::Stream;

use crate::{
    middleware::error::{AppError, AppResult},
    utils::config::{AmapConfig, QianwenConfig},
};

/// 通义千问API服务
#[derive(Debug, Clone)]
pub struct QianwenService {
    client: Client,
    config: QianwenConfig,
    amap_config: Option<AmapConfig>,
    mcp_client: Option<McpClient>,
}

/// MCP客户端 - 遵循MCP协议标准
#[derive(Debug, Clone)]
pub struct McpClient {
    client: Client,
    server_url: String,
    api_key: String,
    max_connections: u32,
    timeout_seconds: u64,
    retry_attempts: u32,
    enable_caching: bool,
    cache_ttl_seconds: u64,
    /// MCP协议版本
    protocol_version: String,
    /// 客户端能力
    capabilities: McpClientCapabilities,
    /// 连接状态
    connection_state: McpConnectionState,
}

/// MCP连接状态
#[derive(Debug, Clone, PartialEq)]
pub enum McpConnectionState {
    Disconnected,
    Initializing,
    Connected,
    Error(String),
}

/// MCP客户端能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpClientCapabilities {
    /// 支持的根目录
    pub roots: Option<McpRootsCapability>,
    /// 支持的采样
    pub sampling: Option<McpSamplingCapability>,
}

/// 根目录能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpRootsCapability {
    /// 是否支持列出根目录
    pub list_changed: bool,
}

/// 采样能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpSamplingCapability {}

/// JSON-RPC 2.0 请求结构 - 符合MCP标准
#[derive(Debug, Serialize)]
pub struct JsonRpcRequest {
    pub jsonrpc: String,
    pub method: String,
    pub params: Option<serde_json::Value>,
    pub id: serde_json::Value,
}

/// JSON-RPC 2.0 响应结构 - 符合MCP标准
#[derive(Debug, Deserialize)]
pub struct JsonRpcResponse {
    pub jsonrpc: String,
    pub id: serde_json::Value,
    pub result: Option<serde_json::Value>,
    pub error: Option<JsonRpcError>,
}

/// JSON-RPC 2.0 错误结构 - 符合MCP标准
#[derive(Debug, Deserialize)]
pub struct JsonRpcError {
    pub code: i32,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

/// MCP初始化请求参数
#[derive(Debug, Serialize)]
pub struct McpInitializeParams {
    /// 协议版本
    pub protocol_version: String,
    /// 客户端信息
    pub client_info: McpClientInfo,
    /// 客户端能力
    pub capabilities: McpClientCapabilities,
}

/// MCP客户端信息
#[derive(Debug, Serialize)]
pub struct McpClientInfo {
    pub name: String,
    pub version: String,
}

/// MCP初始化响应结果
#[derive(Debug, Deserialize)]
pub struct McpInitializeResult {
    /// 协议版本
    pub protocol_version: String,
    /// 服务器信息
    pub server_info: McpServerInfo,
    /// 服务器能力
    pub capabilities: McpServerCapabilities,
}

/// MCP服务器信息
#[derive(Debug, Deserialize)]
pub struct McpServerInfo {
    pub name: String,
    pub version: String,
}

/// MCP服务器能力
#[derive(Debug, Deserialize)]
pub struct McpServerCapabilities {
    /// 资源能力
    pub resources: Option<McpResourcesCapability>,
    /// 工具能力
    pub tools: Option<McpToolsCapability>,
    /// 提示能力
    pub prompts: Option<McpPromptsCapability>,
}

/// 资源能力
#[derive(Debug, Deserialize)]
pub struct McpResourcesCapability {
    /// 是否支持订阅
    pub subscribe: Option<bool>,
    /// 是否支持列表变更通知
    pub list_changed: Option<bool>,
}

/// 工具能力
#[derive(Debug, Deserialize)]
pub struct McpToolsCapability {
    /// 是否支持列表变更通知
    pub list_changed: Option<bool>,
}

/// 提示能力
#[derive(Debug, Deserialize)]
pub struct McpPromptsCapability {
    /// 是否支持列表变更通知
    pub list_changed: Option<bool>,
}

/// MCP资源请求参数
#[derive(Debug, Serialize)]
pub struct McpListResourcesParams {
    /// 可选的游标，用于分页
    pub cursor: Option<String>,
}

/// MCP资源列表响应
#[derive(Debug, Deserialize)]
pub struct McpListResourcesResult {
    /// 资源列表
    pub resources: Vec<McpResource>,
    /// 下一页游标
    pub next_cursor: Option<String>,
}

/// MCP资源定义
#[derive(Debug, Deserialize, Clone)]
pub struct McpResource {
    /// 资源URI
    pub uri: String,
    /// 资源名称
    pub name: String,
    /// 资源描述
    pub description: Option<String>,
    /// MIME类型
    pub mime_type: Option<String>,
}

/// MCP读取资源请求参数
#[derive(Debug, Serialize)]
pub struct McpReadResourceParams {
    /// 资源URI
    pub uri: String,
}

/// MCP读取资源响应
#[derive(Debug, Deserialize)]
pub struct McpReadResourceResult {
    /// 资源内容
    pub contents: Vec<McpResourceContent>,
}

/// MCP资源内容
#[derive(Debug, Deserialize, Clone)]
pub struct McpResourceContent {
    /// 资源URI
    pub uri: String,
    /// MIME类型
    pub mime_type: String,
    /// 文本内容（如果是文本类型）
    pub text: Option<String>,
    /// 二进制内容（如果是二进制类型）
    pub blob: Option<String>,
}

/// 高德地图特定的资源结果
#[derive(Debug, Deserialize)]
pub struct AmapMapResult {
    pub pois: Vec<McpPoi>,
    pub routes: Vec<McpRoute>,
    pub weather: Option<McpWeather>,
    pub traffic: Option<McpTraffic>,
}

/// MCP POI信息
#[derive(Debug, Deserialize, Clone)]
pub struct McpPoi {
    pub id: String,
    pub name: String,
    pub address: String,
    pub location: String,
    pub poi_type: String,
    pub rating: Option<f32>,
    pub distance: Option<u32>,
    pub business_hours: Option<String>,
}

/// MCP路线信息
#[derive(Debug, Deserialize, Clone)]
pub struct McpRoute {
    pub origin: String,
    pub destination: String,
    pub distance: u32,
    pub duration: u32,
    pub traffic_condition: String,
    pub steps: Vec<String>,
}

/// MCP天气信息
#[derive(Debug, Deserialize, Clone)]
pub struct McpWeather {
    pub city: String,
    pub temperature: i32,
    pub weather: String,
    pub humidity: u32,
    pub wind: String,
}

/// MCP交通信息
#[derive(Debug, Deserialize, Clone)]
pub struct McpTraffic {
    pub road_condition: String,
    pub congestion_level: u32,
    pub estimated_delay: u32,
}

/// MCP标准错误代码 - 符合JSON-RPC 2.0标准
pub mod mcp_error_codes {
    /// 解析错误
    pub const PARSE_ERROR: i32 = -32700;
    /// 无效请求
    pub const INVALID_REQUEST: i32 = -32600;
    /// 方法未找到
    pub const METHOD_NOT_FOUND: i32 = -32601;
    /// 无效参数
    pub const INVALID_PARAMS: i32 = -32602;
    /// 内部错误
    pub const INTERNAL_ERROR: i32 = -32603;
    /// 服务器错误范围开始
    pub const SERVER_ERROR_START: i32 = -32099;
    /// 服务器错误范围结束
    pub const SERVER_ERROR_END: i32 = -32000;

    /// MCP特定错误代码
    /// 资源未找到
    pub const RESOURCE_NOT_FOUND: i32 = -32001;
    /// 工具执行失败
    pub const TOOL_EXECUTION_ERROR: i32 = -32002;
    /// 连接超时
    pub const CONNECTION_TIMEOUT: i32 = -32003;
    /// 认证失败
    pub const AUTHENTICATION_FAILED: i32 = -32004;
}

impl McpClient {
    /// 创建新的MCP客户端 - 遵循MCP协议标准
    pub fn new(amap_config: &AmapConfig) -> AppResult<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(amap_config.mcp_timeout_seconds))
            .build()
            .map_err(|e| AppError::QianwenApiError(format!("MCP客户端创建失败: {}", e)))?;

        let capabilities = McpClientCapabilities {
            roots: Some(McpRootsCapability { list_changed: true }),
            sampling: Some(McpSamplingCapability {}),
        };

        Ok(Self {
            client,
            server_url: amap_config.mcp_server_url.clone(),
            api_key: if amap_config.mcp_api_key.is_empty() {
                amap_config.api_key.clone()
            } else {
                amap_config.mcp_api_key.clone()
            },
            max_connections: amap_config.mcp_max_connections,
            timeout_seconds: amap_config.mcp_timeout_seconds,
            retry_attempts: amap_config.mcp_retry_attempts,
            enable_caching: amap_config.mcp_enable_caching,
            cache_ttl_seconds: amap_config.mcp_cache_ttl_seconds,
            protocol_version: "2025-06-18".to_string(),
            capabilities,
            connection_state: McpConnectionState::Disconnected,
        })
    }

    /// 初始化MCP连接 - 遵循MCP协议握手流程
    pub async fn initialize(&mut self) -> AppResult<McpInitializeResult> {
        self.connection_state = McpConnectionState::Initializing;

        let init_params = McpInitializeParams {
            protocol_version: self.protocol_version.clone(),
            client_info: McpClientInfo {
                name: "ai-chat-backend".to_string(),
                version: "1.0.0".to_string(),
            },
            capabilities: self.capabilities.clone(),
        };

        let request = JsonRpcRequest {
            jsonrpc: "2.0".to_string(),
            method: "initialize".to_string(),
            params: Some(serde_json::to_value(init_params)?),
            id: serde_json::Value::String(uuid::Uuid::new_v4().to_string()),
        };

        match self.send_json_rpc_request(&request).await {
            Ok(response) => {
                if let Some(result) = response.result {
                    let init_result: McpInitializeResult =
                        serde_json::from_value(result).map_err(|e| {
                            AppError::QianwenApiError(format!("初始化响应解析失败: {}", e))
                        })?;

                    // 发送initialized通知
                    self.send_initialized_notification().await?;

                    self.connection_state = McpConnectionState::Connected;
                    tracing::info!(
                        "MCP连接初始化成功，服务器: {}",
                        init_result.server_info.name
                    );
                    Ok(init_result)
                } else if let Some(error) = response.error {
                    let error_msg =
                        format!("MCP初始化失败: {} (代码: {})", error.message, error.code);
                    self.connection_state = McpConnectionState::Error(error_msg.clone());
                    Err(AppError::QianwenApiError(error_msg))
                } else {
                    let error_msg = "MCP初始化响应格式错误".to_string();
                    self.connection_state = McpConnectionState::Error(error_msg.clone());
                    Err(AppError::QianwenApiError(error_msg))
                }
            }
            Err(e) => {
                self.connection_state = McpConnectionState::Error(e.to_string());
                Err(e)
            }
        }
    }

    /// 发送initialized通知 - MCP协议要求
    async fn send_initialized_notification(&self) -> AppResult<()> {
        let notification = JsonRpcRequest {
            jsonrpc: "2.0".to_string(),
            method: "notifications/initialized".to_string(),
            params: Some(serde_json::Value::Object(serde_json::Map::new())),
            id: serde_json::Value::Null, // 通知没有ID
        };

        self.send_notification(&notification).await
    }

    /// 获取地图数据 - 使用MCP资源协议
    pub async fn get_map_data(
        &self,
        query: &str,
        location: Option<&str>,
    ) -> AppResult<AmapMapResult> {
        // 确保连接已初始化
        if self.connection_state != McpConnectionState::Connected {
            return Err(AppError::QianwenApiError("MCP连接未初始化".to_string()));
        }

        // 构建资源URI
        let resource_uri = self.build_map_resource_uri(query, location)?;

        tracing::info!("请求MCP地图资源: {}", resource_uri);

        let mut retry_count = 0;
        loop {
            match self.read_map_resource(&resource_uri).await {
                Ok(map_data) => {
                    tracing::info!("MCP地图数据获取成功，POI数量: {}", map_data.pois.len());
                    return Ok(map_data);
                }
                Err(e) => {
                    retry_count += 1;
                    if retry_count >= self.retry_attempts {
                        tracing::error!("MCP请求失败，已达到最大重试次数: {}", e);
                        return Err(e);
                    }
                    tracing::warn!(
                        "MCP请求失败，正在重试 ({}/{}): {}",
                        retry_count,
                        self.retry_attempts,
                        e
                    );
                    tokio::time::sleep(Duration::from_millis(1000 * retry_count as u64)).await;
                }
            }
        }
    }

    /// 构建地图资源URI
    fn build_map_resource_uri(&self, query: &str, location: Option<&str>) -> AppResult<String> {
        let mut uri = format!("amap://search?q={}", urlencoding::encode(query));

        if let Some(loc) = location {
            uri.push_str(&format!("&location={}", urlencoding::encode(loc)));
        }

        // 添加默认参数
        uri.push_str("&radius=5000&types=poi");

        Ok(uri)
    }

    /// 读取地图资源
    async fn read_map_resource(&self, resource_uri: &str) -> AppResult<AmapMapResult> {
        let params = McpReadResourceParams {
            uri: resource_uri.to_string(),
        };

        let request = JsonRpcRequest {
            jsonrpc: "2.0".to_string(),
            method: "resources/read".to_string(),
            params: Some(serde_json::to_value(params)?),
            id: serde_json::Value::String(uuid::Uuid::new_v4().to_string()),
        };

        let response = self.send_json_rpc_request(&request).await?;

        if let Some(result) = response.result {
            let read_result: McpReadResourceResult = serde_json::from_value(result)
                .map_err(|e| AppError::QianwenApiError(format!("资源读取响应解析失败: {}", e)))?;

            // 解析地图数据
            self.parse_map_resource_content(&read_result)
        } else if let Some(error) = response.error {
            Err(AppError::QianwenApiError(format!(
                "MCP资源读取失败: {} (代码: {})",
                error.message, error.code
            )))
        } else {
            Err(AppError::QianwenApiError(
                "MCP资源读取响应格式错误".to_string(),
            ))
        }
    }

    /// 解析地图资源内容
    fn parse_map_resource_content(
        &self,
        read_result: &McpReadResourceResult,
    ) -> AppResult<AmapMapResult> {
        for content in &read_result.contents {
            if content.mime_type == "application/json" {
                if let Some(text) = &content.text {
                    let map_data: AmapMapResult = serde_json::from_str(text).map_err(|e| {
                        AppError::QianwenApiError(format!("地图数据解析失败: {}", e))
                    })?;
                    return Ok(map_data);
                }
            }
        }

        Err(AppError::QianwenApiError(
            "未找到有效的地图数据内容".to_string(),
        ))
    }

    /// 发送JSON-RPC 2.0请求 - 符合MCP标准
    async fn send_json_rpc_request(&self, request: &JsonRpcRequest) -> AppResult<JsonRpcResponse> {
        let url = format!("{}?key={}", self.server_url, self.api_key);

        tracing::debug!("发送JSON-RPC请求到: {}", url);
        tracing::debug!("请求内容: {:?}", request);

        let response = self
            .client
            .post(&url)
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .header("User-Agent", "ai-chat-backend/1.0.0")
            .json(request)
            .send()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("JSON-RPC请求发送失败: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());

            // 根据HTTP状态码返回相应的MCP错误
            let error_code = match status.as_u16() {
                400 => mcp_error_codes::INVALID_REQUEST,
                401 => mcp_error_codes::AUTHENTICATION_FAILED,
                404 => mcp_error_codes::METHOD_NOT_FOUND,
                408 => mcp_error_codes::CONNECTION_TIMEOUT,
                500..=599 => mcp_error_codes::INTERNAL_ERROR,
                _ => mcp_error_codes::INTERNAL_ERROR,
            };

            return Err(AppError::QianwenApiError(format!(
                "MCP服务器错误 ({}) [代码: {}]: {}",
                status, error_code, error_text
            )));
        }

        let response_text = response
            .text()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("读取JSON-RPC响应失败: {}", e)))?;

        tracing::debug!("收到JSON-RPC响应: {}", response_text);

        serde_json::from_str(&response_text)
            .map_err(|e| AppError::QianwenApiError(format!("JSON-RPC响应解析失败: {}", e)))
    }

    /// 发送通知 - 不期望响应
    async fn send_notification(&self, notification: &JsonRpcRequest) -> AppResult<()> {
        let url = format!("{}?key={}", self.server_url, self.api_key);

        tracing::debug!("发送JSON-RPC通知到: {}", url);

        let response = self
            .client
            .post(&url)
            .header("Content-Type", "application/json")
            .header("User-Agent", "ai-chat-backend/1.0.0")
            .json(notification)
            .send()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("JSON-RPC通知发送失败: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            return Err(AppError::QianwenApiError(format!(
                "MCP通知发送失败 ({}): {}",
                status, error_text
            )));
        }

        Ok(())
    }

    /// 列出可用的MCP资源
    pub async fn list_resources(
        &self,
        cursor: Option<String>,
    ) -> AppResult<McpListResourcesResult> {
        if self.connection_state != McpConnectionState::Connected {
            return Err(AppError::QianwenApiError("MCP连接未初始化".to_string()));
        }

        let params = McpListResourcesParams { cursor };

        let request = JsonRpcRequest {
            jsonrpc: "2.0".to_string(),
            method: "resources/list".to_string(),
            params: Some(serde_json::to_value(params)?),
            id: serde_json::Value::String(uuid::Uuid::new_v4().to_string()),
        };

        let response = self.send_json_rpc_request(&request).await?;

        if let Some(result) = response.result {
            let list_result: McpListResourcesResult = serde_json::from_value(result)
                .map_err(|e| AppError::QianwenApiError(format!("资源列表响应解析失败: {}", e)))?;

            tracing::info!("获取到 {} 个MCP资源", list_result.resources.len());
            Ok(list_result)
        } else if let Some(error) = response.error {
            Err(AppError::QianwenApiError(format!(
                "MCP资源列表获取失败: {} (代码: {})",
                error.message, error.code
            )))
        } else {
            Err(AppError::QianwenApiError(
                "MCP资源列表响应格式错误".to_string(),
            ))
        }
    }

    /// 检查连接状态
    pub fn is_connected(&self) -> bool {
        self.connection_state == McpConnectionState::Connected
    }

    /// 获取连接状态
    pub fn get_connection_state(&self) -> &McpConnectionState {
        &self.connection_state
    }

    /// 关闭MCP连接
    pub async fn close(&mut self) -> AppResult<()> {
        if self.connection_state == McpConnectionState::Connected {
            // 发送关闭通知（如果MCP服务器支持）
            let notification = JsonRpcRequest {
                jsonrpc: "2.0".to_string(),
                method: "notifications/close".to_string(),
                params: Some(serde_json::Value::Object(serde_json::Map::new())),
                id: serde_json::Value::Null,
            };

            // 忽略关闭通知的错误，因为连接可能已经断开
            let _ = self.send_notification(&notification).await;
        }

        self.connection_state = McpConnectionState::Disconnected;
        tracing::info!("MCP连接已关闭");
        Ok(())
    }
}

/// 文本聊天请求
#[derive(Debug, Serialize)]
pub struct TextChatRequest {
    pub model: String,
    pub input: TextInput,
    pub parameters: Option<ChatParameters>,
}

/// 文本输入
#[derive(Debug, Serialize)]
pub struct TextInput {
    pub messages: Vec<ChatMessage>,
}

/// 聊天消息 - 支持多模态内容
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: String, // "user", "assistant", "system"
    pub content: MessageContent,
}

/// 消息内容 - 支持文本和多模态格式
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum MessageContent {
    /// 纯文本消息（向后兼容）
    Text(String),
    /// 多模态消息内容（文本+图片）
    Multimodal(Vec<ContentPart>),
}

/// 内容部分 - 文本或图片
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentPart {
    #[serde(rename = "type")]
    pub content_type: String, // "text" 或 "image_url"
    #[serde(flatten)]
    pub data: ContentData,
}

/// 内容数据
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ContentData {
    Text { text: String },
    ImageUrl { image_url: ImageUrl },
}

/// 聊天参数
#[derive(Debug, Serialize)]
pub struct ChatParameters {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_p: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub stream: Option<bool>, // 是否启用流式响应
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_k: Option<u32>,
}

/// 视觉理解请求（使用OpenAI兼容格式）
#[derive(Debug, Serialize)]
pub struct VisionRequest {
    pub model: String,
    pub messages: Vec<VisionMessage>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_p: Option<f32>,
}

/// 视觉消息
#[derive(Debug, Serialize)]
pub struct VisionMessage {
    pub role: String,
    pub content: Vec<VisionContent>,
}

/// 视觉内容（OpenAI兼容格式）
#[derive(Debug, Serialize)]
pub struct VisionContent {
    #[serde(rename = "type")]
    pub content_type: String,
    #[serde(flatten)]
    pub data: VisionContentData,
}

/// 视觉内容数据
#[derive(Debug, Serialize)]
#[serde(untagged)]
pub enum VisionContentData {
    Text { text: String },
    ImageUrl { image_url: ImageUrl },
}

/// 图片URL（OpenAI兼容格式）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageUrl {
    pub url: String,
}

/// API响应（OpenAI兼容格式）
#[derive(Debug, Deserialize)]
pub struct QianwenResponse {
    pub choices: Option<Vec<Choice>>,
    pub usage: Option<Usage>,
    pub id: Option<String>,
    pub object: Option<String>,
    pub created: Option<u64>,
    pub model: Option<String>,
}

/// 响应输出（保留用于向后兼容）
#[derive(Debug, Deserialize)]
pub struct ResponseOutput {
    pub text: Option<String>,
    pub choices: Option<Vec<Choice>>,
}

/// 选择项
#[derive(Debug, Deserialize)]
pub struct Choice {
    pub message: ChatMessage,
    pub finish_reason: String,
}

/// 使用统计（OpenAI兼容格式）
#[derive(Debug, Deserialize)]
pub struct Usage {
    #[serde(alias = "input_tokens")]
    pub prompt_tokens: u32,
    #[serde(alias = "output_tokens")]
    pub completion_tokens: u32,
    pub total_tokens: u32,
    #[serde(default)]
    pub prompt_tokens_details: Option<serde_json::Value>,
}

/// 流式响应数据块
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StreamChunk {
    pub content: String,
    pub is_final: bool,
    pub chunk_id: Option<String>,
}

/// 流式响应数据块（支持JSON对象）
#[derive(Debug, Serialize, Clone)]
pub struct StreamChunkWithDelta {
    pub delta: serde_json::Value,
    pub is_final: bool,
    pub chunk_id: Option<String>,
}

/// 流式响应事件
#[derive(Debug, Serialize, Clone)]
pub struct StreamEvent {
    pub event_type: String, // "chunk", "error", "done"
    pub data: StreamEventData,
}

/// 流式响应事件数据
#[derive(Debug, Serialize, Clone)]
#[serde(untagged)]
pub enum StreamEventData {
    Chunk(StreamChunk),
    ChunkWithDelta(StreamChunkWithDelta),
    Error { message: String },
    Done { total_tokens: Option<u32> },
}

/// API错误响应
#[derive(Debug, Deserialize)]
pub struct QianwenError {
    pub code: String,
    pub message: String,
    pub request_id: Option<String>,
}

impl ChatMessage {
    /// 创建纯文本消息
    pub fn text(role: &str, content: &str) -> Self {
        Self {
            role: role.to_string(),
            content: MessageContent::Text(content.to_string()),
        }
    }

    /// 创建多模态消息（文本+图片）
    pub fn multimodal(role: &str, text: &str, image_url: &str) -> Self {
        Self {
            role: role.to_string(),
            content: MessageContent::Multimodal(vec![
                ContentPart {
                    content_type: "text".to_string(),
                    data: ContentData::Text {
                        text: text.to_string(),
                    },
                },
                ContentPart {
                    content_type: "image_url".to_string(),
                    data: ContentData::ImageUrl {
                        image_url: ImageUrl {
                            url: image_url.to_string(),
                        },
                    },
                },
            ]),
        }
    }

    /// 从旧版本的ChatMessage转换（向后兼容）
    pub fn from_legacy(role: String, content: String) -> Self {
        Self {
            role,
            content: MessageContent::Text(content),
        }
    }

    /// 从前端多模态内容创建ChatMessage
    pub fn from_multimodal_content(role: &str, content: serde_json::Value) -> AppResult<Self> {
        match content {
            // 纯文本消息
            serde_json::Value::String(text) => Ok(Self::text(role, &text)),
            // 多模态消息数组
            serde_json::Value::Array(parts) => {
                let mut content_parts = Vec::new();

                for part in parts {
                    let part_obj = part.as_object().ok_or_else(|| {
                        AppError::ValidationError("多模态内容部分必须是对象".to_string())
                    })?;

                    let content_type =
                        part_obj
                            .get("type")
                            .and_then(|v| v.as_str())
                            .ok_or_else(|| {
                                AppError::ValidationError("多模态内容部分缺少type字段".to_string())
                            })?;

                    match content_type {
                        "text" => {
                            let text =
                                part_obj
                                    .get("text")
                                    .and_then(|v| v.as_str())
                                    .ok_or_else(|| {
                                        AppError::ValidationError(
                                            "文本内容缺少text字段".to_string(),
                                        )
                                    })?;

                            content_parts.push(ContentPart {
                                content_type: "text".to_string(),
                                data: ContentData::Text {
                                    text: text.to_string(),
                                },
                            });
                        }
                        "image_url" => {
                            let image_url_obj = part_obj
                                .get("image_url")
                                .and_then(|v| v.as_object())
                                .ok_or_else(|| {
                                    AppError::ValidationError(
                                        "图片内容缺少image_url字段".to_string(),
                                    )
                                })?;

                            let url = image_url_obj
                                .get("url")
                                .and_then(|v| v.as_str())
                                .ok_or_else(|| {
                                    AppError::ValidationError("图片URL缺少url字段".to_string())
                                })?;

                            content_parts.push(ContentPart {
                                content_type: "image_url".to_string(),
                                data: ContentData::ImageUrl {
                                    image_url: ImageUrl {
                                        url: url.to_string(),
                                    },
                                },
                            });
                        }
                        _ => {
                            return Err(AppError::ValidationError(format!(
                                "不支持的内容类型: {}",
                                content_type
                            )));
                        }
                    }
                }

                if content_parts.is_empty() {
                    return Err(AppError::ValidationError("多模态内容不能为空".to_string()));
                }

                Ok(Self {
                    role: role.to_string(),
                    content: MessageContent::Multimodal(content_parts),
                })
            }
            _ => Err(AppError::ValidationError(
                "不支持的内容格式，必须是字符串或数组".to_string(),
            )),
        }
    }

    /// 获取文本内容（用于显示和存储）
    pub fn get_text_content(&self) -> String {
        match &self.content {
            MessageContent::Text(text) => text.clone(),
            MessageContent::Multimodal(parts) => parts
                .iter()
                .filter_map(|part| match &part.data {
                    ContentData::Text { text } => Some(text.clone()),
                    _ => None,
                })
                .collect::<Vec<_>>()
                .join(" "),
        }
    }

    /// 检查是否包含图片
    pub fn has_image(&self) -> bool {
        match &self.content {
            MessageContent::Text(_) => false,
            MessageContent::Multimodal(parts) => parts
                .iter()
                .any(|part| matches!(part.data, ContentData::ImageUrl { .. })),
        }
    }
}

impl QianwenService {
    /// 创建新的通义千问服务实例
    pub fn new(config: QianwenConfig) -> AppResult<Self> {
        let mut headers = HeaderMap::new();
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {}", config.api_key))
                .map_err(|e| AppError::QianwenApiError(format!("无效的API密钥格式: {}", e)))?,
        );
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

        let client = Client::builder()
            .timeout(Duration::from_secs(60))
            .default_headers(headers)
            .build()
            .map_err(|e| AppError::QianwenApiError(format!("HTTP客户端创建失败: {}", e)))?;

        let service = Self {
            client,
            config,
            amap_config: None,
            mcp_client: None,
        };

        // 验证模型配置
        service.validate_model_config()?;

        Ok(service)
    }

    /// 创建带有高德地图MCP支持的通义千问服务实例
    pub fn new_with_amap(config: QianwenConfig, amap_config: AmapConfig) -> AppResult<Self> {
        let mut headers = HeaderMap::new();
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {}", config.api_key))
                .map_err(|e| AppError::QianwenApiError(format!("无效的API密钥格式: {}", e)))?,
        );
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

        let client = Client::builder()
            .timeout(Duration::from_secs(60))
            .default_headers(headers)
            .build()
            .map_err(|e| AppError::QianwenApiError(format!("HTTP客户端创建失败: {}", e)))?;

        // 创建MCP客户端（如果启用）
        let mcp_client = if amap_config.mcp_enabled {
            Some(McpClient::new(&amap_config)?)
        } else {
            None
        };

        let service = Self {
            client,
            config,
            amap_config: Some(amap_config),
            mcp_client,
        };

        // 验证模型配置
        service.validate_model_config()?;

        Ok(service)
    }

    /// 初始化MCP连接 - 必须在使用MCP功能前调用
    pub async fn initialize_mcp(&mut self) -> AppResult<()> {
        if let Some(ref mut mcp_client) = self.mcp_client {
            if !mcp_client.is_connected() {
                tracing::info!("正在初始化MCP连接...");
                let init_result = mcp_client.initialize().await?;
                tracing::info!(
                    "MCP连接初始化成功，服务器: {} v{}",
                    init_result.server_info.name,
                    init_result.server_info.version
                );

                // 可选：列出可用资源
                if let Ok(resources) = mcp_client.list_resources(None).await {
                    tracing::info!("发现 {} 个MCP资源", resources.resources.len());
                    for resource in resources.resources.iter().take(5) {
                        tracing::debug!("MCP资源: {} ({})", resource.name, resource.uri);
                    }
                }
            }
        }
        Ok(())
    }

    /// 检查MCP连接状态
    pub fn is_mcp_connected(&self) -> bool {
        self.mcp_client
            .as_ref()
            .map(|client| client.is_connected())
            .unwrap_or(false)
    }

    /// 关闭MCP连接
    pub async fn close_mcp(&mut self) -> AppResult<()> {
        if let Some(ref mut mcp_client) = self.mcp_client {
            mcp_client.close().await?;
        }
        Ok(())
    }

    /// 发送文本聊天请求
    pub async fn chat_text(&self, messages: Vec<ChatMessage>) -> AppResult<String> {
        // 转换为OpenAI兼容格式
        let openai_messages: Vec<serde_json::Value> = messages
            .into_iter()
            .map(|msg| self.convert_to_openai_format(msg))
            .collect();

        let request = serde_json::json!({
            "model": self.config.model_text, // 使用文本模型
            "messages": openai_messages,
            "temperature": 0.7,
            "max_tokens": 2000,
            "top_p": 0.8
        });

        let url = format!(
            "{}/compatible-mode/v1/chat/completions",
            self.config.base_url
        );

        tracing::info!("发送文本聊天请求到: {}", url);
        tracing::debug!(
            "文本聊天请求数据: {}",
            serde_json::to_string_pretty(&request).unwrap_or_else(|_| "序列化失败".to_string())
        );

        let response = self
            .client
            .post(&url)
            .json(&request)
            .send()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("请求发送失败: {}", e)))?;

        let status = response.status();
        if !status.is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            tracing::error!("API请求失败，状态码: {}, 错误: {}", status, error_text);
            return Err(AppError::QianwenApiError(format!(
                "API请求失败: {}",
                error_text
            )));
        }

        // 先获取原始响应文本用于调试
        let response_text = response
            .text()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("读取响应失败: {}", e)))?;

        tracing::info!("收到API响应: {}", response_text);

        // 尝试解析为JSON
        let qianwen_response: QianwenResponse = serde_json::from_str(&response_text)
            .map_err(|e| AppError::QianwenApiError(format!("响应解析失败: {}", e)))?;

        // 提取响应文本（OpenAI兼容格式）
        if let Some(choices) = qianwen_response.choices {
            if let Some(choice) = choices.first() {
                Ok(choice.message.get_text_content())
            } else {
                Err(AppError::QianwenApiError("API响应为空".to_string()))
            }
        } else {
            Err(AppError::QianwenApiError(
                "API响应格式错误：缺少choices字段".to_string(),
            ))
        }
    }

    /// 发送视觉理解请求
    pub async fn analyze_image(
        &self,
        image_url: String,
        prompt: Option<String>,
    ) -> AppResult<String> {
        let content = vec![
            VisionContent {
                content_type: "text".to_string(),
                data: VisionContentData::Text {
                    text: prompt.unwrap_or_else(|| "请描述这张图片的内容".to_string()),
                },
            },
            VisionContent {
                content_type: "image_url".to_string(),
                data: VisionContentData::ImageUrl {
                    image_url: ImageUrl { url: image_url },
                },
            },
        ];

        let request = VisionRequest {
            model: self.config.model_vision.clone(),
            messages: vec![VisionMessage {
                role: "user".to_string(),
                content,
            }],
            temperature: Some(0.1),
            max_tokens: Some(1500),
            top_p: Some(0.8),
        };

        // 使用OpenAI兼容的端点
        let url = format!(
            "{}/compatible-mode/v1/chat/completions",
            self.config.base_url
        );

        // 添加调试信息
        tracing::info!("发送图像分析请求到: {}", url);
        tracing::debug!(
            "请求数据: {}",
            serde_json::to_string_pretty(&request).unwrap_or_else(|_| "序列化失败".to_string())
        );

        let response = self
            .client
            .post(&url)
            .json(&request)
            .send()
            .await
            .map_err(|e| {
                tracing::error!("图像分析请求失败: {}", e);
                AppError::QianwenApiError(format!("图像分析请求失败: {}", e))
            })?;

        let status = response.status();
        tracing::info!("图像分析API响应状态: {}", status);

        if !status.is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            tracing::error!("图像分析API错误响应: {}", error_text);
            return Err(AppError::QianwenApiError(format!(
                "图像分析API请求失败 (状态码: {}): {}",
                status, error_text
            )));
        }

        // 先获取响应文本用于调试
        let response_text = response
            .text()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("响应文本读取失败: {}", e)))?;

        tracing::debug!("图像分析API原始响应: {}", response_text);

        // 尝试解析为OpenAI兼容格式
        let qianwen_response: QianwenResponse =
            serde_json::from_str(&response_text).map_err(|e| {
                tracing::error!("图像分析响应解析失败: {}", e);
                tracing::error!("原始响应内容: {}", response_text);
                AppError::QianwenApiError(format!("图像分析响应解析失败: {}", e))
            })?;

        // 提取响应文本（OpenAI兼容格式）
        if let Some(choices) = qianwen_response.choices {
            if let Some(choice) = choices.first() {
                let content = choice.message.get_text_content();
                tracing::info!("图像分析成功，响应长度: {}", content.len());
                Ok(content)
            } else {
                Err(AppError::QianwenApiError("图像分析响应为空".to_string()))
            }
        } else {
            Err(AppError::QianwenApiError(
                "图像分析响应格式错误：缺少choices字段".to_string(),
            ))
        }
    }

    /// 统一的多模态聊天方法 - 支持文本和图片混合消息，集成MCP地图数据增强
    pub async fn chat_multimodal(&self, messages: Vec<ChatMessage>) -> AppResult<String> {
        // 首先尝试构建包含MCP地图数据的上下文
        let enhanced_messages = self.build_context_with_map_data(messages).await?;

        // 检查是否包含图片，决定使用哪个API端点
        let has_images = enhanced_messages.iter().any(|msg| msg.has_image());

        if has_images {
            // 使用OpenAI兼容的多模态端点
            self.chat_multimodal_openai_compatible(enhanced_messages)
                .await
        } else {
            // 使用传统的文本聊天端点
            self.chat_text(enhanced_messages).await
        }
    }

    /// 使用OpenAI兼容端点进行多模态聊天
    async fn chat_multimodal_openai_compatible(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<String> {
        // 转换为OpenAI兼容格式
        let openai_messages: Vec<serde_json::Value> = messages
            .into_iter()
            .map(|msg| self.convert_to_openai_format(msg))
            .collect();

        let request = serde_json::json!({
            "model": self.config.model_vision, // 使用视觉模型支持多模态
            "messages": openai_messages,
            "temperature": 0.7,
            "max_tokens": 2000,
            "top_p": 0.8
        });

        let url = format!(
            "{}/compatible-mode/v1/chat/completions",
            self.config.base_url
        );

        tracing::info!("发送多模态聊天请求到: {}", url);
        tracing::debug!(
            "请求数据: {}",
            serde_json::to_string_pretty(&request).unwrap_or_else(|_| "序列化失败".to_string())
        );

        let response = self
            .client
            .post(&url)
            .json(&request)
            .send()
            .await
            .map_err(|e| {
                tracing::error!("多模态聊天请求失败: {}", e);
                AppError::QianwenApiError(format!("多模态聊天请求失败: {}", e))
            })?;

        let status = response.status();
        tracing::info!("多模态聊天API响应状态: {}", status);

        if !status.is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            tracing::error!("多模态聊天API错误响应: {}", error_text);
            return Err(AppError::QianwenApiError(format!(
                "多模态聊天API请求失败 (状态码: {}): {}",
                status, error_text
            )));
        }

        let response_text = response
            .text()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("响应文本读取失败: {}", e)))?;

        tracing::debug!("多模态聊天API原始响应: {}", response_text);

        let qianwen_response: QianwenResponse =
            serde_json::from_str(&response_text).map_err(|e| {
                tracing::error!("多模态聊天响应解析失败: {}", e);
                tracing::error!("原始响应内容: {}", response_text);
                AppError::QianwenApiError(format!("多模态聊天响应解析失败: {}", e))
            })?;

        // 提取响应文本
        if let Some(choices) = qianwen_response.choices {
            if let Some(choice) = choices.first() {
                let content = choice.message.get_text_content();
                tracing::info!("多模态聊天成功，响应长度: {}", content.len());
                Ok(content)
            } else {
                Err(AppError::QianwenApiError("多模态聊天响应为空".to_string()))
            }
        } else {
            Err(AppError::QianwenApiError(
                "多模态聊天响应格式错误：缺少choices字段".to_string(),
            ))
        }
    }

    /// 将ChatMessage转换为OpenAI兼容格式
    fn convert_to_openai_format(&self, message: ChatMessage) -> serde_json::Value {
        match message.content {
            MessageContent::Text(text) => {
                serde_json::json!({
                    "role": message.role,
                    "content": text
                })
            }
            MessageContent::Multimodal(parts) => {
                let content_array: Vec<serde_json::Value> = parts
                    .into_iter()
                    .map(|part| match part.data {
                        ContentData::Text { text } => {
                            serde_json::json!({
                                "type": "text",
                                "text": text
                            })
                        }
                        ContentData::ImageUrl { image_url } => {
                            serde_json::json!({
                                "type": "image_url",
                                "image_url": {
                                    "url": image_url.url
                                }
                            })
                        }
                    })
                    .collect();

                serde_json::json!({
                    "role": message.role,
                    "content": content_array
                })
            }
        }
    }

    /// 检查API连接状态
    pub async fn health_check(&self) -> AppResult<()> {
        let test_messages = vec![ChatMessage::text("user", "你好")];

        // 发送一个简单的测试请求
        self.chat_text(test_messages).await?;
        Ok(())
    }

    /// 验证模型配置
    pub fn validate_model_config(&self) -> AppResult<()> {
        // 检查文本模型配置
        if self.config.model_text.is_empty() {
            return Err(AppError::QianwenApiError(
                "文本模型配置不能为空".to_string(),
            ));
        }

        // 检查视觉模型配置
        if self.config.model_vision.is_empty() {
            return Err(AppError::QianwenApiError(
                "视觉模型配置不能为空".to_string(),
            ));
        }

        // 验证推荐的模型配置
        let recommended_text_models = ["qwen-turbo", "qwen-plus", "qwen-max"];
        let recommended_vision_models = ["qwen-vl-plus", "qwen-vl-max"];

        if !recommended_text_models.contains(&self.config.model_text.as_str()) {
            tracing::warn!(
                "使用非推荐的文本模型: {}，推荐使用: {:?}",
                self.config.model_text,
                recommended_text_models
            );
        }

        if !recommended_vision_models.contains(&self.config.model_vision.as_str()) {
            tracing::warn!(
                "使用非推荐的视觉模型: {}，推荐使用: {:?}",
                self.config.model_vision,
                recommended_vision_models
            );
        }

        tracing::info!(
            "模型配置验证通过 - 文本模型: {}, 视觉模型: {}",
            self.config.model_text,
            self.config.model_vision
        );

        Ok(())
    }

    /// 获取当前使用的模型信息
    pub fn get_model_info(&self) -> serde_json::Value {
        serde_json::json!({
            "text_model": self.config.model_text,
            "vision_model": self.config.model_vision,
            "base_url": self.config.base_url,
            "api_configured": !self.config.api_key.is_empty()
        })
    }

    /// 统一的流式多模态聊天方法 - 支持文本和图片混合消息，集成MCP地图数据增强
    pub async fn chat_multimodal_stream(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<Pin<Box<dyn Stream<Item = Result<StreamEvent, AppError>> + Send>>> {
        // 首先尝试构建包含MCP地图数据的上下文
        let enhanced_messages = self.build_context_with_map_data(messages).await?;

        // 检查是否包含图片，决定使用哪个API端点
        let has_images = enhanced_messages.iter().any(|msg| msg.has_image());

        if has_images {
            // 使用OpenAI兼容的多模态流式端点
            self.chat_multimodal_stream_openai_compatible(enhanced_messages)
                .await
        } else {
            // 使用传统的文本聊天流式端点
            self.chat_text_stream(enhanced_messages).await
        }
    }

    /// 使用OpenAI兼容端点进行多模态流式聊天
    async fn chat_multimodal_stream_openai_compatible(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<Pin<Box<dyn Stream<Item = Result<StreamEvent, AppError>> + Send>>> {
        tracing::info!("开始多模态流式API请求");

        // 克隆消息以备后用
        let messages_clone = messages.clone();

        // 转换为OpenAI兼容格式
        let openai_messages: Vec<serde_json::Value> = messages
            .into_iter()
            .map(|msg| self.convert_to_openai_format(msg))
            .collect();

        let request = serde_json::json!({
            "model": self.config.model_vision, // 使用视觉模型支持多模态
            "messages": openai_messages,
            "temperature": 0.7,
            "max_tokens": 2000,
            "top_p": 0.8,
            "stream": true // 启用流式响应
        });
        tracing::info!(
            "多模态流式请求数据: {}",
            serde_json::to_string_pretty(&request).unwrap_or_else(|_| "序列化失败".to_string())
        );

        let url = format!(
            "{}/compatible-mode/v1/chat/completions",
            self.config.base_url
        );

        tracing::info!("发送多模态流式请求到: {}", url);

        // 发送流式请求
        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .header("Accept", "text/event-stream")
            .json(&request)
            .send()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("多模态流式请求发送失败: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());

            tracing::error!("多模态流式API请求失败: {} - {}", status, error_text);

            // 如果流式API失败，降级到模拟流式
            tracing::warn!("降级到模拟多模态流式响应");
            return self.simulate_multimodal_stream(messages_clone).await;
        }

        tracing::info!("多模态流式API请求成功，开始处理响应流");

        // 创建真实的流式响应处理器
        let stream = self.create_real_stream(response).await?;
        Ok(Box::pin(stream))
    }

    /// 模拟多模态流式响应（当API不支持真实流式时）
    async fn simulate_multimodal_stream(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<Pin<Box<dyn Stream<Item = Result<StreamEvent, AppError>> + Send>>> {
        // 先获取完整响应
        let full_response = self.chat_multimodal(messages).await?;
        tracing::info!(
            "开始模拟多模态流式响应，完整内容长度: {}",
            full_response.len()
        );

        // 将响应分割成块进行流式发送
        let chunks = self.split_response_into_chunks(&full_response);
        tracing::info!("分割成 {} 个数据块", chunks.len());

        // 创建真正的异步流
        let stream = async_stream::stream! {
            let total_chunks = chunks.len();

            for (i, chunk) in chunks.into_iter().enumerate() {
                let is_final = i == total_chunks - 1;

                // 创建流事件
                let event = StreamEvent {
                    event_type: if is_final {
                        "done".to_string()
                    } else {
                        "chunk".to_string()
                    },
                    data: if is_final {
                        StreamEventData::Done { total_tokens: None }
                    } else {
                        StreamEventData::Chunk(StreamChunk {
                            content: chunk.clone(),
                            is_final: false,
                            chunk_id: Some(format!("multimodal_chunk_{}", i)),
                        })
                    },
                };

                tracing::debug!("发送多模态数据块 {}/{}: {:?}", i + 1, total_chunks, chunk);

                // 立即发送事件
                yield Ok(event);

                // 如果不是最后一个块，添加延迟模拟真实流式效果
                if !is_final {
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }

            tracing::info!("模拟多模态流式响应完成");
        };

        Ok(Box::pin(stream))
    }

    /// 发送流式文本聊天请求
    /// 使用真实的流式API响应，提供实时数据块传输
    pub async fn chat_text_stream(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<Pin<Box<dyn Stream<Item = Result<StreamEvent, AppError>> + Send>>> {
        tracing::info!("开始真实流式API请求");

        let request = TextChatRequest {
            model: self.config.model_text.clone(),
            input: TextInput { messages },
            parameters: Some(ChatParameters {
                max_tokens: Some(2000),
                temperature: Some(0.7),
                top_p: Some(0.8),
                top_k: None,
                stream: Some(true), // 启用流式响应
            }),
        };

        let url = format!(
            "{}/services/aigc/text-generation/generation",
            self.config.base_url
        );

        // 发送流式请求
        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .header("Content-Type", "application/json")
            .header("Accept", "text/event-stream")
            .json(&request)
            .send()
            .await
            .map_err(|e| AppError::QianwenApiError(format!("流式请求发送失败: {}", e)))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());

            tracing::error!("流式API请求失败: {} - {}", status, error_text);

            // 如果流式API失败，降级到模拟流式
            tracing::warn!("降级到模拟流式响应");
            return self.simulate_stream(request.input.messages).await;
        }

        tracing::info!("流式API请求成功，开始处理响应流");

        // 创建真实的流式响应处理器
        let stream = self.create_real_stream(response).await?;
        Ok(Box::pin(stream))
    }

    /// 创建真实的流式响应处理器
    async fn create_real_stream(
        &self,
        response: reqwest::Response,
    ) -> AppResult<Pin<Box<dyn Stream<Item = Result<StreamEvent, AppError>> + Send>>> {
        use tokio::io::{AsyncBufReadExt, BufReader};
        use tokio_util::io::StreamReader;

        // 将响应转换为字节流
        let byte_stream = response.bytes_stream();

        // 创建一个流读取器
        // Use explicit trait to avoid ambiguity
        let mapped_stream = FuturesStreamExt::map(byte_stream, |result| {
            result.map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))
        });
        let stream_reader = StreamReader::new(mapped_stream);

        // 创建缓冲读取器以按行读取
        let mut buf_reader = BufReader::new(stream_reader);

        // 创建异步流来处理SSE数据
        let stream = async_stream::stream! {
            let mut line_buffer = String::new();
            let mut event_buffer = Vec::new();

            loop {
                line_buffer.clear();

                match buf_reader.read_line(&mut line_buffer).await {
                    Ok(0) => {
                        // 流结束
                        tracing::info!("流式响应结束");
                        break;
                    }
                    Ok(_) => {
                        let line = line_buffer.trim();
                        tracing::debug!("接收到SSE行: {:?}", line);

                        if line.is_empty() {
                            // 空行表示事件结束，处理累积的事件数据
                            if !event_buffer.is_empty() {
                                let event_data = event_buffer.join("\n");
                                match Self::parse_sse_event(&event_data) {
                                    Ok(Some(event)) => {
                                        tracing::debug!("解析到SSE事件: {:?}", event);
                                        yield Ok(event);
                                    }
                                    Ok(None) => {
                                        // 跳过无效事件
                                        tracing::debug!("跳过无效SSE事件");
                                    }
                                    Err(e) => {
                                        tracing::warn!("SSE事件解析失败: {}", e);
                                        // 继续处理下一个事件，不中断流
                                    }
                                }
                                event_buffer.clear();
                            }
                        } else {
                            // 累积事件行
                            event_buffer.push(line.to_string());
                        }
                    }
                    Err(e) => {
                        tracing::error!("读取流数据失败: {}", e);
                        yield Err(AppError::QianwenApiError(format!("读取流数据失败: {}", e)));
                        break;
                    }
                }
            }

            // 发送完成事件
            yield Ok(StreamEvent {
                event_type: "done".to_string(),
                data: StreamEventData::Done { total_tokens: None },
            });
        };

        Ok(Box::pin(stream))
    }

    /// 解析SSE事件数据
    fn parse_sse_event(event_data: &str) -> AppResult<Option<StreamEvent>> {
        tracing::debug!("解析SSE事件数据: {:?}", event_data);

        let lines: Vec<&str> = event_data.lines().collect();
        let mut data_parts = Vec::new();
        let mut event_type = "chunk".to_string();

        for line in lines {
            let line = line.trim();
            if line.is_empty() {
                continue;
            }

            if let Some(data_content) = line.strip_prefix("data: ") {
                data_parts.push(data_content);
            } else if let Some(event_content) = line.strip_prefix("event: ") {
                event_type = event_content.to_string();
            } else if line.starts_with("data:") {
                // 处理没有空格的data:格式
                data_parts.push(&line[5..]);
            } else if line.starts_with(":HTTP_STATUS/") {
                // 忽略HTTP状态行
                continue;
            } else if line.starts_with("id:") {
                // 忽略ID行
                continue;
            } else {
                // 可能是纯数据行
                data_parts.push(line);
            }
        }

        if data_parts.is_empty() {
            return Ok(None);
        }

        let combined_data = data_parts.join("\n");

        // 处理特殊的结束标记
        if combined_data.trim() == "[DONE]" {
            return Ok(Some(StreamEvent {
                event_type: "done".to_string(),
                data: StreamEventData::Done { total_tokens: None },
            }));
        }

        // 尝试解析通义千问API的复杂SSE格式
        if let Some(extracted_content) = Self::extract_qianwen_sse_content(&combined_data) {
            return Ok(Some(StreamEvent {
                event_type: "chunk".to_string(),
                data: StreamEventData::Chunk(StreamChunk {
                    content: extracted_content,
                    is_final: false,
                    chunk_id: None,
                }),
            }));
        }

        // 尝试解析JSON数据
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&combined_data) {
            tracing::debug!("成功解析JSON数据: {:?}", json_value);

            // 检查是否是OpenAI兼容格式（包含choices数组）
            if json_value.get("choices").is_some() {
                // 返回完整的JSON对象作为delta
                return Ok(Some(StreamEvent {
                    event_type,
                    data: StreamEventData::ChunkWithDelta(StreamChunkWithDelta {
                        delta: json_value,
                        is_final: false,
                        chunk_id: None,
                    }),
                }));
            }

            // 尝试提取文本内容（兼容旧格式）
            if let Some(content) = Self::extract_content_from_json(&json_value) {
                return Ok(Some(StreamEvent {
                    event_type,
                    data: StreamEventData::Chunk(StreamChunk {
                        content,
                        is_final: false,
                        chunk_id: None,
                    }),
                }));
            }
        }

        // 如果不是JSON，直接作为文本内容
        if !combined_data.trim().is_empty() {
            return Ok(Some(StreamEvent {
                event_type,
                data: StreamEventData::Chunk(StreamChunk {
                    content: combined_data,
                    is_final: false,
                    chunk_id: None,
                }),
            }));
        }

        Ok(None)
    }

    /// 从JSON中提取内容
    fn extract_content_from_json(json: &serde_json::Value) -> Option<String> {
        // 优先处理OpenAI兼容格式的流式响应
        if let Some(choices) = json.get("choices").and_then(|v| v.as_array()) {
            if let Some(choice) = choices.first() {
                // 处理流式响应中的delta字段
                if let Some(delta) = choice.get("delta") {
                    if let Some(content) = delta.get("content").and_then(|v| v.as_str()) {
                        return Some(content.to_string());
                    }
                }
                // 处理完整响应中的message字段
                if let Some(message) = choice.get("message") {
                    if let Some(content) = message.get("content").and_then(|v| v.as_str()) {
                        return Some(content.to_string());
                    }
                }
            }
        }

        // 处理传统通义千问格式
        if let Some(output) = json.get("output") {
            if let Some(text) = output.get("text").and_then(|v| v.as_str()) {
                return Some(text.to_string());
            }
            if let Some(choices) = output.get("choices").and_then(|v| v.as_array()) {
                if let Some(choice) = choices.first() {
                    if let Some(message) = choice.get("message") {
                        if let Some(content) = message.get("content").and_then(|v| v.as_str()) {
                            return Some(content.to_string());
                        }
                    }
                }
            }
        }

        // 直接查找content字段
        if let Some(content) = json.get("content").and_then(|v| v.as_str()) {
            return Some(content.to_string());
        }

        // 查找顶级delta字段（OpenAI格式）
        if let Some(delta) = json.get("delta") {
            if let Some(content) = delta.get("content").and_then(|v| v.as_str()) {
                return Some(content.to_string());
            }
        }

        // 查找text字段（备用）
        if let Some(text) = json.get("text").and_then(|v| v.as_str()) {
            return Some(text.to_string());
        }

        None
    }

    /// 模拟流式响应（当API不支持真实流式时）
    async fn simulate_stream(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<Pin<Box<dyn Stream<Item = Result<StreamEvent, AppError>> + Send>>> {
        // 先获取完整响应
        let full_response = self.chat_text(messages).await?;
        tracing::info!("开始模拟流式响应，完整内容长度: {}", full_response.len());

        // 将响应分割成块进行流式发送
        let chunks = self.split_response_into_chunks(&full_response);
        tracing::info!("分割成 {} 个数据块", chunks.len());

        // 创建真正的异步流
        let stream = async_stream::stream! {
            let total_chunks = chunks.len();

            for (i, chunk) in chunks.into_iter().enumerate() {
                let is_final = i == total_chunks - 1;

                // 创建流事件
                let event = StreamEvent {
                    event_type: if is_final {
                        "done".to_string()
                    } else {
                        "chunk".to_string()
                    },
                    data: if is_final {
                        StreamEventData::Done { total_tokens: None }
                    } else {
                        StreamEventData::Chunk(StreamChunk {
                            content: chunk.clone(),
                            is_final: false,
                            chunk_id: Some(format!("chunk_{}", i)),
                        })
                    },
                };

                tracing::debug!("发送数据块 {}/{}: {:?}", i + 1, total_chunks, chunk);

                // 立即发送事件
                yield Ok(event);

                // 如果不是最后一个块，添加延迟模拟真实流式效果
                if !is_final {
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }

            tracing::info!("模拟流式响应完成");
        };

        Ok(Box::pin(stream))
    }

    /// 解析SSE数据块（静态版本，用于流式处理）
    fn parse_sse_chunk_static(chunk_str: &str) -> Result<StreamEvent, AppError> {
        // 记录调试信息
        tracing::debug!("解析SSE数据块: {:?}", chunk_str);

        // 处理空数据或仅包含空白字符的数据
        let trimmed = chunk_str.trim();
        if trimmed.is_empty() {
            return Err(AppError::QianwenApiError("空的SSE数据块".to_string()));
        }

        // 解析SSE格式的数据
        let lines: Vec<&str> = trimmed.lines().collect();
        let mut event_type = String::new();
        let mut data_parts = Vec::new();
        let mut event_id = None;

        for line in lines {
            let line = line.trim();
            if line.is_empty() {
                continue;
            }

            if let Some(data_content) = line.strip_prefix("data: ") {
                data_parts.push(data_content);
            } else if let Some(event_content) = line.strip_prefix("event: ") {
                event_type = event_content.to_string();
            } else if let Some(id_content) = line.strip_prefix("id: ") {
                event_id = Some(id_content.to_string());
            } else if line.starts_with(": ") {
                // 注释行，忽略
                continue;
            } else if line.starts_with("data:") {
                // 处理没有空格的data:格式
                data_parts.push(&line[5..]);
            } else if line.starts_with(":HTTP_STATUS/") {
                // 忽略HTTP状态行
                continue;
            } else {
                // 可能是纯数据行（某些API不使用data:前缀）
                tracing::debug!("未识别的SSE行格式: {}", line);
            }
        }

        // 如果没有找到任何data部分，尝试将整个chunk作为数据处理
        if data_parts.is_empty() {
            data_parts.push(trimmed);
        }

        // 合并所有data部分
        let combined_data = data_parts.join("\n");

        // 处理特殊的结束标记
        if combined_data.trim() == "[DONE]" || combined_data.trim() == "data: [DONE]" {
            return Ok(StreamEvent {
                event_type: "done".to_string(),
                data: StreamEventData::Done { total_tokens: None },
            });
        }

        // 尝试解析通义千问API的复杂SSE格式
        if let Some(extracted_content) = Self::extract_qianwen_sse_content(&combined_data) {
            return Ok(StreamEvent {
                event_type: "chunk".to_string(),
                data: StreamEventData::Chunk(StreamChunk {
                    content: extracted_content,
                    is_final: false,
                    chunk_id: event_id,
                }),
            });
        }

        // 尝试解析JSON数据
        match serde_json::from_str::<serde_json::Value>(&combined_data) {
            Ok(chunk_data) => {
                tracing::debug!("成功解析JSON: {:?}", chunk_data);

                // 检查是否是OpenAI兼容格式（包含choices数组）
                if chunk_data.get("choices").is_some() {
                    // 返回完整的JSON对象作为delta
                    return Ok(StreamEvent {
                        event_type: if event_type.is_empty() {
                            "chunk".to_string()
                        } else {
                            event_type
                        },
                        data: StreamEventData::ChunkWithDelta(StreamChunkWithDelta {
                            delta: chunk_data,
                            is_final: false,
                            chunk_id: event_id,
                        }),
                    });
                }

                // 尝试从不同的字段中提取内容（兼容旧格式）
                let content = chunk_data
                    .get("content")
                    .and_then(|c| c.as_str())
                    .or_else(|| chunk_data.get("text").and_then(|c| c.as_str()))
                    .or_else(|| chunk_data.get("message").and_then(|c| c.as_str()))
                    .or_else(|| {
                        // 尝试从嵌套的output字段中获取
                        chunk_data
                            .get("output")
                            .and_then(|output| output.get("text"))
                            .and_then(|text| text.as_str())
                    })
                    .unwrap_or("");

                if !content.is_empty() {
                    return Ok(StreamEvent {
                        event_type: if event_type.is_empty() {
                            "chunk".to_string()
                        } else {
                            event_type
                        },
                        data: StreamEventData::Chunk(StreamChunk {
                            content: content.to_string(),
                            is_final: false,
                            chunk_id: event_id.or_else(|| {
                                chunk_data
                                    .get("id")
                                    .and_then(|id| id.as_str())
                                    .map(|s| s.to_string())
                            }),
                        }),
                    });
                } else {
                    tracing::warn!("JSON数据中未找到内容字段: {:?}", chunk_data);
                }
            }
            Err(json_err) => {
                tracing::debug!("JSON解析失败: {}, 原始数据: {:?}", json_err, combined_data);

                // 如果JSON解析失败，尝试将原始数据作为纯文本内容
                if !combined_data.trim().is_empty() {
                    return Ok(StreamEvent {
                        event_type: if event_type.is_empty() {
                            "chunk".to_string()
                        } else {
                            event_type
                        },
                        data: StreamEventData::Chunk(StreamChunk {
                            content: combined_data,
                            is_final: false,
                            chunk_id: event_id,
                        }),
                    });
                }
            }
        }

        Err(AppError::QianwenApiError(format!(
            "无效的SSE数据格式，无法提取内容。原始数据: {:?}",
            chunk_str
        )))
    }

    /// 提取通义千问API的SSE内容
    /// 处理类似这样的格式: "id:1\nevent:result\n:HTTP_STATUS/200\n{\"output\":{\"text\":\"好的\"}}"
    fn extract_qianwen_sse_content(raw_data: &str) -> Option<String> {
        tracing::debug!("尝试提取通义千问SSE内容: {:?}", raw_data);

        // 查找JSON部分（通常在最后一行）
        let lines: Vec<&str> = raw_data.lines().collect();

        for line in lines.iter().rev() {
            let line = line.trim();

            // 跳过空行和特殊标记行
            if line.is_empty()
                || line.starts_with("id:")
                || line.starts_with("event:")
                || line.starts_with(":HTTP_STATUS")
            {
                continue;
            }

            // 尝试解析JSON
            if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(line) {
                tracing::debug!("成功解析通义千问JSON: {:?}", json_value);

                // 提取output.text字段
                if let Some(output) = json_value.get("output") {
                    if let Some(text) = output.get("text").and_then(|v| v.as_str()) {
                        if !text.is_empty() {
                            tracing::debug!("提取到文本内容: {:?}", text);
                            return Some(text.to_string());
                        }
                    }
                }

                // 尝试其他可能的字段
                if let Some(content) = Self::extract_content_from_json(&json_value) {
                    return Some(content);
                }
            }
        }

        None
    }

    /// 解析SSE数据块（实例方法）
    fn parse_sse_chunk(&self, chunk_str: &str) -> Result<StreamEvent, AppError> {
        Self::parse_sse_chunk_static(chunk_str)
    }

    /// 将响应文本分割成块
    fn split_response_into_chunks(&self, response: &str) -> Vec<String> {
        // 按句子或固定长度分割
        let mut chunks = Vec::new();
        let sentences: Vec<&str> = response
            .split(|c| c == '。' || c == '！' || c == '？' || c == '.' || c == '!' || c == '?')
            .collect();

        for sentence in sentences {
            if !sentence.trim().is_empty() {
                // 如果句子太长，按固定长度分割
                if sentence.len() > 50 {
                    for chunk in sentence.chars().collect::<Vec<_>>().chunks(20) {
                        let chunk_str: String = chunk.iter().collect();
                        if !chunk_str.trim().is_empty() {
                            chunks.push(chunk_str);
                        }
                    }
                } else {
                    chunks.push(sentence.to_string());
                }
            }
        }

        // 确保至少有一个块
        if chunks.is_empty() {
            chunks.push(response.to_string());
        }

        chunks
    }

    /// 检测消息是否包含旅行相关关键词
    fn detect_travel_keywords(&self, content: &str) -> bool {
        // 地名关键词（中国主要城市和景点）
        let location_keywords = [
            "北京",
            "上海",
            "广州",
            "深圳",
            "杭州",
            "南京",
            "苏州",
            "成都",
            "重庆",
            "西安",
            "武汉",
            "天津",
            "青岛",
            "大连",
            "厦门",
            "三亚",
            "丽江",
            "桂林",
            "张家界",
            "九寨沟",
            "黄山",
            "泰山",
            "华山",
            "峨眉山",
            "普陀山",
            "五台山",
            "天山",
            "长白山",
            "庐山",
            "故宫",
            "天安门",
            "长城",
            "颐和园",
            "天坛",
            "明十三陵",
            "兵马俑",
            "大雁塔",
            "西湖",
            "外滩",
            "东方明珠",
            "陆家嘴",
            "中山陵",
            "夫子庙",
            "拙政园",
            "留园",
            "都江堰",
            "乐山大佛",
            "武侯祠",
            "宽窄巷子",
            "洪崖洞",
            "解放碑",
            "磁器口",
            "鼓楼",
            "回民街",
            "华清池",
            "法门寺",
            "黄鹤楼",
            "东湖",
            "户部巷",
            "江汉路",
            "五大道",
            "古文化街",
            "意式风情区",
            "栈桥",
            "八大关",
            "崂山",
            "星海广场",
            "老虎滩",
            "金石滩",
            "鼓浪屿",
            "南普陀寺",
            "胡里山炮台",
            "亚龙湾",
            "天涯海角",
            "蜈支洲岛",
            "古城",
            "玉龙雪山",
            "泸沽湖",
            "象山",
            "漓江",
            "阳朔",
            "龙脊梯田",
            "金鞭溪",
            "天门山",
            "凤凰古城",
            "迎客松",
            "光明顶",
            "莲花峰",
            "玉皇顶",
            "红门",
            "中天门",
            "南天门",
            "华山论剑",
            "长空栈道",
            "金顶",
            "万佛顶",
            "普济寺",
            "法雨寺",
            "慧济寺",
            "显通寺",
            "塔院寺",
            "菩萨顶",
            "天池",
            "长白瀑布",
        ];

        // 旅行相关词汇
        let travel_keywords = [
            "旅游",
            "旅行",
            "游玩",
            "出游",
            "度假",
            "自由行",
            "跟团游",
            "自驾游",
            "背包游",
            "行程",
            "路线",
            "攻略",
            "景点",
            "景区",
            "名胜",
            "古迹",
            "遗址",
            "博物馆",
            "纪念馆",
            "公园",
            "广场",
            "街道",
            "商圈",
            "购物",
            "美食",
            "小吃",
            "餐厅",
            "酒店",
            "民宿",
            "住宿",
            "交通",
            "地铁",
            "公交",
            "出租车",
            "高铁",
            "飞机",
            "机场",
            "火车站",
            "汽车站",
            "码头",
            "港口",
            "门票",
            "预订",
            "导游",
            "向导",
            "地图",
            "导航",
            "拍照",
            "摄影",
            "风景",
            "日出",
            "日落",
            "夜景",
            "灯光",
            "建筑",
            "文化",
            "历史",
            "传统",
            "民俗",
            "节庆",
            "活动",
            "表演",
            "展览",
            "特产",
            "纪念品",
            "手信",
            "温泉",
            "海滩",
            "山峰",
            "湖泊",
            "河流",
            "瀑布",
            "森林",
            "草原",
            "沙漠",
            "雪山",
        ];

        // 位置相关查询词汇
        let location_query_keywords = [
            "附近",
            "周边",
            "周围",
            "附近的",
            "周边的",
            "周围的",
            "距离",
            "多远",
            "怎么去",
            "怎么走",
            "路线",
            "导航",
            "地址",
            "位置",
            "在哪",
            "在哪里",
            "哪里有",
            "哪儿有",
            "推荐",
            "好玩",
            "值得",
            "必去",
            "热门",
            "网红",
            "打卡",
            "拍照",
            "适合",
            "方便",
        ];

        // 检查是否包含任何关键词
        location_keywords
            .iter()
            .any(|&keyword| content.contains(keyword))
            || travel_keywords
                .iter()
                .any(|&keyword| content.contains(keyword))
            || location_query_keywords
                .iter()
                .any(|&keyword| content.contains(keyword))
    }

    /// 从消息内容中提取地理位置信息
    fn extract_location_from_content(&self, content: &str) -> Option<String> {
        // 使用正则表达式匹配常见的地理位置模式
        let location_patterns = [
            r"在(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)",
            r"去(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)",
            r"到(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)",
            r"从(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)",
            r"(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)(?:附近|周边|周围)",
            r"我在(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)",
            r"现在在(.{1,10}?)(?:市|区|县|镇|村|街|路|广场|公园|景区|景点)",
        ];

        for pattern in &location_patterns {
            if let Ok(re) = Regex::new(pattern) {
                if let Some(captures) = re.captures(content) {
                    if let Some(location) = captures.get(1) {
                        let location_str = location.as_str().trim();
                        if !location_str.is_empty() && location_str.len() <= 10 {
                            tracing::debug!("从内容中提取到位置: {}", location_str);
                            return Some(location_str.to_string());
                        }
                    }
                }
            }
        }

        None
    }

    /// 构建包含MCP地图数据的AI上下文
    async fn build_context_with_map_data(
        &self,
        messages: Vec<ChatMessage>,
    ) -> AppResult<Vec<ChatMessage>> {
        // 检查是否启用MCP集成
        if !self.config.enable_mcp_integration {
            tracing::debug!("MCP集成未启用，使用原始消息");
            return Ok(messages);
        }

        // 检查是否有MCP客户端
        let mcp_client = match &self.mcp_client {
            Some(client) => client,
            None => {
                tracing::debug!("MCP客户端未初始化，使用原始消息");
                return Ok(messages);
            }
        };

        // 获取最后一条用户消息
        let last_user_message = messages
            .iter()
            .rev()
            .find(|msg| msg.role == "user")
            .map(|msg| msg.get_text_content())
            .unwrap_or_default();

        // 检测是否包含旅行相关关键词
        if !self.detect_travel_keywords(&last_user_message) {
            tracing::debug!("未检测到旅行相关关键词，使用原始消息");
            return Ok(messages);
        }

        tracing::info!("检测到旅行相关查询，启用MCP地图数据增强");

        // 提取位置信息
        let location = self.extract_location_from_content(&last_user_message);

        // 获取MCP地图数据
        match mcp_client
            .get_map_data(&last_user_message, location.as_deref())
            .await
        {
            Ok(map_data) => {
                // 构建地图数据上下文
                let map_context = self.format_map_data_for_ai(&map_data);

                // 创建包含地图数据的系统消息
                let system_message = ChatMessage::text(
                    "system",
                    &format!(
                        "以下是实时地图数据，请结合这些信息为用户提供准确的旅行建议：\n\n{}",
                        map_context
                    ),
                );

                // 将系统消息插入到对话开头
                let mut enhanced_messages = vec![system_message];
                enhanced_messages.extend(messages);

                tracing::info!(
                    "已添加MCP地图数据到AI上下文，POI数量: {}",
                    map_data.pois.len()
                );
                Ok(enhanced_messages)
            }
            Err(e) => {
                if self.config.mcp_fallback_enabled {
                    tracing::warn!("MCP请求失败，启用降级模式: {}", e);
                    Ok(messages)
                } else {
                    tracing::error!("MCP请求失败且未启用降级模式: {}", e);
                    Err(e)
                }
            }
        }
    }

    /// 将MCP地图数据格式化为AI可理解的文本
    fn format_map_data_for_ai(&self, map_data: &AmapMapResult) -> String {
        let mut context = String::new();

        // 格式化POI信息
        if !map_data.pois.is_empty() {
            context.push_str("【附近景点和设施】\n");
            for (i, poi) in map_data.pois.iter().take(10).enumerate() {
                context.push_str(&format!(
                    "{}. {} - {} (类型: {}, 距离: {}米)\n",
                    i + 1,
                    poi.name,
                    poi.address,
                    poi.poi_type,
                    poi.distance.unwrap_or(0)
                ));
                if let Some(rating) = poi.rating {
                    context.push_str(&format!("   评分: {:.1}/5.0\n", rating));
                }
                if let Some(hours) = &poi.business_hours {
                    context.push_str(&format!("   营业时间: {}\n", hours));
                }
            }
            context.push('\n');
        }

        // 格式化路线信息
        if !map_data.routes.is_empty() {
            context.push_str("【推荐路线】\n");
            for (i, route) in map_data.routes.iter().take(3).enumerate() {
                context.push_str(&format!(
                    "{}. {} → {} (距离: {}米, 用时: {}分钟, 路况: {})\n",
                    i + 1,
                    route.origin,
                    route.destination,
                    route.distance,
                    route.duration / 60,
                    route.traffic_condition
                ));
            }
            context.push('\n');
        }

        // 格式化天气信息
        if let Some(weather) = &map_data.weather {
            context.push_str(&format!(
                "【当前天气】\n{}: {}°C, {}, 湿度{}%, 风力{}\n\n",
                weather.city, weather.temperature, weather.weather, weather.humidity, weather.wind
            ));
        }

        // 格式化交通信息
        if let Some(traffic) = &map_data.traffic {
            context.push_str(&format!(
                "【交通状况】\n路况: {}, 拥堵等级: {}/10, 预计延误: {}分钟\n\n",
                traffic.road_condition, traffic.congestion_level, traffic.estimated_delay
            ));
        }

        if context.is_empty() {
            context.push_str("暂无相关地图数据");
        }

        context
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json;

    /// 测试视觉内容数据结构序列化
    #[test]
    fn test_vision_content_serialization() {
        // 测试文本内容
        let text_content = VisionContent {
            content_type: "text".to_string(),
            data: VisionContentData::Text {
                text: "请分析这张图片".to_string(),
            },
        };

        let text_json = serde_json::to_string(&text_content).unwrap();
        assert!(text_json.contains("\"type\":\"text\""));
        assert!(text_json.contains("\"text\":\"请分析这张图片\""));

        // 测试图像URL内容
        let image_content = VisionContent {
            content_type: "image_url".to_string(),
            data: VisionContentData::ImageUrl {
                image_url: ImageUrl {
                    url: "https://example.com/image.jpg".to_string(),
                },
            },
        };

        let image_json = serde_json::to_string(&image_content).unwrap();
        assert!(image_json.contains("\"type\":\"image_url\""));
        assert!(image_json.contains("\"image_url\""));
        assert!(image_json.contains("\"url\":\"https://example.com/image.jpg\""));
    }

    /// 测试视觉请求结构序列化
    #[test]
    fn test_vision_request_serialization() {
        let request = VisionRequest {
            model: "qwen-vl-plus".to_string(),
            messages: vec![VisionMessage {
                role: "user".to_string(),
                content: vec![
                    VisionContent {
                        content_type: "text".to_string(),
                        data: VisionContentData::Text {
                            text: "请分析这张图片".to_string(),
                        },
                    },
                    VisionContent {
                        content_type: "image_url".to_string(),
                        data: VisionContentData::ImageUrl {
                            image_url: ImageUrl {
                                url: "https://example.com/image.jpg".to_string(),
                            },
                        },
                    },
                ],
            }],
            temperature: Some(0.1),
            max_tokens: Some(1500),
            top_p: Some(0.8),
        };

        let json = serde_json::to_string_pretty(&request).unwrap();
        println!("Vision Request JSON:\n{}", json);

        // 验证JSON结构
        assert!(json.contains("\"model\":\"qwen-vl-plus\""));
        assert!(json.contains("\"messages\""));
        assert!(json.contains("\"role\":\"user\""));
        assert!(json.contains("\"content\""));
        assert!(json.contains("\"type\":\"text\""));
        assert!(json.contains("\"type\":\"image_url\""));
        assert!(json.contains("\"temperature\":0.1"));
        assert!(json.contains("\"max_tokens\":1500"));
        assert!(json.contains("\"top_p\":0.8"));
    }

    /// 测试多模态ChatMessage创建和序列化
    #[test]
    fn test_multimodal_chat_message() {
        // 测试纯文本消息
        let text_msg = ChatMessage::text("user", "你好");
        assert_eq!(text_msg.role, "user");
        assert_eq!(text_msg.get_text_content(), "你好");
        assert!(!text_msg.has_image());

        let text_json = serde_json::to_string(&text_msg).unwrap();
        println!("Text Message JSON: {}", text_json);

        // 测试多模态消息
        let multimodal_msg =
            ChatMessage::multimodal("user", "请分析这张图片", "https://example.com/image.jpg");
        assert_eq!(multimodal_msg.role, "user");
        assert_eq!(multimodal_msg.get_text_content(), "请分析这张图片");
        assert!(multimodal_msg.has_image());

        let multimodal_json = serde_json::to_string(&multimodal_msg).unwrap();
        println!("Multimodal Message JSON: {}", multimodal_json);

        // 验证JSON结构
        assert!(multimodal_json.contains("\"role\":\"user\""));
        assert!(multimodal_json.contains("\"type\":\"text\""));
        assert!(multimodal_json.contains("\"type\":\"image_url\""));
        assert!(multimodal_json.contains("\"text\":\"请分析这张图片\""));
        assert!(multimodal_json.contains("\"url\":\"https://example.com/image.jpg\""));
    }

    /// 测试从前端多模态内容创建ChatMessage
    #[test]
    fn test_from_multimodal_content() {
        // 测试纯文本内容
        let text_content = serde_json::json!("你好，这是一条文本消息");
        let text_msg = ChatMessage::from_multimodal_content("user", text_content).unwrap();
        assert_eq!(text_msg.role, "user");
        assert_eq!(text_msg.get_text_content(), "你好，这是一条文本消息");
        assert!(!text_msg.has_image());

        // 测试多模态内容
        let multimodal_content = serde_json::json!([
            {
                "type": "text",
                "text": "请分析这张图片"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "https://example.com/test.jpg"
                }
            }
        ]);

        let multimodal_msg =
            ChatMessage::from_multimodal_content("user", multimodal_content).unwrap();
        assert_eq!(multimodal_msg.role, "user");
        assert_eq!(multimodal_msg.get_text_content(), "请分析这张图片");
        assert!(multimodal_msg.has_image());

        // 测试错误情况
        let invalid_content = serde_json::json!(123);
        let result = ChatMessage::from_multimodal_content("user", invalid_content);
        assert!(result.is_err());

        // 测试空数组
        let empty_content = serde_json::json!([]);
        let result = ChatMessage::from_multimodal_content("user", empty_content);
        assert!(result.is_err());

        // 测试无效的内容类型
        let invalid_type_content = serde_json::json!([
            {
                "type": "invalid_type",
                "data": "some data"
            }
        ]);
        let result = ChatMessage::from_multimodal_content("user", invalid_type_content);
        assert!(result.is_err());
    }

    /// 测试OpenAI格式转换
    #[test]
    fn test_openai_format_conversion() {
        use crate::utils::config::QianwenConfig;

        let config = QianwenConfig {
            api_key: "test_key".to_string(),
            base_url: "https://dashscope.aliyuncs.com".to_string(),
            model_text: "qwen-turbo".to_string(),
            model_vision: "qwen-vl-plus".to_string(),
            enable_mcp_integration: false,
            mcp_context_window: 8192,
            mcp_fallback_enabled: true,
        };

        let service = QianwenService::new(config).unwrap();

        // 测试文本消息转换
        let text_msg = ChatMessage::text("user", "你好");
        let text_openai = service.convert_to_openai_format(text_msg);

        assert_eq!(text_openai["role"], "user");
        assert_eq!(text_openai["content"], "你好");

        // 测试多模态消息转换
        let multimodal_msg =
            ChatMessage::multimodal("user", "请分析这张图片", "https://example.com/image.jpg");
        let multimodal_openai = service.convert_to_openai_format(multimodal_msg);

        assert_eq!(multimodal_openai["role"], "user");
        assert!(multimodal_openai["content"].is_array());

        let content_array = multimodal_openai["content"].as_array().unwrap();
        assert_eq!(content_array.len(), 2);

        // 验证文本部分
        let text_part = &content_array[0];
        assert_eq!(text_part["type"], "text");
        assert_eq!(text_part["text"], "请分析这张图片");

        // 验证图片部分
        let image_part = &content_array[1];
        assert_eq!(image_part["type"], "image_url");
        assert_eq!(
            image_part["image_url"]["url"],
            "https://example.com/image.jpg"
        );
    }
}
