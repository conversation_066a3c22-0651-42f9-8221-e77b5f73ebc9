// 数据库服务 - 数据库连接和基础操作
use sqlx::{Pool, Sqlite, SqlitePool};
use std::time::Duration;

use crate::{
    middleware::error::{AppError, AppResult},
    utils::config::Settings,
};

/// 数据库服务结构
#[derive(Debug, Clone)]
pub struct DatabaseService {
    pool: SqlitePool,
}

impl DatabaseService {
    /// 创建新的数据库服务实例
    pub async fn new(settings: &Settings) -> AppResult<Self> {
        let pool = create_connection_pool(&settings.database.url).await?;

        Ok(Self { pool })
    }

    /// 获取数据库连接池
    pub fn pool(&self) -> &SqlitePool {
        &self.pool
    }

    /// 运行数据库迁移
    pub async fn migrate(&self) -> AppResult<()> {
        sqlx::migrate!("./migrations")
            .run(&self.pool)
            .await
            .map_err(|e| AppError::InternalServerError(format!("数据库迁移失败: {}", e)))?;

        Ok(())
    }

    /// 检查数据库连接
    pub async fn health_check(&self) -> AppResult<()> {
        sqlx::query("SELECT 1")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database(e))?;

        Ok(())
    }

    /// 获取数据库统计信息
    pub async fn get_stats(&self) -> AppResult<DatabaseStats> {
        let user_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database(e))?;

        let conversation_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM conversations")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database(e))?;

        let message_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM messages")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database(e))?;

        let file_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM files")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database(e))?;

        Ok(DatabaseStats {
            user_count,
            conversation_count,
            message_count,
            file_count,
        })
    }

    /// 清理过期数据
    pub async fn cleanup_expired_data(&self) -> AppResult<CleanupResult> {
        let mut tx = self.pool.begin().await.map_err(|e| AppError::Database(e))?;

        // 清理过期文件
        let expired_files_result = sqlx::query(
            "DELETE FROM files WHERE expires_at IS NOT NULL AND expires_at < datetime('now')",
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| AppError::Database(e))?;
        let expired_files = expired_files_result.rows_affected() as i64;

        // 清理已删除的消息（超过30天）
        let deleted_messages_result = sqlx::query(
            "DELETE FROM messages WHERE is_deleted = true AND updated_at < datetime('now', '-30 days')"
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| AppError::Database(e))?;
        let deleted_messages = deleted_messages_result.rows_affected() as i64;

        tx.commit().await.map_err(|e| AppError::Database(e))?;

        Ok(CleanupResult {
            expired_files,
            deleted_messages,
        })
    }
}

/// 创建数据库连接池
async fn create_connection_pool(database_url: &str) -> AppResult<SqlitePool> {
    let pool = SqlitePool::connect_with(
        sqlx::sqlite::SqliteConnectOptions::new()
            .filename(database_url.trim_start_matches("sqlite:"))
            .create_if_missing(true)
            .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
            .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
            .busy_timeout(Duration::from_secs(30)),
    )
    .await
    .map_err(|e| AppError::Database(e))?;

    Ok(pool)
}

/// 数据库统计信息
#[derive(Debug, serde::Serialize)]
pub struct DatabaseStats {
    pub user_count: i64,
    pub conversation_count: i64,
    pub message_count: i64,
    pub file_count: i64,
}

/// 清理结果
#[derive(Debug, serde::Serialize)]
pub struct CleanupResult {
    pub expired_files: i64,
    pub deleted_messages: i64,
}

/// 分页参数
#[derive(Debug, Clone)]
pub struct PaginationParams {
    pub page: u32,
    pub per_page: u32,
}

impl PaginationParams {
    pub fn new(page: Option<u32>, per_page: Option<u32>) -> Self {
        Self {
            page: page.unwrap_or(1).max(1),
            per_page: per_page.unwrap_or(20).min(100).max(1),
        }
    }

    pub fn offset(&self) -> u32 {
        (self.page - 1) * self.per_page
    }

    pub fn limit(&self) -> u32 {
        self.per_page
    }
}

/// 排序参数
#[derive(Debug, Clone)]
pub struct SortParams {
    pub field: String,
    pub direction: SortDirection,
}

#[derive(Debug, Clone)]
pub enum SortDirection {
    Asc,
    Desc,
}

impl SortParams {
    pub fn new(field: Option<String>, direction: Option<String>) -> Self {
        let field = field.unwrap_or_else(|| "created_at".to_string());
        let direction = match direction.as_deref() {
            Some("asc") => SortDirection::Asc,
            _ => SortDirection::Desc,
        };

        Self { field, direction }
    }

    pub fn to_sql(&self) -> String {
        let direction = match self.direction {
            SortDirection::Asc => "ASC",
            SortDirection::Desc => "DESC",
        };
        format!("{} {}", self.field, direction)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pagination_params() {
        let params = PaginationParams::new(Some(2), Some(10));
        assert_eq!(params.page, 2);
        assert_eq!(params.per_page, 10);
        assert_eq!(params.offset(), 10);
        assert_eq!(params.limit(), 10);

        // 测试默认值
        let params = PaginationParams::new(None, None);
        assert_eq!(params.page, 1);
        assert_eq!(params.per_page, 20);

        // 测试边界值
        let params = PaginationParams::new(Some(0), Some(200));
        assert_eq!(params.page, 1); // 最小值为1
        assert_eq!(params.per_page, 100); // 最大值为100
    }

    #[test]
    fn test_sort_params() {
        let params = SortParams::new(Some("name".to_string()), Some("asc".to_string()));
        assert_eq!(params.field, "name");
        assert_eq!(params.to_sql(), "name ASC");

        let params = SortParams::new(None, None);
        assert_eq!(params.field, "created_at");
        assert_eq!(params.to_sql(), "created_at DESC");
    }
}
