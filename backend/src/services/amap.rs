// 高德地图API服务 - 提供地理位置、POI搜索、路线规划等功能
use crate::utils::config::AmapConfig;
use crate::utils::response::AppResult;
use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, error, info, warn};

/// 高德地图API服务
#[derive(Clone)]
pub struct AmapService {
    client: Client,
    config: AmapConfig,
}

/// 地理编码响应
#[derive(Debug, Deserialize)]
pub struct GeocodingResponse {
    pub status: String,
    pub info: String,
    pub infocode: String,
    pub count: String,
    pub geocodes: Vec<Geocode>,
}

/// 地理编码信息
#[derive(Debug, Deserialize, Serialize)]
pub struct Geocode {
    pub formatted_address: String,
    pub country: String,
    pub province: String,
    pub city: String,
    pub district: String,
    pub township: String,
    pub neighborhood: String,
    pub building: String,
    pub adcode: String,
    pub street: String,
    pub number: String,
    pub location: String,
    pub level: String,
}

/// 逆地理编码响应
#[derive(Debug, Deserialize)]
pub struct ReverseGeocodingResponse {
    pub status: String,
    pub info: String,
    pub infocode: String,
    pub regeocode: RegeocodeInfo,
}

/// 逆地理编码信息
#[derive(Debug, Deserialize, Serialize)]
pub struct RegeocodeInfo {
    pub formatted_address: String,
    pub addressComponent: AddressComponent,
    pub pois: Vec<PoiInfo>,
}

/// 地址组件
#[derive(Debug, Deserialize, Serialize)]
pub struct AddressComponent {
    pub country: String,
    pub province: String,
    pub city: String,
    pub district: String,
    pub township: String,
    pub neighborhood: String,
    pub building: String,
    pub adcode: String,
    pub streetNumber: StreetNumber,
}

/// 街道信息
#[derive(Debug, Deserialize, Serialize)]
pub struct StreetNumber {
    pub street: String,
    pub number: String,
    pub location: String,
    pub direction: String,
    pub distance: String,
}

/// POI搜索响应
#[derive(Debug, Deserialize)]
pub struct PoiSearchResponse {
    pub status: String,
    pub info: String,
    pub infocode: String,
    pub count: String,
    pub suggestion: Option<Suggestion>,
    pub pois: Vec<PoiInfo>,
}

/// 搜索建议
#[derive(Debug, Deserialize, Serialize)]
pub struct Suggestion {
    pub keywords: Vec<String>,
    pub cities: Vec<String>,
}

/// POI信息
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct PoiInfo {
    pub id: String,
    pub name: String,
    pub r#type: String,
    pub typecode: String,
    pub biz_type: String,
    pub address: String,
    pub location: String,
    pub distance: Option<String>,
    pub tel: Option<String>,
    pub postcode: Option<String>,
    pub website: Option<String>,
    pub email: Option<String>,
    pub pcode: String,
    pub pname: String,
    pub citycode: String,
    pub cityname: String,
    pub adcode: String,
    pub adname: String,
    pub importance: Option<String>,
    pub shopid: Option<String>,
    pub shopinfo: Option<String>,
    pub poiweight: Option<String>,
    pub gridcode: Option<String>,
    pub navi_poiid: Option<String>,
    pub entr_location: Option<String>,
    pub exit_location: Option<String>,
    pub tag: Option<String>,
    pub indoor_map: Option<String>,
    pub indoor_data: Option<String>,
    pub photos: Option<Vec<Photo>>,
    pub children: Option<Vec<PoiInfo>>,
    pub business: Option<Business>,
}

/// 照片信息
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct Photo {
    pub title: String,
    pub url: String,
}

/// 商业信息
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct Business {
    pub businessarea: String,
    pub opentime: String,
    pub opentimetext: String,
    pub rating: String,
    pub cost: String,
    pub atmosphere: String,
    pub service: String,
    pub tasting: String,
    pub environment: String,
    pub hygiene: String,
    pub technology: String,
    pub parking: String,
    pub indoor: String,
    pub seat: String,
    pub health: String,
    pub baby: String,
    pub card: String,
    pub meal: String,
    pub reservation: String,
    pub groupbuy: String,
    pub discount: String,
    pub special: String,
    pub feature: String,
    pub sell: String,
    pub around: String,
}

/// 路线规划响应
#[derive(Debug, Deserialize)]
pub struct DirectionResponse {
    pub status: String,
    pub info: String,
    pub infocode: String,
    pub count: String,
    pub route: Route,
}

/// 路线信息
#[derive(Debug, Deserialize, Serialize)]
pub struct Route {
    pub origin: String,
    pub destination: String,
    pub taxi_cost: Option<String>,
    pub paths: Vec<Path>,
}

/// 路径信息
#[derive(Debug, Deserialize, Serialize)]
pub struct Path {
    pub distance: String,
    pub duration: String,
    pub strategy: String,
    pub tolls: String,
    pub toll_distance: String,
    pub restriction: String,
    pub traffic_lights: String,
    pub steps: Vec<Step>,
}

/// 路径步骤
#[derive(Debug, Deserialize, Serialize)]
pub struct Step {
    pub instruction: String,
    pub orientation: String,
    pub distance: String,
    pub duration: String,
    pub polyline: String,
    pub action: String,
    pub assistant_action: String,
    pub road: String,
    pub toll_distance: String,
    pub toll_road: String,
    pub tolls: String,
}

/// 天气响应
#[derive(Debug, Deserialize)]
pub struct WeatherResponse {
    pub status: String,
    pub count: String,
    pub info: String,
    pub infocode: String,
    pub lives: Vec<WeatherLive>,
    pub forecasts: Vec<WeatherForecast>,
}

/// 实时天气
#[derive(Debug, Deserialize, Serialize)]
pub struct WeatherLive {
    pub province: String,
    pub city: String,
    pub adcode: String,
    pub weather: String,
    pub temperature: String,
    pub winddirection: String,
    pub windpower: String,
    pub humidity: String,
    pub reporttime: String,
    pub temperature_float: String,
    pub humidity_float: String,
}

/// 天气预报
#[derive(Debug, Deserialize, Serialize)]
pub struct WeatherForecast {
    pub city: String,
    pub adcode: String,
    pub province: String,
    pub reporttime: String,
    pub casts: Vec<WeatherCast>,
}

/// 天气预报详情
#[derive(Debug, Deserialize, Serialize)]
pub struct WeatherCast {
    pub date: String,
    pub week: String,
    pub dayweather: String,
    pub nightweather: String,
    pub daytemp: String,
    pub nighttemp: String,
    pub daywind: String,
    pub nightwind: String,
    pub daypower: String,
    pub nightpower: String,
    pub daytemp_float: String,
    pub nighttemp_float: String,
}

impl AmapService {
    /// 创建新的高德地图服务实例
    pub fn new(config: AmapConfig) -> Self {
        let client = Client::new();
        info!("🗺️ 高德地图API服务初始化完成");
        
        Self { client, config }
    }

    /// 地理编码 - 将地址转换为经纬度
    pub async fn geocoding(&self, address: &str) -> AppResult<Vec<Geocode>> {
        let url = format!("{}{}", self.config.base_url, self.config.geocoding_url);
        
        let mut params = HashMap::new();
        params.insert("key", self.config.api_key.as_str());
        params.insert("address", address);
        params.insert("output", "json");
        
        debug!("发送地理编码请求: {}", address);
        
        let response = self.client
            .get(&url)
            .query(&params)
            .send()
            .await?;
            
        if !response.status().is_success() {
            error!("地理编码API请求失败: {}", response.status());
            return Err(anyhow::anyhow!("地理编码API请求失败").into());
        }
        
        let geocoding_response: GeocodingResponse = response.json().await?;
        
        if geocoding_response.status != "1" {
            warn!("地理编码API返回错误: {}", geocoding_response.info);
            return Err(anyhow::anyhow!("地理编码失败: {}", geocoding_response.info).into());
        }
        
        info!("地理编码成功，找到 {} 个结果", geocoding_response.geocodes.len());
        Ok(geocoding_response.geocodes)
    }
}
