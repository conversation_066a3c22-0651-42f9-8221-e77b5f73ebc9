// JWT认证中间件 - 处理用户认证和授权
use axum::{
    extract::{Request, State},
    http::header::AUTHORIZATION,
    middleware::Next,
    response::Response,
};
use std::sync::Arc;

use crate::{
    middleware::error::{AppError, AppResult},
    utils::{config::Settings, crypto::JwtUtils},
};

/// 用户认证信息
#[derive(Debug, Clone)]
pub struct AuthUser {
    pub user_id: String,
    pub username: String,
}

/// JWT认证中间件
pub async fn jwt_auth_middleware(
    State(settings): State<Arc<Settings>>,
    mut request: Request,
    next: Next,
) -> AppResult<Response> {
    // 从请求头中提取Authorization
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let token = match auth_header {
        Some(header) => {
            if header.starts_with("Bearer ") {
                &header[7..] // 移除 "Bearer " 前缀
            } else {
                return Err(AppError::InvalidToken);
            }
        }
        None => return Err(AppError::InvalidToken),
    };

    // 验证JWT令牌
    let token_data = JwtUtils::verify_token(token, &settings.auth.jwt_secret)
        .map_err(|_| AppError::InvalidToken)?;

    // 检查令牌是否过期
    if token_data.claims.is_expired() {
        return Err(AppError::TokenExpired);
    }

    // 将用户信息添加到请求扩展中
    let auth_user = AuthUser {
        user_id: token_data.claims.sub,
        username: token_data.claims.username,
    };

    request.extensions_mut().insert(auth_user);

    Ok(next.run(request).await)
}

/// 可选的JWT认证中间件（不强制要求认证）
pub async fn optional_jwt_auth_middleware(
    State(settings): State<Arc<Settings>>,
    mut request: Request,
    next: Next,
) -> Response {
    // 尝试从请求头中提取Authorization
    if let Some(auth_header) = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
    {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            // 尝试验证JWT令牌
            if let Ok(token_data) = JwtUtils::verify_token(token, &settings.auth.jwt_secret) {
                if !token_data.claims.is_expired() {
                    // 将用户信息添加到请求扩展中
                    let auth_user = AuthUser {
                        user_id: token_data.claims.sub,
                        username: token_data.claims.username,
                    };
                    request.extensions_mut().insert(auth_user);
                }
            }
        }
    }

    next.run(request).await
}

/// 管理员权限检查中间件
pub async fn admin_auth_middleware(request: Request, next: Next) -> AppResult<Response> {
    // 获取用户认证信息
    let auth_user = request
        .extensions()
        .get::<AuthUser>()
        .ok_or(AppError::InvalidToken)?;

    // 这里可以添加管理员权限检查逻辑
    // 例如：检查用户角色、权限等
    // 目前简单示例：检查用户名是否为admin
    if auth_user.username != "admin" {
        return Err(AppError::InsufficientPermissions);
    }

    Ok(next.run(request).await)
}

/// 从请求中提取认证用户信息的辅助函数
pub fn extract_auth_user(request: &Request) -> AppResult<&AuthUser> {
    request
        .extensions()
        .get::<AuthUser>()
        .ok_or(AppError::InvalidToken)
}

/// 检查用户是否有权限访问特定资源
pub fn check_resource_permission(auth_user: &AuthUser, resource_owner_id: &str) -> AppResult<()> {
    // 用户只能访问自己的资源，或者是管理员
    if auth_user.user_id == resource_owner_id || auth_user.username == "admin" {
        Ok(())
    } else {
        Err(AppError::InsufficientPermissions)
    }
}

/// 令牌刷新检查中间件
pub async fn token_refresh_middleware(
    State(settings): State<Arc<Settings>>,
    request: Request,
    next: Next,
) -> Response {
    let mut response = next.run(request).await;

    // 检查请求头中的令牌是否需要刷新
    if let Some(auth_header) = response
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
    {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            // 检查令牌是否即将过期并刷新
            if let Ok(Some(new_token)) = JwtUtils::refresh_token_if_needed(
                token,
                &settings.auth.jwt_secret,
                settings.auth.jwt_expiration_hours,
            ) {
                // 在响应头中添加新令牌
                response
                    .headers_mut()
                    .insert("X-New-Token", new_token.parse().unwrap());
            }
        }
    }

    response
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::crypto::JwtUtils;

    #[tokio::test]
    async fn test_auth_user_extraction() {
        // 这里可以添加测试用例来验证认证用户信息提取
        let user_id = "test_user_id".to_string();
        let username = "test_user".to_string();

        let auth_user = AuthUser {
            user_id: user_id.clone(),
            username: username.clone(),
        };

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, username);
    }

    #[test]
    fn test_resource_permission_check() {
        let auth_user = AuthUser {
            user_id: "user123".to_string(),
            username: "testuser".to_string(),
        };

        // 用户访问自己的资源
        assert!(check_resource_permission(&auth_user, "user123").is_ok());

        // 用户访问他人的资源
        assert!(check_resource_permission(&auth_user, "user456").is_err());

        // 管理员访问任何资源
        let admin_user = AuthUser {
            user_id: "admin123".to_string(),
            username: "admin".to_string(),
        };
        assert!(check_resource_permission(&admin_user, "user456").is_ok());
    }

    #[test]
    fn test_jwt_token_generation_and_verification() {
        let secret = "test_secret";
        let user_id = "user123".to_string();
        let username = "testuser".to_string();

        // 生成令牌
        let token =
            JwtUtils::generate_token(user_id.clone(), username.clone(), secret, 24).unwrap();

        // 验证令牌
        let token_data = JwtUtils::verify_token(&token, secret).unwrap();
        assert_eq!(token_data.claims.sub, user_id);
        assert_eq!(token_data.claims.username, username);
        assert!(!token_data.claims.is_expired());
    }
}
