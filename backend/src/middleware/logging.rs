// 日志中间件 - 请求日志记录和性能监控
use axum::{
    extract::{MatchedPath, Request},
    middleware::Next,
    response::Response,
};
use std::time::Instant;
use tracing::{error, info, warn};
use uuid::Uuid;

/// 请求日志中间件
pub async fn request_logging_middleware(request: Request, next: Next) -> Response {
    let start_time = Instant::now();
    let request_id = Uuid::new_v4().to_string();

    // 提取请求信息
    let method = request.method().clone();
    let uri = request.uri().clone();
    let path = request
        .extensions()
        .get::<MatchedPath>()
        .map(|matched_path| matched_path.as_str().to_string())
        .unwrap_or_else(|| uri.path().to_string());

    // 获取用户代理和IP地址
    let user_agent = request
        .headers()
        .get("user-agent")
        .and_then(|header| header.to_str().ok())
        .unwrap_or("unknown")
        .to_string();

    let client_ip = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|header| header.to_str().ok())
        .or_else(|| {
            request
                .headers()
                .get("x-real-ip")
                .and_then(|header| header.to_str().ok())
        })
        .unwrap_or("unknown")
        .to_string();

    // 创建跟踪span
    let span = tracing::info_span!(
        "http_request",
        request_id = %request_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        user_agent = %user_agent
    );

    let _enter = span.enter();

    info!("开始处理请求: {} {} from {}", method, path, client_ip);

    // 处理请求
    let response = next.run(request).await;

    // 计算处理时间
    let duration = start_time.elapsed();
    let status = response.status();

    // 记录响应日志
    match status.as_u16() {
        200..=299 => {
            info!(
                "请求完成: {} {} - {} ({:.2}ms)",
                method,
                path,
                status,
                duration.as_millis()
            );
        }
        400..=499 => {
            warn!(
                "客户端错误: {} {} - {} ({:.2}ms)",
                method,
                path,
                status,
                duration.as_millis()
            );
        }
        500..=599 => {
            error!(
                "服务器错误: {} {} - {} ({:.2}ms)",
                method,
                path,
                status,
                duration.as_millis()
            );
        }
        _ => {
            info!(
                "请求完成: {} {} - {} ({:.2}ms)",
                method,
                path,
                status,
                duration.as_millis()
            );
        }
    }

    // 如果请求处理时间过长，记录警告
    if duration.as_millis() > 1000 {
        warn!(
            "慢请求警告: {} {} 耗时 {:.2}ms",
            method,
            path,
            duration.as_millis()
        );
    }

    response
}

/// 性能监控中间件
pub async fn performance_monitoring_middleware(request: Request, next: Next) -> Response {
    let start_time = Instant::now();
    let path = request.uri().path().to_string();
    let method = request.method().clone();

    let response = next.run(request).await;
    let duration = start_time.elapsed();

    // 记录性能指标
    tracing::info!(
        target: "performance",
        method = %method,
        path = %path,
        duration_ms = duration.as_millis(),
        status = response.status().as_u16(),
        "请求性能指标"
    );

    // 可以在这里添加更多性能监控逻辑
    // 例如：发送到监控系统、记录到数据库等

    response
}

/// 安全日志中间件
pub async fn security_logging_middleware(request: Request, next: Next) -> Response {
    let method = request.method().clone();
    let uri = request.uri().clone();
    let client_ip = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|header| header.to_str().ok())
        .or_else(|| {
            request
                .headers()
                .get("x-real-ip")
                .and_then(|header| header.to_str().ok())
        })
        .unwrap_or("unknown")
        .to_string();

    // 检查可疑请求模式
    let is_suspicious = check_suspicious_patterns(&uri.to_string(), &method.to_string());

    if is_suspicious {
        warn!(
            target: "security",
            client_ip = %client_ip,
            method = %method,
            uri = %uri,
            "检测到可疑请求"
        );
    }

    let response = next.run(request).await;

    // 记录认证失败
    if response.status() == 401 {
        warn!(
            target: "security",
            client_ip = %client_ip,
            method = %method,
            uri = %uri,
            "认证失败"
        );
    }

    // 记录权限不足
    if response.status() == 403 {
        warn!(
            target: "security",
            client_ip = %client_ip,
            method = %method,
            uri = %uri,
            "权限不足"
        );
    }

    response
}

/// 检查可疑请求模式
fn check_suspicious_patterns(uri: &str, method: &str) -> bool {
    // 检查SQL注入模式
    let sql_injection_patterns = [
        "union",
        "select",
        "insert",
        "delete",
        "update",
        "drop",
        "exec",
        "script",
        "alert",
        "javascript:",
        "data:",
        "' or '",
        "' and '",
        "or 1=1",
        "and 1=1",
        "<script>",
        "</script>",
    ];

    let uri_lower = uri.to_lowercase();
    for pattern in &sql_injection_patterns {
        if uri_lower.contains(pattern) {
            return true;
        }
    }

    // 检查路径遍历攻击
    if uri.contains("../") || uri.contains("..\\") {
        return true;
    }

    // 检查异常的HTTP方法
    if !["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"].contains(&method) {
        return true;
    }

    false
}

/// 初始化日志系统
pub fn init_logging(log_level: &str) {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    let log_level = match log_level.to_lowercase().as_str() {
        "trace" => tracing::Level::TRACE,
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::fmt::layer()
                .with_target(true)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true),
        )
        .with(
            tracing_subscriber::filter::Targets::new()
                .with_target("ai_chat_backend", log_level)
                .with_target("tower_http", tracing::Level::DEBUG)
                .with_target("axum", tracing::Level::DEBUG),
        )
        .init();
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_suspicious_pattern_detection() {
        // SQL注入检测
        assert!(check_suspicious_patterns(
            "/api/users?id=1' OR '1'='1",
            "GET"
        ));
        assert!(check_suspicious_patterns(
            "/api/search?q=<script>alert('xss')</script>",
            "GET"
        ));

        // 路径遍历检测
        assert!(check_suspicious_patterns("/api/../../../etc/passwd", "GET"));
        assert!(check_suspicious_patterns(
            "/api/..\\windows\\system32",
            "GET"
        ));

        // 正常请求
        assert!(!check_suspicious_patterns("/api/users", "GET"));
        assert!(!check_suspicious_patterns("/api/chat/messages", "POST"));

        // 异常HTTP方法
        assert!(check_suspicious_patterns("/api/users", "TRACE"));
        assert!(!check_suspicious_patterns("/api/users", "POST"));
    }

    #[test]
    fn test_log_level_parsing() {
        // 这里可以测试日志级别解析逻辑
        // 由于init_logging函数会初始化全局日志系统，我们在测试中不直接调用它
        assert!(true);
    }
}
