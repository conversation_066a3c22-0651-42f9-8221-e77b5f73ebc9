// 错误处理中间件 - 统一错误处理和响应
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use thiserror::Error;
use tracing::error;

use crate::utils::response::ApiResponse;

/// 应用错误类型
#[derive(Error, Debug)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),

    #[error("配置错误: {0}")]
    Config(#[from] config::ConfigError),

    #[error("JWT错误: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),

    #[error("密码哈希错误: {0}")]
    BcryptError(#[from] bcrypt::BcryptError),

    #[error("HTTP请求错误: {0}")]
    Reqwest(#[from] reqwest::Error),

    #[error("序列化错误: {0}")]
    SerdeJson(#[from] serde_json::Error),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("用户未找到")]
    UserNotFound,

    #[error("用户已存在")]
    UserAlreadyExists,

    #[error("资源未找到: {0}")]
    NotFound(String),

    #[error("密码错误")]
    InvalidPassword,

    #[error("令牌无效")]
    InvalidToken,

    #[error("令牌已过期")]
    TokenExpired,

    #[error("权限不足")]
    InsufficientPermissions,

    #[error("对话未找到")]
    ConversationNotFound,

    #[error("消息未找到")]
    MessageNotFound,

    #[error("文件未找到")]
    FileNotFound,

    #[error("文件类型不支持")]
    UnsupportedFileType,

    #[error("文件大小超出限制")]
    FileSizeExceeded,

    #[error("API调用失败: {0}")]
    ApiCallFailed(String),

    #[error("通义千问API错误: {0}")]
    QianwenApiError(String),

    #[error("语音服务错误: {0}")]
    SpeechServiceError(String),

    #[error("数据验证失败: {0}")]
    ValidationError(String),

    #[error("请求参数错误: {0}")]
    BadRequest(String),

    #[error("内部服务器错误: {0}")]
    InternalServerError(String),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_code, message) = match &self {
            AppError::Database(err) => {
                error!("数据库错误: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "DATABASE_ERROR",
                    "数据库操作失败",
                )
            }
            AppError::Config(err) => {
                error!("配置错误: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "CONFIG_ERROR",
                    "配置加载失败",
                )
            }
            AppError::Jwt(_) => (StatusCode::UNAUTHORIZED, "JWT_ERROR", "令牌验证失败"),
            AppError::BcryptError(err) => {
                error!("密码哈希错误: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "BCRYPT_ERROR",
                    "密码处理失败",
                )
            }
            AppError::Reqwest(err) => {
                error!("HTTP请求错误: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "HTTP_ERROR",
                    "外部服务请求失败",
                )
            }
            AppError::SerdeJson(err) => {
                error!("序列化错误: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "SERIALIZATION_ERROR",
                    "数据序列化失败",
                )
            }
            AppError::Io(err) => {
                error!("IO错误: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "IO_ERROR",
                    "文件操作失败",
                )
            }
            AppError::UserNotFound => (StatusCode::NOT_FOUND, "USER_NOT_FOUND", "用户未找到"),
            AppError::UserAlreadyExists => (StatusCode::CONFLICT, "USER_EXISTS", "用户已存在"),
            &AppError::NotFound(ref msg) => (StatusCode::NOT_FOUND, "NOT_FOUND", msg.as_str()),
            &AppError::BadRequest(ref msg) => {
                (StatusCode::BAD_REQUEST, "BAD_REQUEST", msg.as_str())
            }
            AppError::InvalidPassword => (StatusCode::UNAUTHORIZED, "INVALID_PASSWORD", "密码错误"),
            AppError::InvalidToken => (StatusCode::UNAUTHORIZED, "INVALID_TOKEN", "令牌无效"),
            AppError::TokenExpired => (StatusCode::UNAUTHORIZED, "TOKEN_EXPIRED", "令牌已过期"),
            AppError::InsufficientPermissions => (
                StatusCode::FORBIDDEN,
                "INSUFFICIENT_PERMISSIONS",
                "权限不足",
            ),
            AppError::ConversationNotFound => (
                StatusCode::NOT_FOUND,
                "CONVERSATION_NOT_FOUND",
                "对话未找到",
            ),
            AppError::MessageNotFound => (StatusCode::NOT_FOUND, "MESSAGE_NOT_FOUND", "消息未找到"),
            AppError::FileNotFound => (StatusCode::NOT_FOUND, "FILE_NOT_FOUND", "文件未找到"),
            AppError::UnsupportedFileType => (
                StatusCode::BAD_REQUEST,
                "UNSUPPORTED_FILE_TYPE",
                "文件类型不支持",
            ),
            AppError::FileSizeExceeded => (
                StatusCode::BAD_REQUEST,
                "FILE_SIZE_EXCEEDED",
                "文件大小超出限制",
            ),
            AppError::ApiCallFailed(msg) => {
                error!("API调用失败: {}", msg);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "API_CALL_FAILED",
                    "外部API调用失败",
                )
            }
            AppError::QianwenApiError(msg) => {
                error!("通义千问API错误: {}", msg);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "QIANWEN_API_ERROR",
                    "AI服务调用失败",
                )
            }
            AppError::SpeechServiceError(msg) => {
                error!("语音服务错误: {}", msg);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "SPEECH_SERVICE_ERROR",
                    "语音服务调用失败",
                )
            }
            AppError::ValidationError(msg) => {
                (StatusCode::BAD_REQUEST, "VALIDATION_ERROR", msg.as_str())
            }

            AppError::InternalServerError(msg) => {
                error!("内部服务器错误: {}", msg);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "INTERNAL_SERVER_ERROR",
                    "服务器内部错误",
                )
            }
        };

        let response = ApiResponse::error(error_code.to_string(), message.to_string());
        (status, Json(response)).into_response()
    }
}

/// 结果类型别名
pub type AppResult<T> = Result<T, AppError>;

/// 错误处理工具
pub struct ErrorHandler;

impl ErrorHandler {
    /// 处理数据库错误
    pub fn handle_database_error(err: sqlx::Error) -> AppError {
        match err {
            sqlx::Error::RowNotFound => AppError::UserNotFound,
            sqlx::Error::Database(db_err) => {
                if db_err.is_unique_violation() {
                    AppError::UserAlreadyExists
                } else {
                    AppError::Database(sqlx::Error::Database(db_err))
                }
            }
            _ => AppError::Database(err),
        }
    }

    /// 处理验证错误
    pub fn handle_validation_error(
        errors: std::collections::HashMap<String, Vec<String>>,
    ) -> AppError {
        let error_messages: Vec<String> = errors
            .into_iter()
            .flat_map(|(field, messages)| {
                messages
                    .into_iter()
                    .map(move |msg| format!("{}: {}", field, msg))
            })
            .collect();

        AppError::ValidationError(error_messages.join(", "))
    }

    /// 处理文件上传错误
    pub fn handle_file_upload_error(filename: &str, error: &str) -> AppError {
        error!("文件上传失败 {}: {}", filename, error);
        AppError::BadRequest(format!("文件上传失败: {}", error))
    }

    /// 处理外部API错误
    pub fn handle_external_api_error(service: &str, error: &str) -> AppError {
        error!("外部API调用失败 {}: {}", service, error);
        AppError::ApiCallFailed(format!("{}: {}", service, error))
    }
}

/// 全局错误处理中间件
pub async fn global_error_handler(err: AppError) -> Response {
    err.into_response()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_conversion() {
        let error = AppError::UserNotFound;
        let response = error.into_response();
        // 这里可以添加更多的测试来验证响应格式
    }

    #[test]
    fn test_validation_error_handling() {
        let mut errors = std::collections::HashMap::new();
        errors.insert("email".to_string(), vec!["邮箱格式不正确".to_string()]);
        errors.insert("password".to_string(), vec!["密码太短".to_string()]);

        let app_error = ErrorHandler::handle_validation_error(errors);
        match app_error {
            AppError::ValidationError(msg) => {
                assert!(msg.contains("email"));
                assert!(msg.contains("password"));
            }
            _ => panic!("Expected ValidationError"),
        }
    }
}
