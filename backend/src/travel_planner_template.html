<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能旅行规划表 - AI Travel Planner</title>
    
    <!-- Google Fonts - 中文和英文字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* CSS自定义属性 - Material Design 3主题 */
        :root {
            --primary-color: #1976D2;
            --secondary-color: #FFC107;
            --text-primary: #212121;
            --text-secondary: #757575;
            --background-light: #FAFAFA;
            --background-white: #FFFFFF;
            --border-color: #E0E0E0;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            
            /* 字体设置 */
            --font-chinese: 'Noto Sans SC', sans-serif;
            --font-english: 'Inter', sans-serif;
            --font-size-base: 14px;
            --font-size-small: 12px;
            --font-size-large: 16px;
            --font-size-title: 24px;
            
            /* 间距设置 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }

        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-chinese);
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--background-light);
        }

        /* A4页面容器 - 精确尺寸 */
        .page-container {
            width: 210mm;
            min-height: 297mm;
            margin: 20px auto;
            background: var(--background-white);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            padding: 10mm;
        }

        /* 网格布局系统 - 6行×4列 */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: auto auto 1fr auto auto auto;
            gap: var(--spacing-md);
            height: 277mm; /* 可打印区域高度 */
        }

        /* 标题区域 */
        .header-section {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, var(--primary-color), #1565C0);
            color: white;
            padding: var(--spacing-lg);
            border-radius: 12px;
            text-align: center;
            position: relative;
        }

        .header-title {
            font-size: var(--font-size-title);
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            font-family: var(--font-chinese);
        }

        .header-subtitle {
            font-size: var(--font-size-base);
            opacity: 0.9;
            font-weight: 300;
        }

        /* 天气信息卡片 */
        .weather-card {
            grid-column: 4;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
            text-align: center;
        }

        .weather-icon {
            font-size: 32px;
            color: var(--secondary-color);
            margin-bottom: var(--spacing-sm);
        }

        /* 行程概览 */
        .overview-section {
            grid-column: 1 / 3;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 交通总览 */
        .transport-overview {
            grid-column: 3 / -1;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 详细时间表 */
        .schedule-section {
            grid-column: 1 / 4;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
            overflow-y: auto;
        }

        /* 地图/路线区域 */
        .map-section {
            grid-column: 4;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
            text-align: center;
        }

        /* 住宿信息 */
        .accommodation-section {
            grid-column: 1 / 3;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 餐饮推荐 */
        .dining-section {
            grid-column: 3 / -1;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 实用信息 */
        .info-section {
            grid-column: 1 / 3;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 紧急联系 */
        .emergency-section {
            grid-column: 3 / -1;
            background: var(--background-white);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 预算摘要 */
        .budget-section {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
            border: 2px solid var(--success-color);
            border-radius: 8px;
            padding: var(--spacing-md);
        }

        /* 通用卡片标题 */
        .section-title {
            font-size: var(--font-size-large);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            font-size: 18px;
        }

        /* 时间表项目 */
        .schedule-item {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid var(--border-color);
        }

        .schedule-item:last-child {
            border-bottom: none;
        }

        .schedule-time {
            font-weight: 600;
            color: var(--primary-color);
            min-width: 80px;
            font-family: var(--font-english);
        }

        .schedule-content {
            flex: 1;
        }

        .schedule-activity {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
        }

        .schedule-location {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .schedule-price {
            font-size: var(--font-size-small);
            color: var(--success-color);
            font-weight: 500;
        }

        /* 预算图表 */
        .budget-chart {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .budget-item {
            flex: 1;
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--background-white);
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .budget-amount {
            font-size: var(--font-size-large);
            font-weight: 600;
            color: var(--primary-color);
            font-family: var(--font-english);
        }

        .budget-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
        }

        /* 控制按钮区域 */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: var(--spacing-sm);
            z-index: 1000;
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-small);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1565C0;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--background-white);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--background-light);
        }

        /* 二维码样式 */
        .qr-code {
            width: 80px;
            height: 80px;
            background: var(--background-light);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
                font-size: 12px;
            }
            
            .page-container {
                width: 210mm;
                height: 297mm;
                margin: 0;
                box-shadow: none;
                page-break-inside: avoid;
            }
            
            .controls {
                display: none;
            }
            
            .grid-container {
                height: 277mm;
            }
            
            /* 打印时转换为高对比度 */
            .header-section {
                background: var(--text-primary) !important;
                color: white !important;
            }
            
            .budget-section {
                background: #F5F5F5 !important;
                border-color: var(--text-secondary) !important;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-container {
                width: 100%;
                margin: 0;
                padding: var(--spacing-md);
            }
            
            .grid-container {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .header-section,
            .overview-section,
            .transport-overview,
            .schedule-section,
            .map-section,
            .accommodation-section,
            .dining-section,
            .info-section,
            .emergency-section,
            .budget-section {
                grid-column: 1;
            }
            
            .controls {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: var(--spacing-md);
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 控制按钮 -->
    <div class="controls">
        <button class="btn btn-primary" onclick="printPage()">
            <i class="fas fa-print"></i>
            打印规划表
        </button>
        <button class="btn btn-secondary" onclick="editMode()">
            <i class="fas fa-edit"></i>
            编辑模式
        </button>
        <button class="btn btn-secondary" onclick="exportPDF()">
            <i class="fas fa-file-pdf"></i>
            导出PDF
        </button>
    </div>

    <!-- A4页面容器 -->
    <div class="page-container">
        <div class="grid-container">
            <!-- 标题区域 -->
            <header class="header-section">
                <h1 class="header-title" id="destination">上海一日游规划表</h1>
                <p class="header-subtitle" id="travel-dates">2025年3月30日 | AI智能规划 | 实时更新</p>
            </header>

            <!-- 天气信息 -->
            <div class="weather-card">
                <div class="weather-icon">
                    <i class="fas fa-cloud" id="weather-icon"></i>
                </div>
                <div id="weather-temp">13°C/7°C</div>
                <div id="weather-condition">阴</div>
                <div style="font-size: 12px; color: #757575; margin-top: 8px;" id="weather-wind">东风1-3级</div>
            </div>

            <!-- 行程概览 -->
            <section class="overview-section">
                <h2 class="section-title">
                    <i class="fas fa-map-marked-alt"></i>
                    行程概览
                </h2>
                <div id="overview-content">
                    <p><strong>总行程：</strong>8小时</p>
                    <p><strong>景点数量：</strong>4个</p>
                    <p><strong>交通方式：</strong>地铁+步行</p>
                    <p><strong>预计费用：</strong>¥500</p>
                </div>
            </section>

            <!-- 交通总览 -->
            <section class="transport-overview">
                <h2 class="section-title">
                    <i class="fas fa-route"></i>
                    交通总览
                </h2>
                <div id="transport-content">
                    <p><strong>主要路线：</strong>地铁14号线</p>
                    <p><strong>总距离：</strong>约15公里</p>
                    <p><strong>交通费用：</strong>¥50</p>
                    <p><strong>实时路况：</strong>畅通</p>
                </div>
            </section>

            <!-- 详细时间表 -->
            <section class="schedule-section">
                <h2 class="section-title">
                    <i class="fas fa-clock"></i>
                    详细时间表
                </h2>
                <div id="schedule-list">
                    <!-- 时间表项目将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 地图/路线 -->
            <section class="map-section">
                <h2 class="section-title">
                    <i class="fas fa-map"></i>
                    路线图
                </h2>
                <div class="qr-code">
                    <i class="fas fa-qrcode"></i>
                </div>
                <p style="font-size: 12px; margin-top: 8px; color: #757575;">扫码查看详细地图</p>
            </section>

            <!-- 住宿信息 -->
            <section class="accommodation-section">
                <h2 class="section-title">
                    <i class="fas fa-bed"></i>
                    住宿信息
                </h2>
                <div id="accommodation-content">
                    <p><strong>酒店：</strong>上海外滩酒店</p>
                    <p><strong>地址：</strong>黄浦区中山东一路500号</p>
                    <p><strong>入住：</strong>15:00 | <strong>退房：</strong>12:00</p>
                </div>
            </section>

            <!-- 餐饮推荐 -->
            <section class="dining-section">
                <h2 class="section-title">
                    <i class="fas fa-utensils"></i>
                    餐饮推荐
                </h2>
                <div id="dining-content">
                    <p><strong>早餐：</strong>南翔小笼包</p>
                    <p><strong>午餐：</strong>老正兴菜馆</p>
                    <p><strong>晚餐：</strong>外滩茂悦大酒店</p>
                </div>
            </section>

            <!-- 实用信息 -->
            <section class="info-section">
                <h2 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    实用信息
                </h2>
                <div id="info-content">
                    <p><strong>最佳游览时间：</strong>9:00-17:00</p>
                    <p><strong>建议着装：</strong>休闲装+外套</p>
                    <p><strong>必带物品：</strong>身份证、充电宝</p>
                </div>
            </section>

            <!-- 紧急联系 -->
            <section class="emergency-section">
                <h2 class="section-title">
                    <i class="fas fa-phone-alt"></i>
                    紧急联系
                </h2>
                <div id="emergency-content">
                    <p><strong>报警电话：</strong>110</p>
                    <p><strong>医疗急救：</strong>120</p>
                    <p><strong>旅游热线：</strong>12301</p>
                </div>
            </section>

            <!-- 预算摘要 -->
            <section class="budget-section">
                <h2 class="section-title">
                    <i class="fas fa-calculator"></i>
                    预算摘要
                </h2>
                <div class="budget-chart" id="budget-chart">
                    <!-- 预算图表将通过JavaScript动态生成 -->
                </div>
            </section>
        </div>
    </div>

    <script>
        // 示例数据 - 兼容MCP服务数据格式
        const travelData = {
            destination: "上海一日游规划表",
            dates: {
                start: "2025-03-30",
                end: "2025-03-30"
            },
            weather: {
                temperature: "13°C/7°C",
                condition: "阴",
                wind: "东风1-3级",
                icon: "fa-cloud"
            },
            itinerary: [
                {
                    time: "09:00-11:00",
                    activity: "游览豫园",
                    location: "福佑路168号",
                    type: "attraction",
                    price: "40元",
                    notes: "提前预订门票"
                },
                {
                    time: "11:30-12:30",
                    activity: "南翔小笼包",
                    location: "豫园商城内",
                    type: "dining",
                    price: "60元",
                    notes: "排队较长"
                },
                {
                    time: "13:00-15:00",
                    activity: "外滩观光",
                    location: "中山东一路",
                    type: "attraction",
                    price: "免费",
                    notes: "最佳拍照时间"
                },
                {
                    time: "15:30-17:00",
                    activity: "东方明珠塔",
                    location: "世纪大道1号",
                    type: "attraction",
                    price: "180元",
                    notes: "建议提前购票"
                },
                {
                    time: "18:00-19:30",
                    activity: "黄浦江游船",
                    location: "外滩码头",
                    type: "activity",
                    price: "120元",
                    notes: "夜景最佳"
                }
            ],
            transportation: [
                {
                    from: "豫园",
                    to: "外滩",
                    method: "步行",
                    duration: "15分钟",
                    cost: "0元"
                },
                {
                    from: "外滩",
                    to: "东方明珠",
                    method: "地铁2号线",
                    duration: "10分钟",
                    cost: "4元"
                }
            ],
            accommodation: {
                name: "上海外滩酒店",
                address: "黄浦区中山东一路500号",
                checkin: "15:00",
                checkout: "12:00"
            },
            budget: {
                total: 500,
                breakdown: {
                    transport: 50,
                    food: 200,
                    attractions: 150,
                    shopping: 100
                }
            }
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderTravelPlan(travelData);
        });

        // 渲染旅行规划数据
        function renderTravelPlan(data) {
            // 更新标题和日期
            document.getElementById('destination').textContent = data.destination;
            document.getElementById('travel-dates').textContent = 
                `${data.dates.start} | AI智能规划 | 实时更新`;

            // 更新天气信息
            document.getElementById('weather-temp').textContent = data.weather.temperature;
            document.getElementById('weather-condition').textContent = data.weather.condition;
            document.getElementById('weather-wind').textContent = data.weather.wind;
            document.getElementById('weather-icon').className = `fas ${data.weather.icon}`;

            // 渲染时间表
            renderSchedule(data.itinerary);

            // 渲染预算图表
            renderBudgetChart(data.budget);

            // 保存到本地存储
            localStorage.setItem('travelPlan', JSON.stringify(data));
        }

        // 渲染时间表
        function renderSchedule(itinerary) {
            const scheduleList = document.getElementById('schedule-list');
            scheduleList.innerHTML = '';

            itinerary.forEach(item => {
                const scheduleItem = document.createElement('div');
                scheduleItem.className = 'schedule-item';
                
                const typeIcon = getActivityIcon(item.type);
                
                scheduleItem.innerHTML = `
                    <div class="schedule-time">${item.time}</div>
                    <div class="schedule-content">
                        <div class="schedule-activity">
                            <i class="${typeIcon}"></i> ${item.activity}
                        </div>
                        <div class="schedule-location">
                            <i class="fas fa-map-marker-alt"></i> ${item.location}
                        </div>
                        <div class="schedule-price">${item.price}</div>
                        ${item.notes ? `<div style="font-size: 12px; color: #FF9800; margin-top: 4px;">
                            <i class="fas fa-exclamation-triangle"></i> ${item.notes}
                        </div>` : ''}
                    </div>
                `;
                
                scheduleList.appendChild(scheduleItem);
            });
        }

        // 获取活动类型图标
        function getActivityIcon(type) {
            const icons = {
                'attraction': 'fas fa-camera',
                'dining': 'fas fa-utensils',
                'activity': 'fas fa-star',
                'shopping': 'fas fa-shopping-bag',
                'transport': 'fas fa-bus'
            };
            return icons[type] || 'fas fa-map-marker-alt';
        }

        // 渲染预算图表
        function renderBudgetChart(budget) {
            const budgetChart = document.getElementById('budget-chart');
            budgetChart.innerHTML = '';

            // 总预算
            const totalItem = document.createElement('div');
            totalItem.className = 'budget-item';
            totalItem.innerHTML = `
                <div class="budget-amount">¥${budget.total}</div>
                <div class="budget-label">总预算</div>
            `;
            budgetChart.appendChild(totalItem);

            // 各项预算
            Object.entries(budget.breakdown).forEach(([key, value]) => {
                const item = document.createElement('div');
                item.className = 'budget-item';
                
                const labels = {
                    transport: '交通',
                    food: '餐饮',
                    attractions: '景点',
                    shopping: '购物'
                };
                
                item.innerHTML = `
                    <div class="budget-amount">¥${value}</div>
                    <div class="budget-label">${labels[key]}</div>
                `;
                budgetChart.appendChild(item);
            });
        }

        // 打印功能
        function printPage() {
            window.print();
        }

        // 编辑模式
        function editMode() {
            alert('编辑模式功能开发中...\n将支持直接编辑规划内容');
        }

        // 导出PDF
        function exportPDF() {
            // 使用浏览器原生打印到PDF功能
            if (window.print) {
                alert('请在打印对话框中选择"另存为PDF"');
                window.print();
            } else {
                alert('您的浏览器不支持PDF导出功能');
            }
        }

        // 从AI聊天后端获取数据的API接口
        async function loadTravelDataFromAPI(conversationId) {
            try {
                const response = await fetch(`/api/travel/plan/${conversationId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    renderTravelPlan(data);
                } else {
                    console.error('Failed to load travel data:', response.statusText);
                }
            } catch (error) {
                console.error('Error loading travel data:', error);
            }
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'p':
                        e.preventDefault();
                        printPage();
                        break;
                    case 's':
                        e.preventDefault();
                        exportPDF();
                        break;
                }
            }
        });

        // 响应式字体大小调节
        function adjustFontSize(size) {
            document.documentElement.style.setProperty('--font-size-base', size + 'px');
            localStorage.setItem('fontSize', size);
        }

        // 加载保存的字体大小
        const savedFontSize = localStorage.getItem('fontSize');
        if (savedFontSize) {
            adjustFontSize(parseInt(savedFontSize));
        }
    </script>
</body>
</html>
