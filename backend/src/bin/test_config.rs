use ai_chat_backend::utils::config::Settings;

fn main() {
    println!("Testing configuration loading...");
    
    match Settings::new() {
        Ok(settings) => {
            println!("Configuration loaded successfully!");
            println!("Server: {}:{}", settings.server.host, settings.server.port);
            println!("Qianwen API Key: {}", if settings.qianwen.api_key.is_empty() { "Not set" } else { "Set" });
        }
        Err(e) => {
            eprintln!("Failed to load configuration: {}", e);
            std::process::exit(1);
        }
    }
}
