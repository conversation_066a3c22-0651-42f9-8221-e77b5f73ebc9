-- 初始数据库迁移
-- 创建用户、对话、消息和文件表

-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY NOT NULL,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    avatar_url TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- 对话表
CREATE TABLE conversations (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_archived BOOLEAN NOT NULL DEFAULT false,
    message_count INTEGER NOT NULL DEFAULT 0,
    last_message_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 文件表
CREATE TABLE files (
    id TEXT PRIMARY KEY NOT NULL,
    user_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_type TEXT NOT NULL CHECK (file_type IN ('image', 'audio', 'document', 'video', 'other')),
    mime_type TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    url TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'uploading' CHECK (status IN ('uploading', 'processing', 'ready', 'failed', 'deleted')),
    metadata TEXT, -- JSON格式的元数据
    checksum TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    expires_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 消息表
CREATE TABLE messages (
    id TEXT PRIMARY KEY NOT NULL,
    conversation_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    message_type TEXT NOT NULL CHECK (message_type IN ('text', 'image', 'audio', 'file')),
    content TEXT NOT NULL,
    metadata TEXT, -- JSON格式的元数据
    file_id TEXT,
    parent_message_id TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_message_id) REFERENCES messages(id) ON DELETE SET NULL
);

-- 创建索引以提高查询性能

-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_active ON users(is_active);

-- 对话表索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_conversations_updated_at ON conversations(updated_at);
CREATE INDEX idx_conversations_is_archived ON conversations(is_archived);
CREATE INDEX idx_conversations_last_message_at ON conversations(last_message_at);

-- 消息表索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_user_id ON messages(user_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_messages_message_type ON messages(message_type);
CREATE INDEX idx_messages_is_deleted ON messages(is_deleted);
CREATE INDEX idx_messages_file_id ON messages(file_id);
CREATE INDEX idx_messages_parent_message_id ON messages(parent_message_id);

-- 文件表索引
CREATE INDEX idx_files_user_id ON files(user_id);
CREATE INDEX idx_files_file_type ON files(file_type);
CREATE INDEX idx_files_status ON files(status);
CREATE INDEX idx_files_created_at ON files(created_at);
CREATE INDEX idx_files_expires_at ON files(expires_at);

-- 创建触发器来自动更新 updated_at 字段

-- 用户表触发器
CREATE TRIGGER update_users_updated_at
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 对话表触发器
CREATE TRIGGER update_conversations_updated_at
    AFTER UPDATE ON conversations
    FOR EACH ROW
BEGIN
    UPDATE conversations SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 消息表触发器
CREATE TRIGGER update_messages_updated_at
    AFTER UPDATE ON messages
    FOR EACH ROW
BEGIN
    UPDATE messages SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 文件表触发器
CREATE TRIGGER update_files_updated_at
    AFTER UPDATE ON files
    FOR EACH ROW
BEGIN
    UPDATE files SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 创建触发器来自动更新对话的消息统计

-- 当插入新消息时更新对话统计
CREATE TRIGGER update_conversation_stats_on_insert
    AFTER INSERT ON messages
    FOR EACH ROW
    WHEN NEW.is_deleted = false
BEGIN
    UPDATE conversations 
    SET 
        message_count = message_count + 1,
        last_message_at = NEW.created_at,
        updated_at = datetime('now')
    WHERE id = NEW.conversation_id;
END;

-- 当删除消息时更新对话统计
CREATE TRIGGER update_conversation_stats_on_delete
    AFTER UPDATE ON messages
    FOR EACH ROW
    WHEN OLD.is_deleted = false AND NEW.is_deleted = true
BEGIN
    UPDATE conversations 
    SET 
        message_count = message_count - 1,
        updated_at = datetime('now')
    WHERE id = NEW.conversation_id;
    
    -- 更新最后消息时间
    UPDATE conversations 
    SET last_message_at = (
        SELECT MAX(created_at) 
        FROM messages 
        WHERE conversation_id = NEW.conversation_id AND is_deleted = false
    )
    WHERE id = NEW.conversation_id;
END;

-- 当恢复消息时更新对话统计
CREATE TRIGGER update_conversation_stats_on_restore
    AFTER UPDATE ON messages
    FOR EACH ROW
    WHEN OLD.is_deleted = true AND NEW.is_deleted = false
BEGIN
    UPDATE conversations 
    SET 
        message_count = message_count + 1,
        last_message_at = NEW.created_at,
        updated_at = datetime('now')
    WHERE id = NEW.conversation_id;
END;
