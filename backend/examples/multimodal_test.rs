// 多模态聊天功能测试示例
use ai_chat_backend::services::qianwen::{ChatMessage, MessageContent, ContentPart, ContentData, ImageUrl};
use serde_json;

fn main() {
    println!("🧪 开始多模态聊天功能测试");

    // 测试1: 创建纯文本消息
    println!("\n📝 测试1: 纯文本消息");
    let text_msg = ChatMessage::text("user", "你好，世界！");
    println!("角色: {}", text_msg.role);
    println!("文本内容: {}", text_msg.get_text_content());
    println!("包含图片: {}", text_msg.has_image());
    
    let text_json = serde_json::to_string_pretty(&text_msg).unwrap();
    println!("JSON序列化:\n{}", text_json);

    // 测试2: 创建多模态消息
    println!("\n🖼️ 测试2: 多模态消息（文本+图片）");
    let multimodal_msg = ChatMessage::multimodal(
        "user", 
        "请分析这张图片的内容", 
        "https://example.com/sample.jpg"
    );
    println!("角色: {}", multimodal_msg.role);
    println!("文本内容: {}", multimodal_msg.get_text_content());
    println!("包含图片: {}", multimodal_msg.has_image());
    
    let multimodal_json = serde_json::to_string_pretty(&multimodal_msg).unwrap();
    println!("JSON序列化:\n{}", multimodal_json);

    // 测试3: 手动创建复杂多模态消息
    println!("\n🔧 测试3: 手动创建复杂多模态消息");
    let complex_msg = ChatMessage {
        role: "user".to_string(),
        content: MessageContent::Multimodal(vec![
            ContentPart {
                content_type: "text".to_string(),
                data: ContentData::Text {
                    text: "这是第一段文本".to_string(),
                },
            },
            ContentPart {
                content_type: "image_url".to_string(),
                data: ContentData::ImageUrl {
                    image_url: ImageUrl {
                        url: "https://example.com/image1.jpg".to_string(),
                    },
                },
            },
            ContentPart {
                content_type: "text".to_string(),
                data: ContentData::Text {
                    text: "这是第二段文本".to_string(),
                },
            },
            ContentPart {
                content_type: "image_url".to_string(),
                data: ContentData::ImageUrl {
                    image_url: ImageUrl {
                        url: "https://example.com/image2.jpg".to_string(),
                    },
                },
            },
        ]),
    };
    
    println!("角色: {}", complex_msg.role);
    println!("文本内容: {}", complex_msg.get_text_content());
    println!("包含图片: {}", complex_msg.has_image());
    
    let complex_json = serde_json::to_string_pretty(&complex_msg).unwrap();
    println!("JSON序列化:\n{}", complex_json);

    // 测试4: 从旧版本格式转换
    println!("\n🔄 测试4: 从旧版本格式转换");
    let legacy_msg = ChatMessage::from_legacy(
        "assistant".to_string(),
        "这是一个助手回复".to_string()
    );
    println!("角色: {}", legacy_msg.role);
    println!("文本内容: {}", legacy_msg.get_text_content());
    println!("包含图片: {}", legacy_msg.has_image());

    // 测试5: 序列化和反序列化
    println!("\n🔄 测试5: 序列化和反序列化");
    let original = ChatMessage::multimodal(
        "user",
        "请描述这张图片",
        "https://example.com/test.png"
    );
    
    let json_str = serde_json::to_string(&original).unwrap();
    println!("序列化: {}", json_str);
    
    let deserialized: ChatMessage = serde_json::from_str(&json_str).unwrap();
    println!("反序列化成功");
    println!("角色匹配: {}", original.role == deserialized.role);
    println!("文本内容匹配: {}", original.get_text_content() == deserialized.get_text_content());
    println!("图片状态匹配: {}", original.has_image() == deserialized.has_image());

    println!("\n✅ 所有测试完成！");
}
