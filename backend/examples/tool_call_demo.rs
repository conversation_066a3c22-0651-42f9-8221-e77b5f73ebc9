// 千问API工具调用演示示例
// 展示如何使用高德地图工具集成

use ai_chat_backend::models::chat::ChatMessage;
use ai_chat_backend::services::qianwen::{AmapTools, QianwenService};
use ai_chat_backend::utils::config::QianwenConfig;
use regex::Regex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::init();

    println!("🚀 千问API工具调用演示");
    println!("===================");

    // 1. 展示工具定义
    demo_tool_definitions();

    // 2. 演示工具调用检测
    demo_tool_detection().await?;

    // 3. 演示完整的聊天流程（需要真实的API密钥）
    // demo_full_chat_flow().await?;

    Ok(())
}

/// 演示工具定义
fn demo_tool_definitions() {
    println!("\n📋 高德地图工具定义:");
    println!("==================");

    let tools = AmapTools::get_all_tools();

    for (i, tool) in tools.iter().enumerate() {
        println!("{}. 工具名称: {}", i + 1, tool.function.name);
        println!("   描述: {}", tool.function.description);

        // 显示参数信息
        if let Some(properties) = tool.function.parameters.get("properties") {
            if let Some(props_obj) = properties.as_object() {
                println!("   参数:");
                for (param_name, param_def) in props_obj {
                    if let Some(desc) = param_def.get("description").and_then(|v| v.as_str()) {
                        println!("     - {}: {}", param_name, desc);
                    }
                }
            }
        }
        println!();
    }
}

/// 演示工具调用检测
async fn demo_tool_detection() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 工具调用检测演示:");
    println!("==================");

    // 创建测试配置
    let config = QianwenConfig {
        api_key: "test_key".to_string(),
        base_url: "https://dashscope.aliyuncs.com".to_string(),
        model_text: "qwen-plus".to_string(),
        model_vision: "qwen-vl-plus".to_string(),
        enable_mcp_integration: true,
        mcp_context_window: 8192,
        mcp_fallback_enabled: true,
    };

    let service = QianwenService::new(config)?;

    // 测试用例
    let test_cases = vec![
        ("北京附近有什么好玩的景点？", "POI搜索"),
        ("从上海到杭州怎么走？", "路线规划"),
        ("今天北京的天气怎么样？", "天气查询"),
        ("天安门广场的经纬度是多少？", "地理编码"),
        ("你好，今天是星期几？", "无工具调用"),
        ("帮我写一首诗", "无工具调用"),
    ];

    for (input, expected) in test_cases {
        let messages = vec![ChatMessage::text("user", input)];
        let should_use_tools = service.should_enable_tools(&messages);

        println!("输入: \"{}\"", input);
        println!(
            "预期: {} | 实际: {}",
            expected,
            if should_use_tools {
                "启用工具调用"
            } else {
                "无工具调用"
            }
        );

        // 如果启用工具调用，展示解析的工具调用
        if should_use_tools {
            if let Some(tool_calls) = service.parse_tool_calls_from_text(input) {
                for tool_call in &tool_calls {
                    println!("  -> 工具: {}", tool_call.function.name);
                    println!("  -> 参数: {}", tool_call.function.arguments);
                }
            }
        }
        println!();
    }

    Ok(())
}

/// 演示完整的聊天流程（需要真实的API密钥）
#[allow(dead_code)]
async fn demo_full_chat_flow() -> Result<(), Box<dyn std::error::Error>> {
    println!("💬 完整聊天流程演示:");
    println!("==================");

    // 注意：这需要真实的API密钥
    let config = QianwenConfig {
        api_key: std::env::var("QIANWEN_API_KEY")
            .unwrap_or_else(|_| "your_api_key_here".to_string()),
        base_url: "https://dashscope.aliyuncs.com".to_string(),
        model_text: "qwen-plus".to_string(),
        model_vision: "qwen-vl-plus".to_string(),
        enable_mcp_integration: true,
        mcp_context_window: 8192,
        mcp_fallback_enabled: true,
    };

    let service = QianwenService::new(config)?;

    // 测试消息
    let messages = vec![ChatMessage::text("user", "北京附近有什么好吃的餐厅推荐？")];

    println!("用户输入: {}", messages[0].get_text_content());

    match service.chat_multimodal(messages).await {
        Ok(response) => {
            println!("AI回复: {}", response);
        }
        Err(e) => {
            println!("错误: {}", e);
        }
    }

    Ok(())
}

/// 演示工具调用JSON格式
#[allow(dead_code)]
fn demo_tool_call_json() {
    println!("📄 工具调用JSON格式演示:");
    println!("========================");

    let tools = AmapTools::get_all_tools();

    // 序列化为JSON并打印
    for tool in tools.iter().take(2) {
        // 只显示前两个工具
        match serde_json::to_string_pretty(tool) {
            Ok(json) => {
                println!("工具: {}", tool.function.name);
                println!("JSON格式:");
                println!("{}", json);
                println!();
            }
            Err(e) => {
                println!("JSON序列化失败: {}", e);
            }
        }
    }
}
