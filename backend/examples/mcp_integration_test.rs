// MCP集成功能测试示例
use ai_chat_backend::{
    services::qianwen::{QianwenService, ChatMessage},
    utils::config::{<PERSON><PERSON>wenConfig, AmapConfig},
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🧪 开始MCP集成功能测试");

    // 创建测试配置
    let qianwen_config = QianwenConfig {
        api_key: "sk-130c858aa2a345949409d91ff45e0367".to_string(),
        base_url: "https://dashscope.aliyuncs.com".to_string(),
        model_text: "qwen-plus".to_string(),
        model_vision: "qwen-vl-plus".to_string(),
        enable_mcp_integration: true,
        mcp_context_window: 8192,
        mcp_fallback_enabled: true,
    };

    let amap_config = AmapConfig {
        api_key: "039c47c81bc9eb2c1e38df53260191e0".to_string(),
        base_url: "https://restapi.amap.com".to_string(),
        geocoding_url: "/v3/geocode/geo".to_string(),
        reverse_geocoding_url: "/v3/geocode/regeo".to_string(),
        poi_search_url: "/v3/place/text".to_string(),
        poi_around_url: "/v3/place/around".to_string(),
        direction_driving_url: "/v3/direction/driving".to_string(),
        direction_walking_url: "/v3/direction/walking".to_string(),
        direction_transit_url: "/v3/direction/transit".to_string(),
        weather_url: "/v3/weather/weatherInfo".to_string(),
        mcp_enabled: true,
        mcp_server_url: "https://mcp.amap.com/sse".to_string(),
        mcp_api_key: "039c47c81bc9eb2c1e38df53260191e0".to_string(),
        mcp_max_connections: 100,
        mcp_timeout_seconds: 30,
        mcp_retry_attempts: 3,
        mcp_enable_caching: true,
        mcp_cache_ttl_seconds: 300,
    };

    // 创建带MCP支持的通义千问服务
    let qianwen_service = QianwenService::new_with_amap(qianwen_config, amap_config)?;

    println!("✅ 通义千问服务（带MCP支持）初始化成功");

    // 测试用例1: 旅行规划查询
    println!("\n📍 测试用例1: 旅行规划查询");
    let travel_query = vec![ChatMessage::text(
        "user",
        "我想在北京游玩3天，喜欢历史文化和美食，请帮我规划详细行程"
    )];

    match qianwen_service.chat_multimodal(travel_query).await {
        Ok(response) => {
            println!("✅ 旅行规划查询成功");
            println!("📝 AI回复: {}", response);
        }
        Err(e) => {
            println!("❌ 旅行规划查询失败: {}", e);
        }
    }

    // 测试用例2: 位置相关查询
    println!("\n📍 测试用例2: 位置相关查询");
    let location_query = vec![ChatMessage::text(
        "user",
        "我现在在天安门广场，附近有什么值得参观的地方？考虑步行距离"
    )];

    match qianwen_service.chat_multimodal(location_query).await {
        Ok(response) => {
            println!("✅ 位置查询成功");
            println!("📝 AI回复: {}", response);
        }
        Err(e) => {
            println!("❌ 位置查询失败: {}", e);
        }
    }

    // 测试用例3: 非旅行相关查询（应该不触发MCP）
    println!("\n📍 测试用例3: 非旅行相关查询");
    let general_query = vec![ChatMessage::text(
        "user",
        "请解释一下什么是人工智能？"
    )];

    match qianwen_service.chat_multimodal(general_query).await {
        Ok(response) => {
            println!("✅ 一般查询成功");
            println!("📝 AI回复: {}", response);
        }
        Err(e) => {
            println!("❌ 一般查询失败: {}", e);
        }
    }

    // 测试用例4: 关键词检测功能
    println!("\n📍 测试用例4: 关键词检测功能");
    test_keyword_detection(&qianwen_service);

    println!("\n🎉 MCP集成功能测试完成");

    Ok(())
}

fn test_keyword_detection(service: &QianwenService) {
    let test_cases = vec![
        ("我想去北京旅游", true, "包含地名和旅行词汇"),
        ("天安门附近有什么好玩的", true, "包含地名和位置查询"),
        ("推荐一些上海的美食", true, "包含地名和旅行相关词汇"),
        ("怎么去西湖", true, "包含地名和位置查询"),
        ("今天天气怎么样", false, "不包含旅行相关词汇"),
        ("什么是人工智能", false, "不包含旅行相关词汇"),
        ("我在杭州西湖边", true, "包含地名和位置信息"),
        ("故宫的开放时间", true, "包含景点名称"),
    ];

    for (query, expected, description) in test_cases {
        // 注意：这里我们无法直接调用私有方法，所以这只是一个示例
        // 在实际测试中，您可能需要将关键词检测方法设为公开或创建专门的测试方法
        println!("🔍 测试查询: \"{}\" - {}", query, description);
        println!("   预期结果: {}", if expected { "触发MCP" } else { "不触发MCP" });
    }
}
