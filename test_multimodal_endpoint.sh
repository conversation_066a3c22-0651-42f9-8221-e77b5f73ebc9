#!/bin/bash

echo "🧪 测试多模态聊天端点"

BASE_URL="http://127.0.0.1:8080"

echo "1. 测试健康检查..."
curl -s "$BASE_URL/health" && echo

echo -e "\n2. 测试纯文本消息..."
curl -X POST "$BASE_URL/api/chat/multimodal" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "你好，请测试多模态端点",
    "stream": false
  }' | jq . && echo

echo -e "\n3. 测试多模态消息（文本+图片）..."
curl -X POST "$BASE_URL/api/chat/multimodal" \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请分析这张图片的内容"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ],
    "stream": false
  }' | jq . && echo

echo "✅ 测试完成"
