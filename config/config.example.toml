# AI聊天应用配置文件示例
# 复制此文件为 config.toml 并填入实际值

[server]
# 服务器配置
host = "127.0.0.1"
port = 8080
# 静态文件目录
static_dir = "uploads"

[database]
# 数据库配置
url = "sqlite:./ai_chat.db"
# 如果使用PostgreSQL，取消注释下面的行
# url = "postgresql://username:password@localhost/ai_chat"

[qianwen]
# 通义千问API配置
api_key = "your_qianwen_api_key_here"
base_url = "https://dashscope.aliyuncs.com/api/v1"
model_text = "qwen-turbo"
model_vision = "qwen-vl-plus"

[auth]
# JWT认证配置
jwt_secret = "your_jwt_secret_key_here_change_in_production"
jwt_expiration_hours = 24

[upload]
# 文件上传配置
max_file_size = 10485760  # 10MB
allowed_image_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
allowed_audio_types = ["audio/mpeg", "audio/wav", "audio/ogg", "audio/m4a"]
upload_dir = "uploads"

[speech]
# 语音服务配置（可选，如果使用第三方语音服务）
# stt_provider = "azure"  # azure, google, baidu等
# tts_provider = "azure"
# stt_api_key = "your_stt_api_key"
# tts_api_key = "your_tts_api_key"

[logging]
# 日志配置
level = "info"
# 可选值: trace, debug, info, warn, error
