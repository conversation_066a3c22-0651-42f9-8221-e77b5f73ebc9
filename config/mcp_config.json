{"mcpServers": {"amap-amap-sse": {"url": "https://mcp.amap.com/sse?key=039c47c81bc9eb2c1e38df53260191e0", "description": "高德地图MCP服务器 - 提供实时地图数据和AI增强功能", "features": ["real_time_location_data", "poi_recommendations", "route_planning", "traffic_updates", "weather_integration"], "config": {"max_connections": 100, "timeout_seconds": 30, "retry_attempts": 3, "enable_caching": true, "cache_ttl_seconds": 300}}}, "integration": {"ai_chat_backend": {"enable_mcp": true, "mcp_context_window": 8192, "fallback_to_traditional_api": true, "supported_models": ["qwen-plus", "qwen-vl-plus"]}}, "travel_guide_features": {"intelligent_itinerary_planning": {"enabled": true, "description": "基于MCP数据的智能行程规划"}, "real_time_recommendations": {"enabled": true, "description": "实时位置推荐和导航建议"}, "multimodal_location_analysis": {"enabled": true, "description": "结合图片和文本的位置识别"}, "traffic_aware_routing": {"enabled": true, "description": "考虑实时交通的路线规划"}}, "api_endpoints": {"mcp_health_check": "/api/health/mcp", "mcp_status": "/api/mcp/status", "travel_guide_chat": "/api/chat/travel-guide", "location_analysis": "/api/chat/location-analysis"}, "error_handling": {"connection_timeout": 30, "max_retry_attempts": 3, "fallback_enabled": true, "error_codes": {"MCP_CONNECTION_ERROR": "MCP服务器连接失败", "MCP_PROTOCOL_ERROR": "MCP协议错误", "AMAP_MCP_ERROR": "高德地图MCP服务错误", "LOCATION_SERVICE_ERROR": "位置服务错误"}}, "performance": {"connection_pooling": true, "request_batching": true, "response_caching": true, "metrics_collection": true}}