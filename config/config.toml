# AI聊天应用配置文件
# 请根据实际环境修改以下配置

[server]
# 服务器配置
host = "127.0.0.1"
port = 8080
# 静态文件目录
static_dir = "uploads"

[database]
# 数据库配置 - 使用SQLite作为默认数据库
url = "sqlite:./ai_chat.db"
# 如果使用PostgreSQL，取消注释下面的行
# url = "postgresql://username:password@localhost/ai_chat"

[qianwen]
# 通义千问API配置
# 请在阿里云控制台获取API密钥
api_key = "sk-130c858aa2a345949409d91ff45e0367"
base_url = "https://dashscope.aliyuncs.com"
model_text = "qwen-plus"
model_vision = "qwen-vl-plus"

# MCP集成配置（新增）
enable_mcp_integration = true
mcp_context_window = 8192     # MCP上下文窗口大小
mcp_fallback_enabled = true   # MCP失败时是否降级到传统API

[auth]
# JWT认证配置
jwt_secret = "your_jwt_secret_key_here_change_in_production"
jwt_expiration_hours = 24

[upload]
# 文件上传配置
max_file_size = 10485760                                                     # 10MB
allowed_image_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
allowed_audio_types = ["audio/mpeg", "audio/wav", "audio/ogg", "audio/m4a"]
upload_dir = "uploads"

[speech]
# 语音服务配置（可选，如果使用第三方语音服务）
# stt_provider = "azure"  # azure, google, baidu等
# tts_provider = "azure"
# stt_api_key = "your_stt_api_key"
# tts_api_key = "your_tts_api_key"

[amap]
# 高德地图API配置
api_key = "039c47c81bc9eb2c1e38df53260191e0"
base_url = "https://restapi.amap.com"
# 地理编码API
geocoding_url = "/v3/geocode/geo"
reverse_geocoding_url = "/v3/geocode/regeo"
# POI搜索API
poi_search_url = "/v3/place/text"
poi_around_url = "/v3/place/around"
# 路线规划API
direction_driving_url = "/v3/direction/driving"
direction_walking_url = "/v3/direction/walking"
direction_transit_url = "/v3/direction/transit"
# 天气查询API
weather_url = "/v3/weather/weatherInfo"

# MCP服务配置（新增）
mcp_enabled = true
mcp_server_url = "https://mcp.amap.com/sse"
mcp_api_key = "039c47c81bc9eb2c1e38df53260191e0" # 与传统API使用相同密钥
mcp_max_connections = 100
mcp_timeout_seconds = 30
mcp_retry_attempts = 3
mcp_enable_caching = true
mcp_cache_ttl_seconds = 300

[logging]
# 日志配置
level = "info"
# 可选值: trace, debug, info, warn, error
