# 后端开发指南

## 项目结构

```
backend/
├── src/
│   ├── handlers/          # HTTP请求处理器
│   │   ├── auth.rs       # 认证相关
│   │   ├── chat.rs       # 聊天功能
│   │   ├── upload.rs     # 文件上传
│   │   └── user.rs       # 用户管理
│   ├── models/           # 数据模型
│   │   ├── user.rs       # 用户模型
│   │   ├── conversation.rs # 对话模型
│   │   ├── message.rs    # 消息模型
│   │   └── file.rs       # 文件模型
│   ├── services/         # 业务服务
│   │   ├── qianwen.rs    # 通义千问API
│   │   ├── database.rs   # 数据库服务
│   │   ├── auth.rs       # 认证服务
│   │   ├── file.rs       # 文件服务
│   │   └── speech.rs     # 语音服务
│   ├── middleware/       # 中间件
│   │   ├── auth.rs       # JWT认证
│   │   ├── cors.rs       # CORS处理
│   │   ├── error.rs      # 错误处理
│   │   └── logging.rs    # 日志记录
│   ├── utils/           # 工具函数
│   │   ├── config.rs     # 配置管理
│   │   ├── validation.rs # 数据验证
│   │   ├── crypto.rs     # 加密工具
│   │   └── response.rs   # 响应格式化
│   └── main.rs          # 应用入口
├── Cargo.toml           # 依赖配置
└── migrations/          # 数据库迁移
```

## 开发环境设置

### 1. 安装依赖
```bash
cd backend
cargo build
```

### 2. 配置文件
复制配置模板：
```bash
cp ../config/config.example.toml ../config/config.toml
```

编辑配置文件，填入实际的API密钥和数据库连接信息。

### 3. 数据库设置
```bash
# 创建数据库
sqlx database create

# 运行迁移
sqlx migrate run
```

### 4. 启动服务器
```bash
# 直接运行（使用默认二进制配置）
cargo run

# 或者明确指定二进制名称
cargo run --bin ai_chat_backend

# 开发模式（带详细日志）
RUST_LOG=debug cargo run
```

**注意：** 项目已配置默认运行目标，`cargo run` 会自动启动 `ai_chat_backend` 二进制文件。

## 核心组件

### 1. Axum Web框架
- 路由定义
- 中间件集成
- 请求/响应处理

### 2. SQLx数据库
- 异步数据库操作
- 编译时SQL检查
- 连接池管理

### 3. 通义千问API集成
- HTTP客户端配置
- OpenAI兼容格式支持
- 多模态消息处理（qwen-vl-plus）
- 文本消息处理（qwen-turbo）
- 流式响应支持
- 请求/响应处理
- 错误处理和重试
- 智能模型选择

### 4. JWT认证
- 令牌生成和验证
- 中间件保护
- 用户会话管理

## 开发规范

### 1. 错误处理
使用`anyhow`和`thiserror`进行错误处理：
```rust
use anyhow::Result;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ApiError {
    #[error("用户未找到")]
    UserNotFound,
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),
}
```

### 2. 日志记录
使用`tracing`进行结构化日志：
```rust
use tracing::{info, warn, error};

info!("用户登录成功: {}", user_id);
warn!("API调用频率过高: {}", user_id);
error!("数据库连接失败: {}", error);
```

### 3. 配置管理
使用`config`crate管理配置：
```rust
use config::{Config, ConfigError};

#[derive(Debug, Deserialize)]
pub struct Settings {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub qianwen: QianwenConfig,
}
```

## 测试

### 单元测试
```bash
cargo test
```

### 集成测试
```bash
cargo test --test integration
```

### API测试
使用curl或Postman测试API端点。

#### 多模态聊天测试
```bash
# 测试文本消息
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "你好", "stream": false}'

# 测试多模态消息
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": [
      {"type": "text", "text": "请分析这张图片"},
      {"type": "image_url", "image_url": {"url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"}}
    ]
  }'

# 测试流式响应
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "请写一首诗", "stream": true}'
```

#### 健康检查测试
```bash
# 基础健康检查
curl http://127.0.0.1:8080/api/health

# AI服务健康检查
curl http://127.0.0.1:8080/api/health/ai

# 存储服务健康检查
curl http://127.0.0.1:8080/api/health/storage
```
