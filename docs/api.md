# AI聊天应用 API 文档

## 概述

本文档描述了AI聊天应用的后端API接口。后端使用Rust + Axum框架构建，提供RESTful API和Server-Sent Events (SSE)流式响应，集成阿里云通义千问AI服务。

## 基础信息

- **基础URL**: `http://127.0.0.1:8080`
- **API版本**: v1
- **内容类型**: `application/json`
- **字符编码**: UTF-8
- **AI服务**: 阿里云通义千问API
- **存储模式**: 内存存储（开发阶段）

## 认证

目前处于开发阶段，暂时跳过认证。生产环境将使用JWT令牌认证。

```http
Authorization: Bearer <jwt_token>
```

## 通用响应格式

所有API响应都遵循统一的格式：

### 成功响应
```json
{
  "success": true,
  "data": <响应数据>,
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": <详细错误信息>
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源未找到
- `422` - 验证错误
- `429` - 请求频率超限
- `500` - 服务器内部错误
- `502` - AI服务错误
- `503` - 服务不可用

## API端点

### 1. 健康检查

#### GET /
基础健康检查

**响应示例:**
```
AI Chat Backend is running! 🤖
```

#### GET /api/health
API健康检查

**响应示例:**
```
AI Chat Backend is running! 🤖
```

#### GET /api/health/storage
存储服务健康检查

**响应示例:**
```json
{
  "status": "healthy",
  "storage_type": "memory",
  "mode": "development",
  "stats": {
    "users": 1,
    "conversations": 5,
    "messages": 23,
    "files": 0
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### GET /api/health/ai
AI服务健康检查

**响应示例:**
```json
{
  "status": "healthy",
  "service": "qianwen",
  "provider": "阿里云通义千问",
  "features": [
    "text_generation",
    "image_understanding",
    "streaming_response"
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### GET /api/health/full
综合健康检查

**响应示例:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "0.1.0",
  "services": {
    "storage": {
      "status": "healthy",
      "type": "memory",
      "stats": {
        "user_count": 1,
        "conversation_count": 5,
        "message_count": 23,
        "file_count": 0
      }
    },
    "ai": {
      "status": "healthy",
      "provider": "qianwen",
      "features": ["text", "image", "streaming"]
    },
    "config": {
      "status": "healthy",
      "server_host": "127.0.0.1",
      "server_port": 8080,
      "environment": "development"
    }
  }
}
```

### 2. 聊天接口

#### POST /api/chat/text
统一文本聊天端点 - 支持流式和非流式响应

**请求体:**
```json
{
  "content": "你好，请介绍一下你自己",
  "conversation_id": "uuid-string",
  "stream": false
}
```

**参数说明:**
- `content` (string, 必需): 用户消息内容
- `conversation_id` (string, 可选): 对话ID，如果不提供将创建新对话
- `stream` (boolean, 可选): 是否使用流式响应，默认false

**非流式响应 (stream: false):**
```json
{
  "success": true,
  "data": {
    "user_message": {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "user_id": "uuid-string",
      "role": "User",
      "message_type": "Text",
      "content": "你好，请介绍一下你自己",
      "metadata": null,
      "file_id": null,
      "parent_message_id": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "file_info": null
    },
    "assistant_message": {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "user_id": "uuid-string",
      "role": "Assistant",
      "message_type": "Text",
      "content": "你好！我是AI助手，基于通义千问模型...",
      "metadata": null,
      "file_id": null,
      "parent_message_id": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "file_info": null
    },
    "conversation": {
      "id": "uuid-string",
      "user_id": "uuid-string",
      "title": "新对话",
      "description": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "is_archived": false,
      "message_count": 2,
      "last_message_at": "2024-01-01T00:00:00Z"
    }
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**流式响应 (stream: true):**
- Content-Type: `text/event-stream`
- Cache-Control: `no-cache`
- Connection: `keep-alive`

**SSE事件类型:**
- `user_message`: 用户消息确认
- `ai_chunk`: AI响应片段
- `ai_done`: AI响应完成
- `error`: 错误事件

**SSE事件示例:**
```
event: user_message
data: {"id": "uuid", "conversation_id": "uuid", "role": "User", "content": "你好", "created_at": "2024-01-01T00:00:00Z"}

event: ai_chunk
data: {"event_type": "chunk", "data": {"content": "你好", "delta": "你好"}}

event: ai_chunk
data: {"event_type": "chunk", "data": {"content": "你好！", "delta": "！"}}

event: ai_done
data: {"event_type": "done", "data": {"total_tokens": 15, "completion_tokens": 8}}
```

#### POST /api/chat/multimodal ✅ **已实现并可用**
统一多模态聊天端点 - 支持文本、图片和混合消息，使用OpenAI兼容格式

**请求体格式1 - 纯文本消息:**
```json
{
  "content": "你好，请介绍一下你自己",
  "conversation_id": "uuid-string",
  "stream": false
}
```

**请求体格式2 - 多模态消息（文本+图片）:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "请分析这张图片的内容"
    },
    {
      "type": "image_url",
      "image_url": {
        "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
      }
    }
  ],
  "conversation_id": "uuid-string",
  "stream": false
}
```

**参数说明:**
- `content` (string|array, 必需): 消息内容，可以是纯文本字符串或多模态内容数组
- `conversation_id` (string, 可选): 对话ID，如果不提供将创建新对话
- `stream` (boolean, 可选): 是否使用流式响应，默认false

**多模态内容数组元素:**
- `type` (string): 内容类型，支持 "text" 和 "image_url"
- `text` (string): 文本内容（当type为"text"时）
- `image_url.url` (string): 图片URL地址（当type为"image_url"时）

**OpenAI兼容格式说明:**
- 使用标准的OpenAI消息格式进行API调用
- 自动模型选择：文本内容使用qwen-turbo，多模态内容使用qwen-vl-plus
- 支持流式和非流式响应模式
- 完全向后兼容现有的文本聊天功能

**非流式响应示例:**
```json
{
  "success": true,
  "data": {
    "user_message": {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "user_id": "uuid-string",
      "role": "User",
      "message_type": "Text",
      "content": "请分析这张图片的内容",
      "metadata": {
        "multimodal": true,
        "has_image": true,
        "image_urls": ["https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"]
      },
      "created_at": "2024-01-01T00:00:00Z"
    },
    "assistant_message": {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "user_id": "uuid-string",
      "role": "Assistant",
      "message_type": "Text",
      "content": "这张图片显示了一个小女孩和一只狗在一起...",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "conversation": {
      "id": "uuid-string",
      "title": "多模态对话",
      "message_count": 2
    },
    "usage": {
      "prompt_tokens": 15,
      "completion_tokens": 66,
      "total_tokens": 81
    }
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**Usage字段说明（OpenAI兼容格式）:**
- `prompt_tokens`: 输入token数量
- `completion_tokens`: 生成token数量
- `total_tokens`: 总token数量
- `prompt_tokens_details`: 可选的详细信息（如缓存token数量）

**流式响应 (stream: true):**
- Content-Type: `text/event-stream`
- Cache-Control: `no-cache`
- Connection: `keep-alive`
- 支持多模态内容的实时流式响应

**流式响应事件示例:**
```
event: user_message
data: {"id": "uuid", "conversation_id": "uuid", "role": "User", "content": "请分析这张图片", "metadata": {"has_image": true}}

event: ai_chunk
data: {"event_type": "chunk", "data": {"content": "这张", "delta": "这张"}}

event: ai_chunk
data: {"event_type": "chunk", "data": {"content": "这张图片", "delta": "图片"}}

event: ai_done
data: {"event_type": "done", "data": {"prompt_tokens": 15, "completion_tokens": 66, "total_tokens": 81}}
```

**流式响应特性:**
- 真实流式：优先使用通义千问流式API
- 模拟流式：API不可用时自动降级到模拟流式响应
- 智能分块：按标点符号和语义边界进行分块
- 错误恢复：网络中断时自动重试和恢复

#### POST /api/chat/image
发送图像消息 - 简化的图片分析接口（向后兼容）

**请求体:**
```json
{
  "image_url": "https://example.com/image.jpg",
  "prompt": "请分析这张图片",
  "conversation_id": "uuid-string"
}
```

**参数说明:**
- `image_url` (string, 必需): 图片URL地址
- `prompt` (string, 可选): 图片分析提示，默认为"请分析这张图片"
- `conversation_id` (string, 可选): 对话ID，如果不提供将创建新对话

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user_message": {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "role": "User",
      "message_type": "Image",
      "content": "请分析这张图片",
      "metadata": {
        "image_url": "https://example.com/image.jpg",
        "prompt": "请分析这张图片"
      },
      "created_at": "2024-01-01T00:00:00Z"
    },
    "assistant_message": {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "role": "Assistant",
      "message_type": "Text",
      "content": "这张图片显示了...",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "conversation": {
      "id": "uuid-string",
      "title": "图片分析对话",
      "message_count": 2
    }
  }
}
```

#### POST /api/chat/debug/stream
调试流式聊天端点 - 用于测试和调试SSE解析

**请求体:**
```json
{
  "content": "测试消息",
  "conversation_id": "uuid-string"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "conversation_id": "uuid-string",
    "user_id": "uuid-string",
    "request_content": "测试消息",
    "events_count": 5,
    "events": [
      {
        "type": "success",
        "event": {
          "event_type": "chunk",
          "data": {"content": "测试", "delta": "测试"}
        }
      }
    ],
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 对话管理

#### GET /api/conversations
获取对话列表

**查询参数:**
- `page` (int, 可选): 页码，默认1
- `per_page` (int, 可选): 每页数量，默认20，最大100
- `archived` (boolean, 可选): 是否包含已归档对话

**请求示例:**
```
GET /api/conversations?page=1&per_page=20&archived=false
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid-string",
      "user_id": "uuid-string",
      "title": "关于AI的讨论",
      "description": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "is_archived": false,
      "message_count": 15,
      "last_message_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": "uuid-string-2",
      "user_id": "uuid-string",
      "title": "技术问题咨询",
      "description": "关于编程的问题",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "is_archived": false,
      "message_count": 8,
      "last_message_at": "2024-01-01T00:00:00Z"
    }
  ],
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### GET /api/conversations/{id}/messages
获取对话消息列表

**路径参数:**
- `id` (string): 对话ID

**查询参数:**
- `page` (int, 可选): 页码，默认1
- `per_page` (int, 可选): 每页数量，默认50，最大100
- `message_type` (string, 可选): 消息类型过滤 ("Text", "Image", "Audio", "File")

**请求示例:**
```
GET /api/conversations/uuid-string/messages?page=1&per_page=50&message_type=Text
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid-string",
      "conversation_id": "uuid-string",
      "user_id": "uuid-string",
      "role": "User",
      "message_type": "Text",
      "content": "你好，请介绍一下你自己",
      "metadata": null,
      "file_id": null,
      "parent_message_id": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "file_info": null
    },
    {
      "id": "uuid-string-2",
      "conversation_id": "uuid-string",
      "user_id": "uuid-string",
      "role": "Assistant",
      "message_type": "Text",
      "content": "你好！我是AI助手，基于通义千问模型...",
      "metadata": null,
      "file_id": null,
      "parent_message_id": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "file_info": null
    }
  ],
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 4. 数据模型

#### 消息角色 (MessageRole)
- `User`: 用户消息
- `Assistant`: AI助手消息
- `System`: 系统消息

#### 消息类型 (MessageType)
- `Text`: 文本消息
- `Image`: 图片消息
- `Audio`: 音频消息
- `File`: 文件消息

#### 对话状态
- `is_archived`: false (活跃对话) / true (已归档对话)

### 5. 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `VALIDATION_ERROR` | 400 | 请求参数验证失败 |
| `MULTIMODAL_FORMAT_ERROR` | 400 | 多模态消息格式错误 |
| `INVALID_CONTENT_TYPE` | 400 | 无效的内容类型 |
| `MISSING_REQUIRED_FIELD` | 400 | 缺少必需字段 |
| `IMAGE_URL_INVALID` | 400 | 图片URL格式无效 |
| `USER_NOT_FOUND` | 404 | 用户不存在 |
| `CONVERSATION_NOT_FOUND` | 404 | 对话不存在 |
| `MESSAGE_NOT_FOUND` | 404 | 消息不存在 |
| `FILE_NOT_FOUND` | 404 | 文件不存在 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `AI_SERVICE_ERROR` | 502 | AI服务错误 |
| `MULTIMODAL_API_ERROR` | 502 | 多模态API调用失败 |
| `IMAGE_DOWNLOAD_ERROR` | 502 | 图片下载失败 |
| `VISION_MODEL_ERROR` | 502 | 视觉模型处理错误 |
| `STORAGE_ERROR` | 500 | 存储服务错误 |
| `CONFIG_ERROR` | 500 | 配置错误 |
| `NETWORK_ERROR` | 502 | 网络连接错误 |
| `MCP_CONNECTION_ERROR` | 502 | MCP服务器连接失败 |
| `MCP_PROTOCOL_ERROR` | 502 | MCP协议错误 |
| `AMAP_MCP_ERROR` | 502 | 高德地图MCP服务错误 |
| `LOCATION_SERVICE_ERROR` | 502 | 位置服务错误 |

#### 多模态相关错误示例

**格式错误 (400):**
```json
{
  "success": false,
  "error": {
    "code": "MULTIMODAL_FORMAT_ERROR",
    "message": "多模态消息格式错误：content数组中的元素必须包含type字段",
    "details": {
      "field": "content[0].type",
      "expected": "text 或 image_url",
      "received": "undefined"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**图片URL无效 (400):**
```json
{
  "success": false,
  "error": {
    "code": "IMAGE_URL_INVALID",
    "message": "图片URL格式无效或无法访问",
    "details": {
      "url": "https://invalid-domain.com/image.jpg",
      "reason": "域名解析失败"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**AI服务错误 (502):**
```json
{
  "success": false,
  "error": {
    "code": "VISION_MODEL_ERROR",
    "message": "视觉模型处理失败",
    "details": {
      "model": "qwen-vl-plus",
      "reason": "图片格式不支持或内容违规"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**MCP服务错误 (502):**
```json
{
  "success": false,
  "error": {
    "code": "AMAP_MCP_ERROR",
    "message": "高德地图MCP服务连接失败",
    "details": {
      "mcp_server": "https://mcp.amap.com/sse",
      "reason": "SSE连接超时",
      "retry_after": 30
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**位置服务错误 (502):**
```json
{
  "success": false,
  "error": {
    "code": "LOCATION_SERVICE_ERROR",
    "message": "位置服务暂时不可用",
    "details": {
      "service": "amap_mcp",
      "error_type": "rate_limit_exceeded",
      "suggestion": "请稍后重试或使用传统地图API"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 6. 限制说明

#### 通用限制
- **消息内容长度**: 最大10,000字符
- **对话标题长度**: 最大100字符
- **API请求频率**: 每分钟100次（开发阶段无限制）
- **并发连接数**: 最大1000个SSE连接

#### 多模态功能限制
- **多模态内容数组**: 最大10个元素
- **单条消息中的图片数量**: 最大5张
- **图片URL**: 必须是可访问的HTTP/HTTPS地址
- **支持的图片格式**: JPG, JPEG, PNG, GIF, WebP, BMP
- **图片大小限制**: 最大10MB
- **图片分辨率**: 最大4096x4096像素
- **图片URL长度**: 最大2048字符
- **文本段落数量**: 每条多模态消息最大10个文本段落

#### 模型限制
- **文本模型**: qwen-turbo（纯文本聊天）
- **视觉模型**: qwen-vl-plus（多模态聊天）
- **上下文长度**: 最大8192个token
- **响应长度**: 最大2048个token
- **图片理解能力**: 支持物体识别、场景理解、文字识别、情感分析

#### MCP服务限制
- **连接数限制**: 每个API密钥最大100个并发SSE连接
- **数据传输速率**: 最大1MB/s的实时数据流
- **地理范围**: 主要支持中国大陆地区的详细地图数据
- **API调用频率**: 每分钟最大1000次MCP请求
- **上下文窗口**: MCP上下文最大8192个token
- **实时数据延迟**: 通常在1-3秒内更新

#### 性能建议
- **图片优化**: 建议使用压缩后的图片以提高响应速度
- **批量请求**: 避免在短时间内发送大量多模态请求
- **流式响应**: 对于长文本生成建议使用流式模式
- **错误重试**: 网络错误时建议使用指数退避重试策略
- **MCP连接管理**: 合理管理SSE连接，避免频繁建立/断开连接
- **地图数据缓存**: 对常用POI和路线数据进行本地缓存

### 7. 开发说明

当前版本为开发版本，具有以下特点：

#### 开发模式特性
- ✅ 使用内存存储，重启后数据丢失
- ✅ 暂时跳过用户认证，使用默认用户
- ✅ 调试端点已启用
- ✅ 详细的错误日志和调试信息
- ✅ CORS已配置允许跨域请求

#### 已实现功能
- ✅ 统一文本聊天端点（支持流式和非流式）
- ✅ **多模态聊天功能（文本+图片）- 已完全实现并测试通过**
- ✅ **统一多模态API端点 (/api/chat/multimodal) - 生产就绪**
- ✅ **OpenAI兼容的多模态消息格式 - 标准格式支持**
- ✅ **自动模型选择（qwen-turbo文本/qwen-vl-plus视觉）**
- ✅ **多模态流式响应支持 - 真实+模拟流式**
- ✅ **Usage字段OpenAI兼容格式 - prompt_tokens/completion_tokens**
- ✅ **智能内容解析 - 自动识别文本/多模态格式**
- ✅ **错误恢复机制 - 流式API失败时自动降级**
- ✅ 图像理解和分析（向后兼容接口）
- ✅ Server-Sent Events流式响应
- ✅ 对话管理（列表、消息历史）
- ✅ 健康检查和监控端点
- ✅ 完整的错误处理和响应格式
- ✅ 通义千问API集成（qwen-turbo + qwen-vl-plus）
- ✅ **向后兼容性保证 - 所有旧端点仍可用**
- ✅ **Cargo默认运行配置 - cargo run直接启动**
- 🆕 **高德地图MCP服务集成 - 实时地图数据和AI增强功能**
- 🆕 **智能旅行指南 - AI结合地图数据的个性化推荐**
- 🆕 **多模态地图理解 - 图片+文本的位置识别和分析**

#### 未来功能（生产版本）
- 🔄 数据库持久化存储（SQLite/PostgreSQL）
- 🔄 完整的用户认证和授权系统
- 🔄 文件上传和管理
- 🔄 语音转文字和文字转语音
- 🔄 对话CRUD操作（创建、更新、删除）
- 🔄 消息搜索和过滤
- 🔄 API速率限制和安全措施
- 🔄 缓存和性能优化

### 8. 测试建议

#### 使用curl测试API

**健康检查:**
```bash
# 基础健康检查
curl -s http://127.0.0.1:8080/health

# AI服务健康检查
curl -s http://127.0.0.1:8080/api/health/ai

# 完整健康检查
curl -s http://127.0.0.1:8080/api/health/full

# MCP服务健康检查（新增）
curl -s http://127.0.0.1:8080/api/health/mcp
```

**获取对话列表:**
```bash
# 获取所有对话
curl -s http://127.0.0.1:8080/api/conversations

# 分页获取对话
curl -s "http://127.0.0.1:8080/api/conversations?page=1&per_page=10"
```

**传统文本聊天测试:**
```bash
# 发送文本消息（非流式）
curl -X POST http://127.0.0.1:8080/api/chat/text \
  -H "Content-Type: application/json" \
  -d '{
    "content": "你好，请介绍一下你自己",
    "stream": false
  }'

# 发送文本消息（流式）- 注意观察SSE事件流
curl -X POST http://127.0.0.1:8080/api/chat/text \
  -H "Content-Type: application/json" \
  -d '{
    "content": "请写一首关于春天的诗",
    "stream": true
  }'
```

**多模态聊天测试（✅ 已验证可用）:**
```bash
# 1. 发送纯文本消息（使用多模态端点）
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "你好，我想了解一下多模态AI的能力",
    "stream": false
  }' \
  -w "\nHTTP Status: %{http_code}\n"

# 2. 发送多模态消息（文本+图片，非流式）
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请详细分析这张图片的内容，包括人物、动物、环境等"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ],
    "stream": false
  }' \
  -w "\nHTTP Status: %{http_code}\n"

# 3. 发送多模态消息（文本+图片，流式响应）
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请分析这张图片并告诉我其中的故事"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ],
    "stream": true
  }'

# 4. 在现有对话中发送多模态消息
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "这是另一张图片，请比较一下与之前图片的异同"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://example.com/another-image.jpg"
        }
      }
    ],
    "conversation_id": "your-conversation-id-here",
    "stream": false
  }' \
  -w "\nHTTP Status: %{http_code}\n"

# 5. 快速测试（最简单的文本消息）
curl -X POST 'http://127.0.0.1:8080/api/chat/multimodal' \
  -H 'Content-Type: application/json' \
  -d '{"content": "你是谁？", "stream": false}' \
  -w "\nHTTP Status: %{http_code}\n"
```

**传统图像分析测试（向后兼容）:**
```bash
# 使用简化的图像分析接口
curl -X POST http://127.0.0.1:8080/api/chat/image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg",
    "prompt": "请分析这张图片中的人物和动物"
  }'
```

**复杂多模态对话测试:**
```bash
# 发送包含多个文本段落和图片的复杂消息
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "我想让你帮我分析几张图片。首先看这张："
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      },
      {
        "type": "text",
        "text": "请告诉我这张图片的主要内容，并分析其中的情感表达。"
      }
    ],
    "stream": false
  }'
```

#### 多模态功能专项测试

**测试多模态消息格式验证:**
```bash
# 测试无效的内容格式（应返回400错误）
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "invalid_type",
        "text": "这是无效的内容类型"
      }
    ]
  }'

# 测试缺少必需字段（应返回400错误）
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text"
        // 缺少text字段
      }
    ]
  }'

# 测试无效的图片URL（应返回502错误）
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请分析这张图片"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://invalid-url.com/nonexistent.jpg"
        }
      }
    ]
  }'
```

**测试API兼容性:**
```bash
# 测试向后兼容性 - 确保旧的文本端点仍然工作
curl -X POST http://127.0.0.1:8080/api/chat/text \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这是使用旧端点的测试消息"
  }'

# 测试新旧端点的响应格式一致性
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这是使用新端点发送的纯文本消息"
  }'
```

**性能和流式响应测试:**
```bash
# 测试大型多模态消息的处理
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请详细分析以下图片，包括构图、色彩、主题、情感表达、艺术风格等各个方面，并提供专业的艺术评价。"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ],
    "stream": true
  }'

# 测试流式响应的中断和恢复
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": "请写一篇1000字的文章",
    "stream": true
  }' | head -20  # 只读取前20行然后中断
```

#### 使用测试脚本

项目包含测试脚本 `backend/test_endpoints.sh`，可以自动测试所有主要端点：

```bash
cd backend
chmod +x test_endpoints.sh
./test_endpoints.sh
```

**创建多模态测试脚本:**
```bash
# 创建专门的多模态测试脚本
cat > test_multimodal.sh << 'EOF'
#!/bin/bash

echo "🧪 开始多模态聊天功能测试"

BASE_URL="http://127.0.0.1:8080"

# 测试1: 纯文本消息
echo "📝 测试1: 发送纯文本消息"
curl -X POST $BASE_URL/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": "你好，我想测试多模态功能"
  }' | jq .

# 测试2: 多模态消息（文本+图片）
echo "🖼️ 测试2: 发送多模态消息"
curl -X POST $BASE_URL/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请分析这张图片"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ]
  }' | jq .

# 测试3: 流式多模态响应
echo "🌊 测试3: 流式多模态响应"
curl -X POST $BASE_URL/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "请详细描述这张图片的内容"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
        }
      }
    ],
    "stream": true
  }'

echo "✅ 多模态测试完成"
EOF

chmod +x test_multimodal.sh
./test_multimodal.sh
```

### 9. 高德地图MCP服务集成 🗺️ **新增功能**

#### MCP服务器配置

本应用集成了高德地图MCP (Model Context Protocol) 服务器，为AI旅行指南提供实时地图数据和智能位置服务。

**MCP配置信息:**
```json
{
    "mcpServers": {
        "amap-amap-sse":
        {
        "url":"https://mcp.amap.com/sse?key=039c47c81bc9eb2c1e38df53260191e0"
        }
     }
}
```

#### MCP服务特性

**🌟 核心功能:**
- **实时地图数据流** - 通过SSE提供实时位置更新
- **AI原生集成** - 直接与通义千问AI模型集成
- **智能位置推理** - AI可基于地图数据进行智能推荐
- **标准化协议** - 遵循MCP标准，便于扩展

**🎯 旅行指南增强:**
- **智能路线规划** - AI结合实时交通数据规划最优路线
- **POI智能推荐** - 基于用户偏好和位置的个性化景点推荐
- **实时位置服务** - 动态更新旅行建议和导航信息
- **多模态地图理解** - 结合图片和文本进行地理位置分析

#### 与现有高德API的关系

**协同工作模式:**
- **传统API** - 用于基础地图功能（地理编码、POI搜索、路线规划）
- **MCP服务** - 用于AI增强功能（智能推荐、实时数据流、上下文理解）

**数据流架构:**
```
用户请求 → AI聊天接口 → 通义千问 ↔ 高德MCP服务 → 实时地图数据
                    ↓
              传统高德API → 基础地图功能
```

#### AI旅行指南应用场景

**1. 智能行程规划**
```bash
# 示例：AI结合MCP数据规划行程
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": "我想在北京游玩3天，喜欢历史文化和美食，请帮我规划详细行程",
    "stream": true
  }'
```

**2. 实时位置推荐**
```bash
# 示例：基于当前位置的智能推荐
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": "我现在在天安门广场，附近有什么值得参观的地方？考虑步行距离",
    "stream": true
  }'
```

**3. 多模态地图分析**
```bash
# 示例：结合图片进行位置识别和推荐
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {
        "type": "text",
        "text": "这是我拍的照片，请识别位置并推荐附近的景点"
      },
      {
        "type": "image_url",
        "image_url": {
          "url": "https://example.com/location-photo.jpg"
        }
      }
    ],
    "stream": true
  }'
```

#### MCP服务配置要求

**环境配置:**
```toml
[amap]
# 高德地图API配置（传统API）
api_key = "039c47c81bc9eb2c1e38df53260191e0"
base_url = "https://restapi.amap.com"

# MCP服务配置（新增）
mcp_enabled = true
mcp_server_url = "https://mcp.amap.com/sse"
mcp_api_key = "039c47c81bc9eb2c1e38df53260191e0"  # 与传统API使用相同密钥
```

**AI模型配置增强:**
```toml
[qianwen]
# 通义千问配置
api_key = "your_qianwen_api_key"
base_url = "https://dashscope.aliyuncs.com"
model_text = "qwen-plus"
model_vision = "qwen-vl-plus"

# MCP集成配置（新增）
enable_mcp_integration = true
mcp_context_window = 8192  # MCP上下文窗口大小
```

#### 技术实现优势

**1. 性能优化**
- **实时数据流** - SSE连接减少API调用延迟
- **智能缓存** - MCP服务器端缓存热点数据
- **并发处理** - 支持多用户同时访问地图服务

**2. 功能增强**
- **上下文感知** - AI理解地理位置上下文
- **个性化推荐** - 基于用户历史和偏好
- **多语言支持** - 支持中英文地名和描述

**3. 扩展性**
- **标准协议** - 易于集成其他MCP服务
- **模块化设计** - 可独立启用/禁用MCP功能
- **向后兼容** - 不影响现有API功能

### 10. 多模态功能使用指南

#### 快速开始

**第一步：发送纯文本消息**
```bash
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{"content": "你好，我想了解多模态AI"}'
```

**第二步：发送图片分析请求**
```bash
curl -X POST http://127.0.0.1:8080/api/chat/multimodal \
  -H "Content-Type: application/json" \
  -d '{
    "content": [
      {"type": "text", "text": "请分析这张图片"},
      {"type": "image_url", "image_url": {"url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"}}
    ]
  }'
```

#### 最佳实践

**1. 消息格式选择**
- 纯文本消息：使用字符串格式 `"content": "文本内容"`
- 多模态消息：使用数组格式 `"content": [...]`
- 混合内容：在数组中组合文本和图片元素

**2. 图片URL要求**
- 使用HTTPS协议确保安全性
- 确保图片URL可公开访问
- 推荐使用CDN加速图片加载
- 图片大小控制在5MB以内以获得最佳性能

**3. 提示词优化**
- 明确描述分析需求："请分析图片中的人物表情"
- 指定输出格式："请用列表形式描述图片内容"
- 提供上下文信息："这是一张产品照片，请分析其商业价值"

**4. 错误处理**
- 检查HTTP状态码判断请求是否成功
- 解析错误响应中的详细信息
- 对网络错误实施重试机制
- 验证图片URL的可访问性

#### 常见问题解答

**Q: 如何在现有对话中发送多模态消息？**
A: 在请求中包含 `conversation_id` 参数：
```json
{
  "content": [...],
  "conversation_id": "your-conversation-id"
}
```

**Q: 支持哪些图片格式？**
A: 支持 JPG, JPEG, PNG, GIF, WebP, BMP 格式，推荐使用 JPG 或 PNG。

**Q: 如何处理大尺寸图片？**
A: 系统会自动处理图片缩放，但建议预先将图片压缩到合理尺寸以提高响应速度。

**Q: 多模态消息是否支持流式响应？**
A: 是的，设置 `"stream": true` 即可启用流式响应。

**Q: 如何确保向后兼容性？**
A: 旧的 `/api/chat/text` 和 `/api/chat/image` 端点仍然可用，新的 `/api/chat/multimodal` 端点完全向后兼容。

**Q: 单条消息可以包含多张图片吗？**
A: 可以，最多支持5张图片，每张图片作为一个独立的 `image_url` 元素。

#### 高级用例

**多图片对比分析:**
```json
{
  "content": [
    {"type": "text", "text": "请比较以下两张图片的异同："},
    {"type": "image_url", "image_url": {"url": "https://example.com/image1.jpg"}},
    {"type": "text", "text": "第一张图片"},
    {"type": "image_url", "image_url": {"url": "https://example.com/image2.jpg"}},
    {"type": "text", "text": "第二张图片，请详细分析它们的差异。"}
  ]
}
```

**图文混合创作:**
```json
{
  "content": [
    {"type": "text", "text": "基于这张图片"},
    {"type": "image_url", "image_url": {"url": "https://example.com/inspiration.jpg"}},
    {"type": "text", "text": "请创作一首诗歌，要求体现图片中的意境和情感。"}
  ]
}
```

**技术文档生成:**
```json
{
  "content": [
    {"type": "text", "text": "请为这个界面截图"},
    {"type": "image_url", "image_url": {"url": "https://example.com/ui-screenshot.png"}},
    {"type": "text", "text": "编写详细的用户操作指南，包括每个按钮的功能说明。"}
  ]
}
```
```
