# MCP协议标准实现文档

## 概述

本文档描述了AI聊天后端中改进后的Model Context Protocol (MCP)实现，该实现严格遵循MCP协议标准和JSON-RPC 2.0规范。

## 架构设计

### 核心组件

1. **McpClient** - MCP客户端实现
2. **JSON-RPC 2.0消息结构** - 标准协议消息格式
3. **连接管理** - 连接生命周期管理
4. **错误处理** - 标准错误代码和处理机制
5. **资源管理** - MCP资源的列举和读取

### 协议合规性

- ✅ **JSON-RPC 2.0标准** - 完全符合JSON-RPC 2.0规范
- ✅ **MCP协议版本** - 支持MCP协议版本2025-06-18
- ✅ **标准错误代码** - 使用标准JSON-RPC错误代码
- ✅ **能力协商** - 支持客户端/服务器能力协商
- ✅ **资源协议** - 实现标准资源列举和读取

## 数据结构

### JSON-RPC 2.0消息格式

```rust
// 请求消息
pub struct JsonRpcRequest {
    pub jsonrpc: String,        // "2.0"
    pub method: String,         // 方法名
    pub params: Option<Value>,  // 参数
    pub id: Value,             // 请求ID
}

// 响应消息
pub struct JsonRpcResponse {
    pub jsonrpc: String,        // "2.0"
    pub id: Value,             // 请求ID
    pub result: Option<Value>,  // 结果
    pub error: Option<JsonRpcError>, // 错误
}
```

### MCP初始化流程

```rust
// 1. 初始化请求
{
    "jsonrpc": "2.0",
    "method": "initialize",
    "params": {
        "protocol_version": "2025-06-18",
        "client_info": {
            "name": "ai-chat-backend",
            "version": "1.0.0"
        },
        "capabilities": {
            "roots": { "list_changed": true },
            "sampling": {}
        }
    },
    "id": "uuid"
}

// 2. 初始化响应
{
    "jsonrpc": "2.0",
    "id": "uuid",
    "result": {
        "protocol_version": "2025-06-18",
        "server_info": {
            "name": "amap-mcp-server",
            "version": "1.0.0"
        },
        "capabilities": {
            "resources": { "subscribe": true },
            "tools": { "list_changed": true }
        }
    }
}

// 3. 初始化完成通知
{
    "jsonrpc": "2.0",
    "method": "notifications/initialized",
    "params": {}
}
```

## 核心功能

### 1. 连接管理

```rust
impl McpClient {
    // 初始化连接
    pub async fn initialize(&mut self) -> AppResult<McpInitializeResult>
    
    // 检查连接状态
    pub fn is_connected(&self) -> bool
    
    // 关闭连接
    pub async fn close(&mut self) -> AppResult<()>
}
```

### 2. 资源操作

```rust
// 列出资源
pub async fn list_resources(&self, cursor: Option<String>) -> AppResult<McpListResourcesResult>

// 读取资源
pub async fn read_map_resource(&self, resource_uri: &str) -> AppResult<AmapMapResult>
```

### 3. 错误处理

标准MCP错误代码：

```rust
pub mod mcp_error_codes {
    pub const PARSE_ERROR: i32 = -32700;           // 解析错误
    pub const INVALID_REQUEST: i32 = -32600;       // 无效请求
    pub const METHOD_NOT_FOUND: i32 = -32601;      // 方法未找到
    pub const INVALID_PARAMS: i32 = -32602;        // 无效参数
    pub const INTERNAL_ERROR: i32 = -32603;        // 内部错误
    
    // MCP特定错误
    pub const RESOURCE_NOT_FOUND: i32 = -32001;    // 资源未找到
    pub const TOOL_EXECUTION_ERROR: i32 = -32002;  // 工具执行失败
    pub const CONNECTION_TIMEOUT: i32 = -32003;    // 连接超时
    pub const AUTHENTICATION_FAILED: i32 = -32004; // 认证失败
}
```

## 集成流程

### 1. 服务初始化

```rust
// 创建带MCP支持的服务
let mut service = QianwenService::new_with_amap(qianwen_config, amap_config)?;

// 初始化MCP连接
service.initialize_mcp().await?;
```

### 2. 智能触发机制

```rust
// 检测旅行相关关键词
fn detect_travel_keywords(&self, content: &str) -> bool {
    let keywords = [
        "景点", "旅游", "游玩", "路线", "导航",
        "餐厅", "酒店", "购物", "交通", "天气"
    ];
    keywords.iter().any(|&keyword| content.contains(keyword))
}
```

### 3. 数据增强

```rust
// 构建包含地图数据的AI上下文
async fn build_context_with_map_data(&self, messages: Vec<ChatMessage>) -> AppResult<Vec<ChatMessage>> {
    // 1. 检测旅行关键词
    // 2. 获取MCP地图数据
    // 3. 格式化为AI可理解的文本
    // 4. 插入系统消息
}
```

## 配置说明

### MCP相关配置

```toml
[qianwen]
enable_mcp_integration = true      # 启用MCP集成
mcp_context_window = 8192         # MCP上下文窗口大小
mcp_fallback_enabled = true       # 启用降级模式

[amap]
mcp_enabled = true                # 启用高德地图MCP
mcp_server_url = "https://mcp.amap.com/api"  # MCP服务器URL
mcp_api_key = "your-api-key"      # MCP API密钥
mcp_max_connections = 100         # 最大连接数
mcp_timeout_seconds = 30          # 超时时间
mcp_retry_attempts = 3            # 重试次数
mcp_enable_caching = true         # 启用缓存
mcp_cache_ttl_seconds = 300       # 缓存TTL
```

## 测试验证

### 运行协议测试

```bash
# 运行MCP协议标准测试
cargo run --example mcp_protocol_test

# 运行原有集成测试
cargo run --example mcp_integration_test
```

### 测试覆盖

- ✅ JSON-RPC 2.0协议合规性
- ✅ MCP连接生命周期
- ✅ 旅行关键词检测
- ✅ 流式和非流式响应
- ✅ 错误处理和降级
- ✅ 资源管理和清理

## 性能优化

### 1. 连接池管理

- 支持最大连接数限制
- 连接复用和生命周期管理
- 自动重连机制

### 2. 缓存策略

- 资源结果缓存
- TTL过期管理
- 内存使用优化

### 3. 错误恢复

- 自动重试机制
- 降级模式支持
- 优雅错误处理

## 最佳实践

1. **连接管理** - 在应用启动时初始化MCP连接
2. **错误处理** - 始终启用降级模式确保服务可用性
3. **资源清理** - 在应用关闭时正确关闭MCP连接
4. **监控日志** - 启用详细日志监控MCP操作状态
5. **性能调优** - 根据实际负载调整连接数和超时参数

## 故障排除

### 常见问题

1. **连接初始化失败**
   - 检查MCP服务器URL和API密钥
   - 验证网络连接和防火墙设置
   - 查看详细错误日志

2. **资源读取失败**
   - 验证资源URI格式
   - 检查权限和配额限制
   - 确认MCP服务器支持的资源类型

3. **性能问题**
   - 调整连接池大小
   - 优化缓存策略
   - 监控网络延迟

## 未来扩展

1. **多MCP服务器支持** - 支持连接多个MCP服务器
2. **高级缓存策略** - 实现分布式缓存
3. **监控和指标** - 添加详细的性能监控
4. **插件化架构** - 支持动态加载MCP插件
