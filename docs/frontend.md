# 前端开发指南

## 项目结构

```
frontend/
├── lib/
│   ├── main.dart              # 应用入口
│   ├── models/               # 数据模型
│   │   ├── user.dart         # 用户模型
│   │   ├── message.dart      # 消息模型
│   │   └── conversation.dart # 对话模型
│   ├── services/             # 服务层
│   │   ├── api_service.dart  # API客户端
│   │   ├── auth_service.dart # 认证服务
│   │   ├── chat_service.dart # 聊天服务
│   │   └── storage_service.dart # 本地存储
│   ├── providers/            # 状态管理
│   │   ├── auth_provider.dart # 认证状态
│   │   ├── chat_provider.dart # 聊天状态
│   │   └── theme_provider.dart # 主题状态
│   ├── screens/              # 页面
│   │   ├── auth/             # 认证页面
│   │   ├── chat/             # 聊天页面
│   │   └── settings/         # 设置页面
│   ├── widgets/              # 组件
│   │   ├── common/           # 通用组件
│   │   ├── chat/             # 聊天组件
│   │   └── auth/             # 认证组件
│   ├── utils/                # 工具函数
│   │   ├── constants.dart    # 常量定义
│   │   ├── helpers.dart      # 辅助函数
│   │   └── validators.dart   # 验证函数
│   └── config/               # 配置
│       ├── routes.dart       # 路由配置
│       └── theme.dart        # 主题配置
├── assets/                   # 资源文件
│   ├── images/              # 图片资源
│   └── icons/               # 图标资源
└── pubspec.yaml             # 依赖配置
```

## 开发环境设置

### 1. 安装依赖
```bash
cd frontend
flutter pub get
```

### 2. 代码生成
```bash
flutter packages pub run build_runner build
```

### 3. 运行应用
```bash
# 调试模式
flutter run

# 发布模式
flutter run --release

# 指定设备
flutter run -d chrome  # Web
flutter run -d macos   # macOS
```

## 核心功能

### 1. 状态管理 (Provider)
```dart
class ChatProvider extends ChangeNotifier {
  List<Message> _messages = [];
  bool _isLoading = false;

  List<Message> get messages => _messages;
  bool get isLoading => _isLoading;

  Future<void> sendMessage(String text) async {
    _isLoading = true;
    notifyListeners();
    
    // 发送消息逻辑
    
    _isLoading = false;
    notifyListeners();
  }
}
```

### 2. HTTP客户端
```dart
class ApiService {
  static const String baseUrl = 'http://localhost:8080/api';
  final Dio _dio = Dio();

  Future<Response> post(String endpoint, Map<String, dynamic> data) async {
    try {
      return await _dio.post('$baseUrl$endpoint', data: data);
    } catch (e) {
      throw ApiException('网络请求失败: $e');
    }
  }
}
```

### 3. 路由管理 (GoRouter)
```dart
final GoRouter router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/chat',
      builder: (context, state) => const ChatScreen(),
    ),
  ],
);
```

## UI组件

### 1. 聊天界面
- 消息列表显示
- 输入框和发送按钮
- 语音录制按钮
- 图片选择按钮
- 加载状态指示器

### 2. 消息气泡
- 用户消息（右侧）
- AI回复（左侧）
- 图片消息显示
- 语音消息播放

### 3. 文件选择器
- 图片选择
- 音频录制
- 文件预览

## 权限管理

### Android权限
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

### iOS权限
```xml
<!-- ios/Runner/Info.plist -->
<key>NSMicrophoneUsageDescription</key>
<string>需要麦克风权限进行语音输入</string>
<key>NSCameraUsageDescription</key>
<string>需要相机权限拍摄照片</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>需要相册权限选择图片</string>
```

## 本地化

### 1. 配置国际化
```dart
import 'package:flutter_localizations/flutter_localizations.dart';

MaterialApp(
  localizationsDelegates: [
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: [
    Locale('zh', 'CN'),
    Locale('en', 'US'),
  ],
)
```

### 2. 多语言文本
```dart
class AppLocalizations {
  static const Map<String, String> zhCN = {
    'app_title': 'AI聊天助手',
    'send_message': '发送消息',
    'voice_input': '语音输入',
  };
}
```

## 测试

### 单元测试
```bash
flutter test
```

### 集成测试
```bash
flutter drive --target=test_driver/app.dart
```

### Widget测试
```dart
testWidgets('聊天界面测试', (WidgetTester tester) async {
  await tester.pumpWidget(MyApp());
  expect(find.text('发送'), findsOneWidget);
});
```
